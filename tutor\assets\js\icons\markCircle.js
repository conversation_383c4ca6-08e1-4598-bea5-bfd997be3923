"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[3662],{76585:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M7.99992 13.6668C11.1295 13.6668 13.6666 11.1298 13.6666 8.00016C13.6666 4.87055 11.1295 2.3335 7.99992 2.3335C4.87031 2.3335 2.33325 4.87055 2.33325 8.00016C2.33325 11.1298 4.87031 13.6668 7.99992 13.6668ZM5.1887 8.28534C5.20349 8.32108 5.22483 8.35372 5.25164 8.3816L7.25948 10.382C7.28598 10.4129 7.31881 10.4378 7.35574 10.4549C7.43478 10.4875 7.52352 10.4875 7.60256 10.4549C7.63949 10.4378 7.67232 10.4129 7.69881 10.382L11.2542 6.84517C11.281 6.8173 11.3023 6.78465 11.3171 6.74891C11.3466 6.66928 11.3466 6.58173 11.3171 6.5021C11.3026 6.46621 11.2813 6.43351 11.2542 6.40584L10.8075 5.97144C10.7813 5.94142 10.7491 5.9173 10.7129 5.9007C10.6768 5.88411 10.6375 5.87541 10.5977 5.87519C10.5553 5.87467 10.5132 5.88308 10.4743 5.89987C10.4367 5.91714 10.4028 5.94143 10.3743 5.97144L7.48162 8.86412L6.134 7.51404C6.10476 7.48385 6.06999 7.45955 6.03157 7.44247C5.99272 7.42538 5.95061 7.41696 5.90817 7.41778C5.869 7.41791 5.83031 7.42632 5.79463 7.44247C5.75757 7.45878 5.72466 7.48324 5.69837 7.51404L5.25164 7.94227C5.22457 7.96993 5.20319 8.00264 5.1887 8.03853C5.15921 8.11816 5.15921 8.20571 5.1887 8.28534Z" fill="currentColor"/>',viewBox:"0 0 16 16"}}}]);
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[7754],{8097:(l,e,a)=>{a.r(e);a.d(e,{default:()=>d});const d={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M11.167 4.214 5.97 9.41a.321.321 0 0 0 0 .454l5.197 5.197a.321.321 0 0 0 .455 0l5.197-5.197a.321.321 0 0 0 0-.454l-5.197-5.197a.321.321 0 0 0-.455 0ZM5.331 8.772a1.225 1.225 0 0 0 0 1.732l5.197 5.197a1.225 1.225 0 0 0 1.733 0l5.197-5.197a1.225 1.225 0 0 0 0-1.732L12.26 3.575a1.225 1.225 0 0 0-1.733 0L5.331 8.772Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.953 14.113c-.871-.871-.703-2.302.183-3.189L5.73 8.331a.452.452 0 0 1 .639.639l-2.594 2.593c-.623.623-.618 1.476-.183 1.911l2.74 2.74c.28.28.735.394 1.222.24l3.758-1.188a.452.452 0 0 1 .272.862l-3.757 1.187c-.764.242-1.583.089-2.134-.462l-2.74-2.74ZM17.492 12.82a.407.407 0 0 1 .43.38c.028.446.235 1.142.7 1.75.457.597 1.157 1.103 2.186 1.216a.407.407 0 0 1-.088.808c-1.278-.14-2.17-.78-2.743-1.53-.566-.739-.83-1.59-.866-2.194a.407.407 0 0 1 .38-.43Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.54 12.82a.407.407 0 0 0-.43.38c-.028.446-.235 1.142-.7 1.75-.458.597-1.158 1.103-2.187 1.216a.407.407 0 0 0 .089.808c1.278-.14 2.17-.78 2.743-1.53.566-.739.829-1.59.866-2.194a.407.407 0 0 0-.38-.43Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.54 20.32a.407.407 0 0 1-.43-.382c-.028-.445-.235-1.14-.7-1.75-.458-.596-1.158-1.103-2.187-1.215a.407.407 0 0 1 .089-.809c1.278.14 2.17.78 2.743 1.53.566.74.829 1.59.866 2.194a.407.407 0 0 1-.38.431Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.492 20.32a.407.407 0 0 0 .43-.382c.028-.445.235-1.14.7-1.75.457-.596 1.157-1.103 2.186-1.215a.407.407 0 0 0-.088-.809c-1.278.14-2.17.78-2.743 1.53-.566.74-.83 1.59-.866 2.194a.407.407 0 0 0 .38.431Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
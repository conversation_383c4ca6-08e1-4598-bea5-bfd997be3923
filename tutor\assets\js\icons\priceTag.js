"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[5632],{31679:(C,t,e)=>{e.r(t);e.d(t,{default:()=>o});const o={icon:'<path d="M20.0772 11.9193C18.8509 10.6934 19.1644 8.39245 20.7772 6.78004C22.3901 5.16764 24.6917 4.85432 25.9179 6.08022C27.1442 7.30613 26.8308 9.60704 25.2179 11.2194M11.8773 16.9776C11.1985 17.6562 11.1985 18.7563 11.8773 19.4349L12.7895 20.3469C13.4771 21.0343 14.5919 21.0343 15.2795 20.3469C15.9671 19.6595 15.9671 18.545 15.2795 17.8576L14.3986 16.9769C13.7025 16.281 13.7025 15.1528 14.3986 14.457C15.0946 13.7611 16.2231 13.7611 16.9192 14.457L17.7689 15.3064C18.4738 16.0111 18.4738 17.1536 17.7689 17.8583M18.1909 14.0559L17.3558 14.8908M12.4653 20.028L11.6302 20.8629M21.5069 9.27036L15.0754 8.38059C14.1839 8.25726 13.2861 8.55863 12.6497 9.19482L6.1746 15.6681C5.05248 16.7899 5.05248 18.6087 6.1746 19.7305L12.2699 25.824C13.392 26.9458 15.2113 26.9458 16.3335 25.824L22.8086 19.3508C23.445 18.7146 23.7464 17.817 23.6231 16.9258L22.733 10.4962C22.6449 9.85924 22.144 9.3585 21.5069 9.27036Z" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" fill="none"/>',viewBox:"0 0 32 32"}}}]);
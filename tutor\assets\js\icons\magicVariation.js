"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2745],{77548:(l,e,c)=>{c.r(e);c.d(e,{default:()=>a});const a={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M4.004 3.31H9.76c.612 0 1.107.496 1.107 1.108v5.757c0 .612-.495 1.107-1.107 1.107H4.004a1.107 1.107 0 0 1-1.108-1.107V4.418c0-.612.496-1.107 1.108-1.107Zm0 .886c-.123 0-.222.1-.222.222v5.757c0 .*************.222H9.76c.122 0 .221-.1.221-.222V4.418c0-.122-.099-.222-.22-.222H4.003ZM17.175 10.275a2.977 2.977 0 1 0 0-5.953 2.977 2.977 0 0 0 0 5.953Zm0 1.019a3.996 3.996 0 1 0 0-7.991 3.996 3.996 0 0 0 0 7.99ZM2.966 20.465c-.09.17.034.373.225.373h7.382a.255.255 0 0 0 .225-.373l-3.673-7.033a.255.255 0 0 0-.451 0l-3.708 7.033Zm3.93-5.49L4.29 19.921h5.19l-2.583-4.946ZM17.152 12.847a.433.433 0 0 1 .46.405c.028.475.249 1.216.745 1.864.487.636 1.233 1.176 2.329 1.295a.433.433 0 0 1-.095.862c-1.361-.15-2.311-.832-2.922-1.63-.602-.787-.883-1.693-.923-2.337a.433.433 0 0 1 .406-.46Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.206 12.847a.433.433 0 0 0-.458.405c-.03.475-.25 1.216-.746 1.864-.487.636-1.233 1.176-2.329 1.295a.433.433 0 1 0 .094.862c1.362-.15 2.312-.832 2.923-1.63.602-.787.883-1.693.922-2.337a.433.433 0 0 0-.405-.46Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.206 20.836a.433.433 0 0 1-.458-.406c-.03-.474-.25-1.215-.746-1.863-.487-.636-1.233-1.176-2.329-1.296a.433.433 0 1 1 .094-.86c1.362.148 2.312.83 2.923 1.63.602.786.883 1.692.922 2.336a.433.433 0 0 1-.405.46Z" fill="#446EF5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.152 20.836a.433.433 0 0 0 .46-.406c.028-.474.249-1.215.745-1.863.487-.636 1.233-1.176 2.329-1.296a.433.433 0 0 0-.095-.86c-1.361.148-2.311.83-2.922 1.63-.602.786-.883 1.692-.923 2.336a.433.433 0 0 0 .406.46Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
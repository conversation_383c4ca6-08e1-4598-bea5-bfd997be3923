"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[1157],{17488:(C,t,L)=>{L.r(t);L.d(t,{default:()=>e});const e={icon:'<path d="M12.0084 5.37951C12.0505 4.79748 12.2427 4.23628 12.5663 3.7507C12.89 3.26513 13.334 2.87177 13.8551 2.60902C14.3761 2.34627 14.9564 2.22312 15.5393 2.25157C16.1221 2.28003 16.6876 2.45913 17.1806 2.77139C17.237 2.80827 17.2824 2.85965 17.312 2.92012C17.3417 2.9806 17.3545 3.04795 17.3491 3.11509C17.3437 3.18223 17.3204 3.24669 17.2815 3.30169C17.2426 3.35668 17.1896 3.40019 17.1281 3.42764C16.2711 3.81321 15.5437 4.43798 15.0331 5.22692C14.5226 6.01586 14.2507 6.93541 14.25 7.87514C14.25 7.98483 14.25 8.09451 14.2612 8.20233C14.2664 8.27047 14.2529 8.33873 14.2221 8.39973C14.1913 8.46074 14.1444 8.51216 14.0865 8.54845C14.0285 8.58474 13.9618 8.60452 13.8935 8.60564C13.8252 8.60676 13.7578 8.58919 13.6987 8.55483C13.1463 8.2388 12.6944 7.77298 12.3953 7.21113C12.0962 6.64929 11.9621 6.01432 12.0084 5.37951ZM22.5 15.0573C22.5013 15.4859 22.3826 15.9063 22.1573 16.2708C21.9319 16.6353 21.609 16.9295 21.225 17.1198L21.1838 17.1386L17.5434 18.6892C17.5075 18.705 17.4701 18.7176 17.4319 18.7267L11.4319 20.2267C11.3724 20.242 11.3114 20.2499 11.25 20.2501H1.5C1.10218 20.2501 0.720644 20.0921 0.43934 19.8108C0.158035 19.5295 0 19.148 0 18.7501V15.0001C0 14.6023 0.158035 14.2208 0.43934 13.9395C0.720644 13.6582 1.10218 13.5001 1.5 13.5001H4.18969L6.31031 11.3786C6.58838 11.0993 6.91904 10.8778 7.28319 10.727C7.64733 10.5763 8.03775 10.4992 8.43188 10.5001H13.125C13.5212 10.5001 13.9122 10.5897 14.2688 10.7623C14.6254 10.9349 14.9383 11.1859 15.1841 11.4966C15.4298 11.8073 15.6021 12.1697 15.6879 12.5564C15.7738 12.9432 15.7709 13.3443 15.6797 13.7298L19.6022 12.828C19.9437 12.7375 20.3013 12.7267 20.6477 12.7964C20.994 12.866 21.3197 13.0142 21.5996 13.2296C21.8796 13.445 22.1064 13.7218 22.2625 14.0387C22.4186 14.3556 22.4999 14.7041 22.5 15.0573ZM21 15.0573C20.9999 14.9333 20.9712 14.811 20.9162 14.6999C20.8611 14.5887 20.7812 14.4918 20.6827 14.4165C20.5841 14.3413 20.4695 14.2897 20.3478 14.2659C20.2261 14.2421 20.1006 14.2467 19.9809 14.2792L19.9519 14.2867L13.6706 15.7314C13.6155 15.7437 13.5593 15.75 13.5028 15.7501H10.5C10.3011 15.7501 10.1103 15.6711 9.96967 15.5305C9.82902 15.3898 9.75 15.1991 9.75 15.0001C9.75 14.8012 9.82902 14.6105 9.96967 14.4698C10.1103 14.3292 10.3011 14.2501 10.5 14.2501H13.125C13.4234 14.2501 13.7095 14.1316 13.9205 13.9206C14.1315 13.7097 14.25 13.4235 14.25 13.1251C14.25 12.8268 14.1315 12.5406 13.9205 12.3296C13.7095 12.1187 13.4234 12.0001 13.125 12.0001H8.43188C8.23482 11.9995 8.03961 12.0381 7.85758 12.1135C7.67555 12.189 7.51035 12.2999 7.37156 12.4398L5.25 14.5605V18.7501H11.1562L17.0091 17.2867L20.5716 15.7698C20.7012 15.7015 20.8097 15.599 20.8852 15.4734C20.9608 15.3478 21.0005 15.2039 21 15.0573ZM15.75 7.87514C15.75 8.54265 15.9479 9.19517 16.3188 9.75019C16.6896 10.3052 17.2167 10.7378 17.8334 10.9932C18.4501 11.2487 19.1287 11.3155 19.7834 11.1853C20.4381 11.0551 21.0395 10.7336 21.5115 10.2616C21.9835 9.78962 22.3049 9.18826 22.4352 8.53357C22.5654 7.87888 22.4985 7.20028 22.2431 6.58358C21.9876 5.96688 21.5551 5.43978 21 5.06893C20.445 4.69808 19.7925 4.50014 19.125 4.50014C18.2299 4.50014 17.3715 4.85572 16.7385 5.48865C16.1056 6.12159 15.75 6.98003 15.75 7.87514Z"fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
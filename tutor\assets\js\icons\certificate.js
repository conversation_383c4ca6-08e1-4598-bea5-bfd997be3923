"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2418],{15661:(l,e,r)=>{r.r(e);r.d(e,{default:()=>o});const o={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M24.5 29.33c5.747 0 10.385-4.629 10.385-10.313 0-5.685-4.638-10.314-10.385-10.314s-10.385 4.63-10.385 10.314S18.753 29.33 24.5 29.33Zm0 2.116c6.904 0 12.5-5.565 12.5-12.43 0-6.864-5.596-12.428-12.5-12.428S12 12.152 12 19.017c0 6.864 5.596 12.429 12.5 12.429Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28.205 16.09a4.624 4.624 0 0 1-6.974 6.039l-1.49 1.489a6.728 6.728 0 0 0 4.518 1.967 6.73 6.73 0 0 0 5.633-10.756l-1.687 1.26ZM15.76 26.515l2.094.301-1.64 11.41a20.288 20.288 0 0 1 16.57 0l-1.639-11.41 2.094-.3 2.21 15.379-2.86-1.421a18.173 18.173 0 0 0-16.18 0l-2.858 1.42 2.21-15.379Z" fill="currentColor"/>',viewBox:"0 0 48 48"}}}]);
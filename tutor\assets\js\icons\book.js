"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[6166],{14341:(a,h,t)=>{t.r(h);t.d(h,{default:()=>e});const e={icon:'<path d="M29 6h-9a5 5 0 0 0-4 2 5 5 0 0 0-4-2H3a1 1 0 0 0-1 1v18a1 1 0 0 0 1 1h9a3 3 0 0 1 3 3 1 1 0 0 0 2 0 3 3 0 0 1 3-3h9a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1ZM12 24H4V8h8a3 3 0 0 1 3 3v14a4.976 4.976 0 0 0-3-1Zm16 0h-8a4.976 4.976 0 0 0-3 1V11a3 3 0 0 1 3-3h8v16Zm-8-13h5a1 1 0 0 1 0 2h-5a1 1 0 0 1 0-2Zm6 5a1 1 0 0 1-1 1h-5a1 1 0 0 1 0-2h5a1 1 0 0 1 1 1Zm0 4a1 1 0 0 1-1 1h-5a1 1 0 0 1 0-2h5a1 1 0 0 1 1 1Z" fill="currentColor"/>',viewBox:"0 0 32 32"}}}]);
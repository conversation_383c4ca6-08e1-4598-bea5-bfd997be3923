"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[7721],{68148:(C,t,r)=>{r.r(t);r.d(t,{default:()=>e});const e={icon:'<path d="M4.9814 4.9977C4.9814 4.87997 4.9343 4.76224 4.84483 4.68218L1.11038 1.03721C1.03032 0.957149 0.926717 0.910057 0.808985 0.910057C0.564103 0.910057 0.375732 1.09372 0.375732 1.3386C0.375732 1.45633 0.422825 1.56465 0.498173 1.6447L3.92652 4.9977L0.498173 8.35541C0.422825 8.43076 0.375732 8.53907 0.375732 8.66151C0.375732 8.9064 0.564103 9.09006 0.808985 9.09006C0.926717 9.09006 1.03032 9.04296 1.11509 8.96291L4.84483 5.31793C4.9343 5.22846 4.9814 5.12014 4.9814 4.9977Z" fill="currentColor"/><path d="M8.99995 4.9977C8.99995 4.87997 8.95286 4.76224 8.86338 4.68218L5.12893 1.03721C5.04888 0.957149 4.94527 0.910057 4.82754 0.910057C4.58266 0.910057 4.39429 1.09372 4.39429 1.3386C4.39429 1.45633 4.44138 1.56465 4.51673 1.6447L7.94508 4.9977L4.51673 8.35541C4.44138 8.43076 4.39429 8.53907 4.39429 8.66151C4.39429 8.9064 4.58266 9.09006 4.82754 9.09006C4.94527 9.09006 5.04888 9.04296 5.13364 8.96291L8.86338 5.31793C8.95286 5.22846 8.99995 5.12014 8.99995 4.9977Z" fill="currentColor"/>',viewBox:"0 0 9 10"}}}]);
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[5479],{619:(t,e,r)=>{r.d(e,{A:()=>a});var n=r(10123);var o=r(70551);function a(t){(0,o.A)(1,arguments);var e=(0,n["default"])(t);e.setSeconds(0,0);return e}},721:(t,e,r)=>{r.d(e,{L:()=>o});var n=r(41594);function o(t){const{day:e,modifiers:r,...o}=t;return n.createElement("td",{...o})}},1207:(t,e,r)=>{r.d(e,{a:()=>o});var n=r(97766);function o(){const t={};for(const e in n.UI){t[n.UI[e]]=`rdp-${n.UI[e]}`}for(const e in n.pL){t[n.pL[e]]=`rdp-${n.pL[e]}`}for(const e in n.wc){t[n.wc[e]]=`rdp-${n.wc[e]}`}for(const e in n.X5){t[n.X5[e]]=`rdp-${n.X5[e]}`}return t}},1741:(t,e,r)=>{r.d(e,{$:()=>a});var n=r(89441);var o=r(49164);function a(t,e){const r=(0,n.q)();const a=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0;const i=(0,o.a)(t,e?.in);const u=i.getDay();const s=(u<a?-7:0)+6-(u-a);i.setDate(i.getDate()+s);i.setHours(23,59,59,999);return i}var i=null&&a},2510:(t,e,r)=>{r.d(e,{e:()=>o});var n=r(39669);function o(t,e,r){return(0,n.P)(t,e*12,r)}var a=null&&o},3884:(t,e,r)=>{r.d(e,{A:()=>G});var n=r(92590);var o=r(942);var a=r(77506);var i=r(21607);var u=r(46201);var s=r(52457);var c=r(62246);var l=r(45538);var d=r(7443);var f=r(82179);var p=r(85589);var v=r(5396);var h=r(94083);var m=r(47849);var g=r(17437);var y=r(12470);var b=r.n(y);var w=r(41594);var _=r.n(w);var x=r(49785);var A=r(19552);var Y=r(11260);var k=r(52854);function O(t){"@babel/helpers - typeof";return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}var S,I;function E(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function C(){return C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},C.apply(null,arguments)}function M(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */M=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new T(n||[]);return o(i,"_invoke",{value:S(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(j([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==O(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function S(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=I(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function I(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(O(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;C(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:j(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function T(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function j(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){T(a,n,o,i,u,"next",t)}function u(t){T(a,n,o,i,u,"throw",t)}i(void 0)}))}}function D(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=N(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function t(){};return{s:o,n:function e(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();return i=e.done,e},e:function t(e){u=!0,a=e},f:function t(){try{i||null==r["return"]||r["return"]()}finally{if(u)throw a}}}}function L(t,e){return K(t)||W(t,e)||N(t,e)||P()}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(t,e){if(t){if("string"==typeof t)return H(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?H(t,e):void 0}}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function W(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function K(t){if(Array.isArray(t))return t}function B(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var z=620;var U=620;var R=true?{name:"xdvdnl",styles:"margin-top:auto"}:0;var F=true?{name:"innv7",styles:"min-height:16px"}:0;var q=function t(){var e=(0,f.p)({defaultValues:{brush_size:40,prompt:""}});var r=(0,p.wt)();var s=(0,w.useRef)(null);var c=(0,Y.A6)(),h=c.onDropdownMenuChange,b=c.currentImage,_=c.field,O=c.onCloseModal;var S=(0,p.ah)();var I=(0,d.d)(e.watch("brush_size",40));var E=(0,w.useState)([]),T=L(E,2),P=T[0],N=T[1];var H=(0,w.useState)(1),W=L(H,2),K=W[0],B=W[1];var q=(0,w.useCallback)((function(t,e){var r;var n=(r=s.current)===null||r===void 0?void 0:r.getContext("2d");if(!n){return}var o=D(e.slice(0,t)),a;try{for(o.s();!(a=o.n()).done;){var i=a.value;n.putImageData(i,0,0)}}catch(t){o.e(t)}finally{o.f()}}),[]);(0,w.useEffect)((function(){var t;var e=(t=s.current)===null||t===void 0?void 0:t.getContext("2d");if(!e){return}e.lineWidth=I}),[I]);(0,w.useEffect)((function(){var t=function t(e){if(e.metaKey){if(e.shiftKey&&e.key.toUpperCase()==="Z"){q(K+1,P);B((function(t){return Math.min(t+1,P.length)}));return}if(e.key.toUpperCase()==="Z"){q(K-1,P);B((function(t){return Math.max(t-1,1)}));return}}};window.addEventListener("keydown",t);return function(){window.removeEventListener("keydown",t)}}),[K,P,q]);if(!b){return null}return(0,g.Y)("form",{css:k.C.wrapper,onSubmit:e.handleSubmit(function(){var t=j(M().mark((function t(e){var n,o,a,i,u;return M().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:n=s.current;o=n===null||n===void 0?void 0:n.getContext("2d");if(!(!n||!o)){c.next=4;break}return c.abrupt("return");case 4:a={prompt:e.prompt,image:(0,v.M5)(n)};c.next=7;return r.mutateAsync(a);case 7:i=c.sent;if(i){u=new Image;u.onload=function(){n.width=z;n.height=U;o.drawImage(u,0,0,n.width,n.height);o.lineWidth=I;o.lineJoin="round";o.lineCap="round"};u.src=i}case 9:case"end":return c.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},(0,g.Y)("div",{css:k.C.left},(0,g.Y)("div",{css:Q.leftWrapper},(0,g.Y)("div",{css:Q.actionBar},(0,g.Y)("div",{css:Q.backButtonWrapper},(0,g.Y)("button",{type:"button",css:Q.backButton,onClick:function t(){return h("generation")}},(0,g.Y)(o.A,{name:"arrowLeft"})),(0,y.__)("Magic Fill","tutor")),(0,g.Y)("div",{css:Q.actions},(0,g.Y)(n.A,{variant:"ghost",disabled:P.length===0,onClick:function t(){q(1,P);N(P.slice(0,1));B(1)}},(0,y.__)("Revert to Original","tutor")),(0,g.Y)(a.w,{variant:"vertical",css:F}),(0,g.Y)("div",{css:Q.undoRedo},(0,g.Y)(n.A,{variant:"ghost",size:"icon",disabled:K<=1,onClick:function t(){q(K-1,P);B((function(t){return Math.max(t-1,1)}))}},(0,g.Y)(o.A,{name:"undo",width:20,height:20})),(0,g.Y)(n.A,{variant:"ghost",size:"icon",disabled:K===P.length,onClick:function t(){q(K+1,P);B((function(t){return Math.min(t+1,P.length)}))}},(0,g.Y)(o.A,{name:"redo",width:20,height:20}))))),(0,g.Y)("div",{css:Q.canvasAndLoading},(0,g.Y)(A.f,{ref:s,width:z,height:U,src:b,brushSize:I,trackStack:P,pointer:K,setTrackStack:N,setPointer:B}),(0,g.Y)(l.A,{when:r.isPending},(0,g.Y)("div",{css:Q.loading}))),(0,g.Y)("div",{css:Q.footerActions},(0,g.Y)("div",{css:Q.footerActionsLeft},(0,g.Y)(n.A,{variant:"secondary",onClick:function t(){var e="".concat((0,m.Ak)(),".png");var r=(0,v.dX)(s),n=r.canvas;if(!n)return;(0,v.JD)((0,v.M5)(n),e)}},(0,g.Y)(o.A,{name:"download",width:24,height:24})))))),(0,g.Y)("div",{css:k.C.right},(0,g.Y)("div",{css:Q.fields},(0,g.Y)(x.xI,{control:e.control,name:"brush_size",render:function t(e){return(0,g.Y)(i.A,C({},e,{label:"Brush Size",min:1,max:100,isMagicAi:true,hasBorder:true}))}}),(0,g.Y)(x.xI,{control:e.control,name:"prompt",render:function t(e){return(0,g.Y)(u.A,C({},e,{label:(0,y.__)("Describe the Fill","tutor"),placeholder:(0,y.__)("Write 5 words to describe...","tutor"),rows:4,isMagicAi:true}))}})),(0,g.Y)("div",{css:[k.C.rightFooter,R,true?"":0,true?"":0]},(0,g.Y)("div",{css:Q.footerButtons},(0,g.Y)(n.A,{type:"submit",disabled:r.isPending||!e.watch("prompt")},(0,g.Y)(o.A,{name:"magicWand",width:24,height:24}),(0,y.__)("Generative Erase","tutor")),(0,g.Y)(n.A,{variant:"primary_outline",disabled:r.isPending,loading:S.isPending,onClick:j(M().mark((function t(){var e,r,n;return M().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:e=(0,v.dX)(s),r=e.canvas;if(r){o.next=3;break}return o.abrupt("return");case 3:o.next=5;return S.mutateAsync({image:(0,v.M5)(r)});case 5:n=o.sent;if(n.data){_.onChange(n.data);O()}case 7:case"end":return o.stop()}}),t)})))},(0,y.__)("Use Image","tutor"))))))};const G=q;var V={loading:(0,g.i7)(S||(S=E(["\n    0% {\n      opacity: 0;\n    }\n    50% {\n      opacity: 0.6;\n    }\n    100% {\n      opacity: 0;\n    }\n  "]))),walker:(0,g.i7)(I||(I=E(["\n    0% {\n      left: 0%;\n    }\n    100% {\n      left: 100%;\n    }\n  "])))};var Q={canvasAndLoading:(0,g.AH)("position:relative;z-index:",s.fE.positive,";"+(true?"":0),true?"":0),loading:(0,g.AH)("position:absolute;top:0;left:0;width:100%;height:100%;background:",s.I6.ai.gradient_1,";opacity:0.6;transition:0.5s ease opacity;animation:",V.loading," 1s linear infinite;z-index:0;&::before{content:'';position:absolute;top:0;left:0;width:200px;height:100%;background:linear-gradient(\n        270deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 0.6) 51.13%,\n        rgba(255, 255, 255, 0) 100%\n      );animation:",V.walker," 1s linear infinite;}"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,fields:(0,g.AH)("display:flex;flex-direction:column;gap:",s.YK[12],";"+(true?"":0),true?"":0),leftWrapper:(0,g.AH)("display:flex;flex-direction:column;gap:",s.YK[8],";padding-block:",s.YK[16],";"+(true?"":0),true?"":0),footerButtons:(0,g.AH)("display:flex;flex-direction:column;gap:",s.YK[8],";"+(true?"":0),true?"":0),footerActions:true?{name:"1eoy87d",styles:"display:flex;justify-content:space-between"}:0,footerActionsLeft:(0,g.AH)("display:flex;align-items:center;gap:",s.YK[12],";"+(true?"":0),true?"":0),actions:(0,g.AH)("display:flex;align-items:center;gap:",s.YK[16],";"+(true?"":0),true?"":0),undoRedo:(0,g.AH)("display:flex;align-items:center;gap:",s.YK[12],";"+(true?"":0),true?"":0),backButtonWrapper:(0,g.AH)("display:flex;align-items:center;gap:",s.YK[8],";",c.I.body("medium"),";color:",s.I6.text.title,";"+(true?"":0),true?"":0),backButton:(0,g.AH)(h.x.resetButton,";width:24px;height:24px;border-radius:",s.Vq[4],";border:1px solid ",s.I6.stroke["default"],";display:flex;align-items:center;justify-content:center;"+(true?"":0),true?"":0),image:true?{name:"gb1um3",styles:"width:492px;height:498px;position:relative;img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}"}:0,canvasWrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,g.AH)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",s.Vq.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",s.I6.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",s.fE.highest,";display:none;"+(true?"":0),true?"":0)}}},4556:(t,e,r)=>{r.d(e,{U:()=>o});var n=r(74084);function o(t,e,r){const[o,a]=(0,n.x)(r?.in,t,e);const i=o.getFullYear()-a.getFullYear();const u=o.getMonth()-a.getMonth();return i*12+u}var a=null&&o},4726:(t,e,r)=>{r.d(e,{c:()=>o});var n=r(41594);function o(t){return n.createElement("option",{...t})}},5170:(t,e,r)=>{r.d(e,{h:()=>k});var n=r(41594);var o=r(97766);var a=r(56066);var i=r(32850);var u=r(19287);var s=r(87096);var c=r(78933);var l=r(1207);var d=r(39903);var f=r(63612);var p=r(66564);var v=r(86607);var h=r(57899);var m=r(93116);var g=r(68587);var y=r(58071);var b=r(51409);var w=r(53581);var _=r(45179);var x=r(33127);var A=r(52044);var Y=r(70684);function k(t){const{components:e,formatters:r,labels:k,dateLib:O,locale:S,classNames:I}=(0,n.useMemo)((()=>{const e={...a.c,...t.locale};const r=new i.i0({locale:e,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib);return{dateLib:r,components:(0,s.P)(t.components),formatters:(0,d.G)(t.formatters),labels:{...m,...t.labels},locale:e,classNames:{...(0,l.a)(),...t.classNames}}}),[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]);const{captionLayout:E,mode:C,onDayBlur:M,onDayClick:T,onDayFocus:j,onDayKeyDown:D,onDayMouseEnter:L,onDayMouseLeave:P,onNextClick:N,onPrevClick:H,showWeekNumber:W,styles:K}=t;const{formatCaption:B,formatDay:z,formatMonthDropdown:U,formatWeekNumber:R,formatWeekNumberHeader:F,formatWeekdayName:q,formatYearDropdown:G}=r;const V=(0,y._)(t,O);const{days:Q,months:$,navStart:Z,navEnd:X,previousMonth:J,nextMonth:tt,goToMonth:et}=V;const rt=(0,_.g)(Q,t,O);const{isSelected:nt,select:ot,selected:at}=(0,x.C)(t,O)??{};const{blur:it,focused:ut,isFocusTarget:st,moveFocus:ct,setFocused:lt}=(0,w.i)(t,V,rt,nt??(()=>false),O);const{labelDayButton:dt,labelGridcell:ft,labelGrid:pt,labelMonthDropdown:vt,labelNav:ht,labelWeekday:mt,labelWeekNumber:gt,labelWeekNumberHeader:yt,labelYearDropdown:bt}=k;const wt=(0,n.useMemo)((()=>(0,v.c)(O,t.ISOWeek)),[O,t.ISOWeek]);const _t=C!==undefined||T!==undefined;const xt=(0,n.useCallback)((()=>{if(!J)return;et(J);H?.(J)}),[J,et,H]);const At=(0,n.useCallback)((()=>{if(!tt)return;et(tt);N?.(tt)}),[et,tt,N]);const Yt=(0,n.useCallback)(((t,e)=>r=>{r.preventDefault();r.stopPropagation();lt(t);ot?.(t.date,e,r);T?.(t.date,e,r)}),[ot,T,lt]);const kt=(0,n.useCallback)(((t,e)=>r=>{lt(t);j?.(t.date,e,r)}),[j,lt]);const Ot=(0,n.useCallback)(((t,e)=>r=>{it();M?.(t.date,e,r)}),[it,M]);const St=(0,n.useCallback)(((e,r)=>n=>{const o={ArrowLeft:["day",t.dir==="rtl"?"after":"before"],ArrowRight:["day",t.dir==="rtl"?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[n.shiftKey?"year":"month","before"],PageDown:[n.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[n.key]){n.preventDefault();n.stopPropagation();const[t,e]=o[n.key];ct(t,e)}D?.(e.date,r,n)}),[ct,D,t.dir]);const It=(0,n.useCallback)(((t,e)=>r=>{L?.(t.date,e,r)}),[L]);const Et=(0,n.useCallback)(((t,e)=>r=>{P?.(t.date,e,r)}),[P]);const Ct=(0,n.useCallback)((t=>e=>{const r=Number(e.target.value);const n=O.setMonth(O.startOfMonth(t),r);et(n)}),[O,et]);const Mt=(0,n.useCallback)((t=>e=>{const r=Number(e.target.value);const n=O.setYear(O.startOfMonth(t),r);et(n)}),[O,et]);const{className:Tt,style:jt}=(0,n.useMemo)((()=>({className:[I[o.UI.Root],t.className].filter(Boolean).join(" "),style:{...K?.[o.UI.Root],...t.style}})),[I,t.className,t.style,K]);const Dt=(0,c.C)(t);const Lt=(0,n.useRef)(null);(0,g.s)(Lt,Boolean(t.animate),{classNames:I,months:$,focused:ut,dateLib:O});const Pt={dayPickerProps:t,selected:at,select:ot,isSelected:nt,months:$,nextMonth:tt,previousMonth:J,goToMonth:et,getModifiers:rt,components:e,classNames:I,styles:K,labels:k,formatters:r};return n.createElement(b.S.Provider,{value:Pt},n.createElement(e.Root,{rootRef:t.animate?Lt:undefined,className:Tt,style:jt,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...Dt},n.createElement(e.Months,{className:I[o.UI.Months],style:K?.[o.UI.Months]},!t.hideNavigation&&n.createElement(e.Nav,{"data-animated-nav":t.animate?"true":undefined,className:I[o.UI.Nav],style:K?.[o.UI.Nav],"aria-label":ht(),onPreviousClick:xt,onNextClick:At,previousMonth:J,nextMonth:tt}),$.map(((a,i)=>{const s=(0,f.L)(a.date,Z,X,r,O);const c=(0,h.g)(Z,X,r,O);return n.createElement(e.Month,{"data-animated-month":t.animate?"true":undefined,className:I[o.UI.Month],style:K?.[o.UI.Month],key:i,displayIndex:i,calendarMonth:a},n.createElement(e.MonthCaption,{"data-animated-caption":t.animate?"true":undefined,className:I[o.UI.MonthCaption],style:K?.[o.UI.MonthCaption],calendarMonth:a,displayIndex:i},E?.startsWith("dropdown")?n.createElement(e.DropdownNav,{className:I[o.UI.Dropdowns],style:K?.[o.UI.Dropdowns]},E==="dropdown"||E==="dropdown-months"?n.createElement(e.MonthsDropdown,{className:I[o.UI.MonthsDropdown],"aria-label":vt(),classNames:I,components:e,disabled:Boolean(t.disableNavigation),onChange:Ct(a.date),options:s,style:K?.[o.UI.Dropdown],value:O.getMonth(a.date)}):n.createElement("span",null,U(a.date,O)),E==="dropdown"||E==="dropdown-years"?n.createElement(e.YearsDropdown,{className:I[o.UI.YearsDropdown],"aria-label":bt(O.options),classNames:I,components:e,disabled:Boolean(t.disableNavigation),onChange:Mt(a.date),options:c,style:K?.[o.UI.Dropdown],value:O.getYear(a.date)}):n.createElement("span",null,G(a.date,O)),n.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},B(a.date,O.options,O))):n.createElement(e.CaptionLabel,{className:I[o.UI.CaptionLabel],role:"status","aria-live":"polite"},B(a.date,O.options,O))),n.createElement(e.MonthGrid,{role:"grid","aria-multiselectable":C==="multiple"||C==="range","aria-label":pt(a.date,O.options,O)||undefined,className:I[o.UI.MonthGrid],style:K?.[o.UI.MonthGrid]},!t.hideWeekdays&&n.createElement(e.Weekdays,{"data-animated-weekdays":t.animate?"true":undefined,className:I[o.UI.Weekdays],style:K?.[o.UI.Weekdays]},W&&n.createElement(e.WeekNumberHeader,{"aria-label":yt(O.options),className:I[o.UI.WeekNumberHeader],style:K?.[o.UI.WeekNumberHeader],scope:"col"},F()),wt.map(((t,r)=>n.createElement(e.Weekday,{"aria-label":mt(t,O.options,O),className:I[o.UI.Weekday],key:r,style:K?.[o.UI.Weekday],scope:"col"},q(t,O.options,O))))),n.createElement(e.Weeks,{"data-animated-weeks":t.animate?"true":undefined,className:I[o.UI.Weeks],style:K?.[o.UI.Weeks]},a.weeks.map(((r,a)=>n.createElement(e.Week,{className:I[o.UI.Week],key:r.weekNumber,style:K?.[o.UI.Week],week:r},W&&n.createElement(e.WeekNumber,{week:r,style:K?.[o.UI.WeekNumber],"aria-label":gt(r.weekNumber,{locale:S}),className:I[o.UI.WeekNumber],scope:"row",role:"rowheader"},R(r.weekNumber)),r.days.map((r=>{const{date:a}=r;const i=rt(r);i[o.pL.focused]=!i.hidden&&Boolean(ut?.isEqualTo(r));i[o.wc.selected]=nt?.(a)||i.selected;if((0,Y.oM)(at)){const{from:t,to:e}=at;i[o.wc.range_start]=Boolean(t&&e&&O.isSameDay(a,t));i[o.wc.range_end]=Boolean(t&&e&&O.isSameDay(a,e));i[o.wc.range_middle]=(0,A.R)(at,a,true,O)}const s=(0,p.J)(i,K,t.modifiersStyles);const c=(0,u.k)(i,I,t.modifiersClassNames);const l=!_t&&!i.hidden?ft(a,i,O.options,O):undefined;return n.createElement(e.Day,{key:`${O.format(a,"yyyy-MM-dd")}_${O.format(r.displayMonth,"yyyy-MM")}`,day:r,modifiers:i,className:c.join(" "),style:s,role:"gridcell","aria-selected":i.selected||undefined,"aria-label":l,"data-day":O.format(a,"yyyy-MM-dd"),"data-month":r.outside?O.format(a,"yyyy-MM"):undefined,"data-selected":i.selected||undefined,"data-disabled":i.disabled||undefined,"data-hidden":i.hidden||undefined,"data-outside":r.outside||undefined,"data-focused":i.focused||undefined,"data-today":i.today||undefined},!i.hidden&&_t?n.createElement(e.DayButton,{className:I[o.UI.DayButton],style:K?.[o.UI.DayButton],type:"button",day:r,modifiers:i,disabled:i.disabled||undefined,tabIndex:st(r)?0:-1,"aria-label":dt(a,i,O.options,O),onClick:Yt(r,i),onBlur:Ot(r,i),onFocus:kt(r,i),onKeyDown:St(r,i),onMouseEnter:It(r,i),onMouseLeave:Et(r,i)},z(a,O.options,O)):!i.hidden&&z(r.date,O.options,O))}))))))))}))),t.footer&&n.createElement(e.Footer,{className:I[o.UI.Footer],style:K?.[o.UI.Footer],role:"status","aria-live":"polite"},t.footer)))}},5396:(t,e,r)=>{r.d(e,{JD:()=>i,KG:()=>o,M5:()=>c,dX:()=>s,kd:()=>n});function n(t,e){t.lineTo(e.x,e.y);t.stroke()}function o(t,e){var r=e.x-t.x;var n=e.y-t.y;return Math.sqrt(r*r+n*n)}function a(t){var e=atob(t.split(",")[1]);var r=t.split(",")[0].split(":")[1].split(";")[0];var n=new ArrayBuffer(e.length);var o=new Uint8Array(n);for(var a=0;a<e.length;a++){o[a]=e.charCodeAt(a)}return new Blob([n],{type:r})}function i(t,e){var r=a(t);var n=document.createElement("a");n.href=URL.createObjectURL(r);n.download=e;document.body.appendChild(n);n.click();document.body.removeChild(n)}function u(t,e){var r=document.createElement("canvas");r.width=1024;r.height=1024;var n=r.getContext("2d");n===null||n===void 0||n.putImageData(t,0,0);n===null||n===void 0||n.drawImage(r,0,0,1024,1024);return new Promise((function(t){r.toBlob((function(r){if(!r){t(null);return}t(new File([r],e,{type:"image/png"}))}))}))}var s=function t(e){if(e&&typeof e!=="function"&&e.current){var r=e.current;var n=r.getContext("2d");return{canvas:r,context:n}}return{canvas:null,context:null}};var c=function t(e){return e.toDataURL("image/png")}},6867:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/7a53b07b7f13e48b7b7b47dff35d9946-black-and-white.png"},6998:(t,e,r)=>{r.d(e,{n:()=>n});function n(t){return"Choose the Year"}},7443:(t,e,r)=>{r.d(e,{d:()=>d});var n=r(41594);var o=r.n(n);function a(t,e){return l(t)||c(t,e)||u(t,e)||i()}function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function c(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function l(t){if(Array.isArray(t))return t}var d=function t(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:300;var o=(0,n.useState)(e),i=a(o,2),u=i[0],s=i[1];(0,n.useEffect)((function(){var t=setTimeout((function(){s(e)}),r);return function(){clearTimeout(t)}}),[e,r]);return u}},7629:(t,e,r)=>{r.d(e,{b:()=>u});var n=r(89441);var o=r(27256);var a=r(91408);var i=r(73524);function u(t,e){const r=(0,n.q)();const u=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1;const s=(0,a.h)(t,e);const c=(0,o.w)(e?.in||t,0);c.setFullYear(s,0,u);c.setHours(0,0,0,0);const l=(0,i.k)(c,e);return l}var s=null&&u},8008:(t,e,r)=>{r.d(e,{o:()=>o});var n=r(49164);function o(t,e){const r=(0,n.a)(t,e?.in);r.setHours(0,0,0,0);return r}var a=null&&o},8798:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/743768c1bc7847c4dff86a04d365c429-not-found.webp"},9683:(t,e,r)=>{r.d(e,{Y:()=>g});var n=r(76260);var o=r(69564);const a=/^(\d+)(th|st|nd|rd)?/i;const i=/\d+/i;const u={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i};const s={any:[/^b/i,/^(a|c)/i]};const c={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i};const l={any:[/1/i,/2/i,/3/i,/4/i]};const d={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i};const f={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]};const p={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i};const v={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]};const h={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i};const m={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}};const g={ordinalNumber:(0,o.K)({matchPattern:a,parsePattern:i,valueCallback:t=>parseInt(t,10)}),era:(0,n.A)({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),quarter:(0,n.A)({matchPatterns:c,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:t=>t+1}),month:(0,n.A)({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:f,defaultParseWidth:"any"}),day:(0,n.A)({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:v,defaultParseWidth:"any"}),dayPeriod:(0,n.A)({matchPatterns:h,defaultMatchWidth:"any",parsePatterns:m,defaultParseWidth:"any"})}},9727:(t,e,r)=>{r.d(e,{_:()=>n});function n(t){const e=[];return t.reduce(((t,e)=>{const r=[];const n=e.weeks.reduce(((t,e)=>[...t,...e.days]),r);return[...t,...n]}),e)}},10540:t=>{function e(t){var e=document.createElement("style");t.setAttributes(e,t.attributes);t.insert(e,t.options);return e}t.exports=e},10752:(t,e,r)=>{r.d(e,{z:()=>L});var n=r(92590);var o=r(942);var a=r(52457);var i=r(62246);var u=r(98880);var s=r(85420);var c=r(47989);var l=r(85589);var d=r(5396);var f=r(94083);var p=r(47849);var v=r(17437);var h=r(12470);var m=r.n(h);var g=r(41594);var y=r.n(g);var b=r(11260);var w=r(41594);function _(t){"@babel/helpers - typeof";return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}var x;function A(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function Y(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Y=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new T(n||[]);return o(i,"_invoke",{value:I(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var x=Object.getPrototypeOf,A=x&&x(x(j([])));A&&A!==r&&n.call(A,i)&&(w=A);var k=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==_(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function I(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=E(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function M(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(_(e)+" is not iterable")}return y.prototype=b,o(k,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},O(S.prototype),c(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new S(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},O(k),c(k,s,"Generator"),c(k,i,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),M(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;M(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:j(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function k(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function O(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){k(a,n,o,i,u,"next",t)}function u(t){k(a,n,o,i,u,"throw",t)}i(void 0)}))}}function S(t,e){return T(t)||M(t,e)||E(t,e)||I()}function I(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(t,e){if(t){if("string"==typeof t)return C(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?C(t,e):void 0}}function C(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function M(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function T(t){if(Array.isArray(t))return t}function j(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var D=[{label:(0,h.__)("Magic Fill","tutor"),value:"magic-fill",icon:(0,v.Y)(o.A,{name:"magicWand",width:24,height:24})},{label:(0,h.__)("Download","tutor"),value:"download",icon:(0,v.Y)(o.A,{name:"download",width:24,height:24})}];var L=function t(e){var r=e.src,a=e.loading,i=e.index;var f=(0,g.useRef)(null);var m=(0,g.useState)(false),y=S(m,2),_=y[0],x=y[1];var A=(0,b.A6)(),k=A.onDropdownMenuChange,I=A.setCurrentImage,E=A.onCloseModal,C=A.field;var M=(0,l.ah)();if(a||!r){return(0,v.Y)("div",{css:B.loader(i+1)})}return(0,v.Y)(w.Fragment,null,(0,v.Y)("div",{css:B.image({isActive:M.isPending})},(0,v.Y)("img",{src:r,alt:(0,h.__)("Generated Image","tutor")}),(0,v.Y)("div",{"data-actions":true},(0,v.Y)("div",{css:B.useButton},(0,v.Y)(n.A,{variant:"primary",disabled:M.isPending,onClick:O(Y().mark((function t(){var e;return Y().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(r){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return M.mutateAsync({image:r});case 4:e=n.sent;if(e.data){C.onChange(e.data);E()}case 6:case"end":return n.stop()}}),t)}))),loading:M.isPending},(0,v.Y)(o.A,{name:"download",width:24,height:24}),(0,h.__)("Use This","tutor"))),(0,v.Y)(n.A,{variant:"primary",size:"icon",css:B.threeDots,ref:f,onClick:function t(){return x(true)}},(0,v.Y)(o.A,{name:"threeDotsVertical",width:24,height:24})))),(0,v.Y)(c.A,{triggerRef:f,isOpen:_,closePopover:function t(){x(false)},animationType:s.J6.slideDown,maxWidth:"160px"},(0,v.Y)("div",{css:B.dropdownOptions},(0,v.Y)(u.A,{each:D},(function(t,e){return(0,v.Y)("button",{type:"button",key:e,css:B.dropdownItem,onClick:function e(){switch(t.value){case"magic-fill":{I(r);k(t.value);break}case"download":{var n="".concat((0,p.Ak)(),".png");(0,d.JD)(r,n);break}default:break}x(false)}},t.icon,t.label)})))))};var P=(0,v.i7)(x||(x=A(["\n\t\t0% {\n      opacity: 0.3;\n    }\n\t\t25% {\n\t\t\topacity: 0.5;\n\t\t}\n    50% {\n      opacity: 0.7;\n    }\n\t\t75% {\n\t\t\topacity: 0.5;\n\t\t}\n    100% {\n      opacity: 0.3;\n    }\n"])));var N=true?{name:"net8hi",styles:"background-position:bottom right;animation-delay:1s"}:0;var H=true?{name:"15lylfh",styles:"background-position:bottom left;animation-delay:1.5s"}:0;var W=true?{name:"1ooocct",styles:"background-position:top right;animation-delay:0.5s"}:0;var K=true?{name:"1auq8ax",styles:"background-position:top left"}:0;var B={loader:function t(e){return(0,v.AH)("border-radius:",a.Vq[12],";background:linear-gradient(\n      73.09deg,\n      #ff9645 18.05%,\n      #ff6471 30.25%,\n      #cf6ebd 55.42%,\n      #a477d1 71.66%,\n      #3e64de 97.9%\n    );position:relative;width:100%;height:100%;background-size:612px 612px;opacity:0.3;transition:opacity 0.5s ease;animation:",P," 2s linear infinite;",e===1&&K," ",e===2&&W," ",e===3&&H," ",e===4&&N,";"+(true?"":0),true?"":0)},image:function t(e){var r=e.isActive;return(0,v.AH)("width:100%;height:100%;overflow:hidden;border-radius:",a.Vq[12],";position:relative;outline:2px solid transparent;outline-offset:2px;transition:border-radius 0.3s ease;[data-actions]{opacity:0;transition:opacity 0.3s ease;}img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}",r&&(0,v.AH)("outline-color:",a.I6.stroke.brand,";[data-actions]{opacity:1;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{outline-color:",a.I6.stroke.brand,";[data-actions]{opacity:1;}}"+(true?"":0),true?"":0)},threeDots:(0,v.AH)("position:absolute;top:",a.YK[8],";right:",a.YK[8],";border-radius:",a.Vq[4],";"+(true?"":0),true?"":0),useButton:(0,v.AH)("position:absolute;left:50%;bottom:",a.YK[12],";transform:translateX(-50%);button{display:inline-flex;align-items:center;gap:",a.YK[4],";}"+(true?"":0),true?"":0),dropdownOptions:(0,v.AH)("display:flex;flex-direction:column;padding-block:",a.YK[8],";"+(true?"":0),true?"":0),dropdownItem:(0,v.AH)(i.I.small(),";",f.x.resetButton,";height:40px;display:flex;gap:",a.YK[10],";align-items:center;transition:background-color 0.3s ease;color:",a.I6.text.title,";padding-inline:",a.YK[8],";cursor:pointer;svg{color:",a.I6.icon["default"],";}&:hover{background-color:",a.I6.background.hover,";}"+(true?"":0),true?"":0)}},11260:(t,e,r)=>{r.d(e,{A6:()=>y,co:()=>b,i3:()=>m});var n=r(82179);var o=r(12470);var a=r.n(o);var i=r(41594);var u=r.n(i);var s=r(49785);var c=r(17437);function l(t,e){return h(t)||v(t,e)||f(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function h(t){if(Array.isArray(t))return t}var m=[(0,o.__)("A serene classroom setting with books and a chalkboard","tutor"),(0,o.__)("An abstract representation of innovation and creativity","tutor"),(0,o.__)("A vibrant workspace with a laptop and coffee cup","tutor"),(0,o.__)("A modern design with digital learning icons","tutor"),(0,o.__)("A futuristic cityscape with a glowing pathway","tutor"),(0,o.__)("A peaceful nature scene with soft colors","tutor"),(0,o.__)("A professional boardroom with sleek visuals","tutor"),(0,o.__)("A stack of books with warm, inviting lighting","tutor"),(0,o.__)("A dynamic collage of technology and education themes","tutor"),(0,o.__)("A bold and minimalistic design with striking colors","tutor")];var g=u().createContext(null);var y=function t(){var e=(0,i.useContext)(g);if(!e){throw new Error("useMagicImageGeneration must be used within MagicImageGenerationProvider.")}return e};var b=function t(e){var r=e.children,o=e.field,a=e.fieldState,u=e.onCloseModal;var d=(0,n.p)({defaultValues:{prompt:"",style:"none"}});var f=(0,i.useState)("generation"),p=l(f,2),v=p[0],h=p[1];var m=(0,i.useState)(""),y=l(m,2),b=y[0],w=y[1];var _=(0,i.useState)([null,null,null,null]),x=l(_,2),A=x[0],Y=x[1];var k=(0,i.useCallback)((function(t){h(t)}),[]);return(0,c.Y)(g.Provider,{value:{state:v,onDropdownMenuChange:k,images:A,setImages:Y,currentImage:b,setCurrentImage:w,field:o,fieldState:a,onCloseModal:u}},(0,c.Y)(s.Op,d,r))}},11456:(t,e,r)=>{r.d(e,{s:()=>u});var n=r(31308);var o=r(99719);var a=r(90642);var i=r(49164);function u(t,e){const r=(0,i.a)(t,e?.in);const u=+(0,o.b)(r)-+(0,a.w)(r);return Math.round(u/n.my)+1}var s=null&&u},12262:(t,e,r)=>{r.d(e,{A:()=>g});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(3771);var u=r.n(i);var s=r(38919);var c=r(942);var l=r(52457);var d=r(62246);var f=r(45538);var p=r(4704);function v(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var h={large:"regular",regular:"small",small:"small"};var m=function t(e){var r=e.buttonText,a=r===void 0?(0,o.__)("Upload Media","tutor"):r,i=e.infoText,u=e.size,d=u===void 0?"regular":u,v=e.value,m=e.uploadHandler,g=e.clearHandler,y=e.emptyImageCss,b=e.previewImageCss,_=e.overlayCss,x=e.replaceButtonText,A=e.loading,Y=e.disabled,k=Y===void 0?false:Y,O=e.isClearAble,S=O===void 0?true:O;return(0,n.Y)(f.A,{when:!A,fallback:(0,n.Y)("div",{css:w.emptyMedia({size:d,isDisabled:k})},(0,n.Y)(p.p8,null))},(0,n.Y)(f.A,{when:v===null||v===void 0?void 0:v.url,fallback:(0,n.Y)("div",{"aria-disabled":k,css:[w.emptyMedia({size:d,isDisabled:k}),y,true?"":0,true?"":0],onClick:function t(e){e.stopPropagation();if(k){return}m()},onKeyDown:function t(e){if(!k&&e.key==="Enter"){e.preventDefault();m()}}},(0,n.Y)(c.A,{name:"addImage",width:32,height:32}),(0,n.Y)(s.A,{disabled:k,size:h[d],variant:"secondary",buttonContentCss:w.buttonText,"data-cy":"upload-media"},a),(0,n.Y)(f.A,{when:i},(0,n.Y)("p",{css:w.infoTexts},i)))},(function(t){return(0,n.Y)("div",{css:[w.previewWrapper({size:d,isDisabled:k}),b,true?"":0,true?"":0],"data-cy":"media-preview"},(0,n.Y)("img",{src:t,alt:v===null||v===void 0?void 0:v.title,css:w.imagePreview}),(0,n.Y)("div",{css:[w.hoverPreview,_,true?"":0,true?"":0],"data-hover-buttons-wrapper":true},(0,n.Y)(s.A,{disabled:k,variant:"secondary",size:h[d],buttonCss:(0,n.AH)("margin-top:",S&&l.YK[16],";"+(true?"":0),true?"":0),onClick:function t(e){e.stopPropagation();m()},"data-cy":"replace-media"},x||(0,o.__)("Replace Image","tutor")),(0,n.Y)(f.A,{when:S},(0,n.Y)(s.A,{disabled:k,variant:"text",size:h[d],onClick:function t(e){e.stopPropagation();g()},"data-cy":"clear-media"},(0,o.__)("Remove","tutor")))))})))};const g=m;var y=true?{name:"1kn988u",styles:"width:168px"}:0;var b=true?{name:"1kn988u",styles:"width:168px"}:0;var w={emptyMedia:function t(e){var r=e.size,o=e.isDisabled;return(0,n.AH)("width:100%;height:168px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:",l.YK[8],";border:1px dashed ",l.I6.stroke.border,";border-radius:",l.Vq[8],";background-color:",l.I6.bg.white,";overflow:hidden;cursor:",o?"not-allowed":"pointer",";",r==="small"&&b," svg{color:",l.I6.icon["default"],";}&:hover svg{color:",!o&&l.I6.brand.blue,";}"+(true?"":0),true?"":0)},buttonText:(0,n.AH)("color:",l.I6.text.brand,";"+(true?"":0),true?"":0),infoTexts:(0,n.AH)(d.I.tiny(),";color:",l.I6.text.subdued,";text-align:center;"+(true?"":0),true?"":0),previewWrapper:function t(e){var r=e.size,o=e.isDisabled;return(0,n.AH)("width:100%;height:168px;border:1px solid ",l.I6.stroke["default"],";border-radius:",l.Vq[8],";overflow:hidden;position:relative;background-color:",l.I6.bg.white,";",r==="small"&&y," &:hover{[data-hover-buttons-wrapper]{display:",o?"none":"flex",";opacity:",o?0:1,";}}"+(true?"":0),true?"":0)},imagePreview:true?{name:"1obrg50",styles:"height:100%;width:100%;object-fit:contain"}:0,hoverPreview:(0,n.AH)("display:flex;flex-direction:column;justify-content:center;align-items:center;gap:",l.YK[8],";opacity:0;position:absolute;inset:0;background-color:",u()(l.I6.color.black.main,.6),";button:first-of-type{box-shadow:",l.r7.button,";}button:last-of-type:not(:only-of-type){color:",l.I6.text.white,";box-shadow:none;}"+(true?"":0),true?"":0)}},12721:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/e67e28356e87045281d41cd6583f5c41-generate-image-2x.webp"},13249:(t,e,r)=>{r.d(e,{r:()=>a});var n=r(74084);var o=r(8008);function a(t,e,r){const[a,i]=(0,n.x)(r?.in,t,e);return+(0,o.o)(a)===+(0,o.o)(i)}var i=null&&a},13663:(t,e,r)=>{r.d(e,{i:()=>o});var n=r(32850);function o(t,e,r){return(r??new n.i0(e)).format(t,"d")}},13747:(t,e,r)=>{r.d(e,{G:()=>o});var n=r(49164);function o(t){const e=(0,n.a)(t);const r=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));r.setUTCFullYear(e.getFullYear());return+t-+r}},13907:(t,e,r)=>{r.d(e,{G:()=>a,w:()=>o});var n=r(32850);function o(t,e,r){return(r??new n.i0(e)).format(t,"LLLL y")}const a=o},14151:(t,e,r)=>{r.d(e,{o:()=>n});function n(t,e,r,n){const{numberOfMonths:o=1}=r;const a=[];for(let r=0;r<o;r++){const o=n.addMonths(t,r);if(e&&o>e){break}a.push(o)}return a}},14618:(t,e,r)=>{r.d(e,{n:()=>o});var n=r(32850);function o(t,e,r){return(r??new n.i0(e)).format(t,"cccc")}},15298:(t,e,r)=>{r.d(e,{A:()=>l});var n=r(17437);var o=r(52457);var a=r(62246);var i=r(45538);var u=r(34419);function s(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var c=function t(e){var r=e.emptyStateImage,o=e.emptyStateImage2x,a=e.imageAltText,s=e.title,c=e.size,l=c===void 0?"medium":c,d=e.description,p=e.actions,v=e.removeBorder,h=v===void 0?true:v;return(0,n.Y)("div",{css:f.bannerWrapper(l,h,!!(0,u.O9)(r))},(0,n.Y)(i.A,{when:r},(0,n.Y)("img",{src:r,alt:a,srcSet:o?"".concat(o," 2x"):""})),(0,n.Y)("div",{css:f.messageWrapper(l)},(0,n.Y)("h5",{css:f.title(l)},s),(0,n.Y)(i.A,{when:d},(0,n.Y)("p",{css:f.description(l)},d)),(0,n.Y)(i.A,{when:p},(0,n.Y)("div",{css:f.actionWrapper(l)},p))))};const l=c;var d=true?{name:"vtxfgp",styles:"max-width:282px"}:0;var f={bannerWrapper:function t(e,r,a){return(0,n.AH)("display:grid;place-items:center;justify-content:center;gap:",o.YK[36],";padding:",a?"".concat(o.YK[16]," ").concat(o.YK[20]):"".concat(o.YK[20]),";",!r&&(0,n.AH)("border:1px solid ",o.I6.stroke.divider,";border-radius:",o.Vq.card,";background-color:",o.I6.background.white,";"+(true?"":0),true?"":0)," ",e==="small"&&(0,n.AH)("gap:",o.YK[12],";padding:",a?o.YK[12]:o.YK[16],";padding-bottom:",a?o.YK[24]:undefined,";"+(true?"":0),true?"":0)," & img{max-width:640px;width:100%;height:auto;border-radius:",e==="small"?o.Vq[6]:o.Vq[10],";overflow:hidden;object-position:center;object-fit:cover;",e==="small"&&d,";}"+(true?"":0),true?"":0)},messageWrapper:function t(e){return(0,n.AH)("display:flex;flex-direction:column;max-width:566px;width:100%;gap:",o.YK[12],";text-align:center;",e==="small"&&(0,n.AH)("gap:",o.YK[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},title:function t(e){return(0,n.AH)(a.I.heading5(),";color:",o.I6.text.primary,";",e==="small"&&(0,n.AH)(a.I.caption("medium"),";color:",o.I6.text.primary,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},description:function t(e){return(0,n.AH)(a.I.body(),";color:",o.I6.text.hints,";",e==="small"&&(0,n.AH)(a.I.tiny(),";color:",o.I6.text.hints,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},actionWrapper:function t(e){return(0,n.AH)("margin-top:",o.YK[20],";display:flex;justify-content:center;align-items:center;gap:",o.YK[12],";",e==="small"&&(0,n.AH)("gap:",o.YK[8],";margin-top:",o.YK[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},16331:(t,e,r)=>{r.d(e,{t:()=>o});var n=r(49164);function o(t,e){return(0,n.a)(t,e?.in).getMonth()}var a=null&&o},16660:(t,e,r)=>{r.d(e,{P:()=>c});var n=r(32850);var o=r(87331);var a=r(93397);var i=r(52044);var u=r(45157);var s=r(70684);function c(t,e,r=n.VA){const c=Array.isArray(e)?e:[e];const l=c.filter((t=>typeof t!=="function"));const d=l.some((e=>{if(typeof e==="boolean")return e;if(r.isDate(e)){return(0,i.R)(t,e,false,r)}if((0,s.Hg)(e,r)){return e.some((e=>(0,i.R)(t,e,false,r)))}if((0,s.oM)(e)){if(e.from&&e.to){return(0,u.G)(t,{from:e.from,to:e.to},r)}return false}if((0,s.OE)(e)){return(0,a.g)(t,e.dayOfWeek,r)}if((0,s.m4)(e)){const n=r.isAfter(e.before,e.after);if(n){return(0,u.G)(t,{from:r.addDays(e.after,1),to:r.addDays(e.before,-1)},r)}return(0,o.k)(t.from,e,r)||(0,o.k)(t.to,e,r)}if((0,s.RE)(e)||(0,s.Ue)(e)){return(0,o.k)(t.from,e,r)||(0,o.k)(t.to,e,r)}return false}));if(d){return true}const f=c.filter((t=>typeof t==="function"));if(f.length){let e=t.from;const n=r.differenceInCalendarDays(t.to,t.from);for(let t=0;t<=n;t++){if(f.some((t=>t(e)))){return true}e=r.addDays(e,1)}}return false}},16687:(t,e,r)=>{r.d(e,{N:()=>o});var n=r(74612);function o(t,e){const{selected:r,required:o,onSelect:a}=t;const[i,u]=(0,n.j)(r,a?r:undefined);const s=!a?i:r;const{isSameDay:c}=e;const l=t=>s?.some((e=>c(e,t)))??false;const{min:d,max:f}=t;const p=(t,e,r)=>{let n=[...s??[]];if(l(t)){if(s?.length===d){return}if(o&&s?.length===1){return}n=s?.filter((e=>!c(e,t)))}else{if(s?.length===f){n=[t]}else{n=[...n,t]}}if(!a){u(n)}a?.(n,t,e,r);return n};return{selected:s,select:p,isSelected:l}}},18525:(t,e,r)=>{r.d(e,{q:()=>o});var n=r(90865);class o extends Date{constructor(...t){super();if(t.length>1&&typeof t[t.length-1]==="string"){this.timeZone=t.pop()}this.internal=new Date;if(isNaN((0,n.Y)(this.timeZone,this))){this.setTime(NaN)}else{if(!t.length){this.setTime(Date.now())}else if(typeof t[0]==="number"&&(t.length===1||t.length===2&&typeof t[1]!=="number")){this.setTime(t[0])}else if(typeof t[0]==="string"){this.setTime(+new Date(t[0]))}else if(t[0]instanceof Date){this.setTime(+t[0])}else{this.setTime(+new Date(...t));s(this,NaN);i(this)}}}static tz(t,...e){return e.length?new o(...e,t):new o(Date.now(),t)}withTimeZone(t){return new o(+this,t)}getTimezoneOffset(){return-(0,n.Y)(this.timeZone,this)}setTime(t){Date.prototype.setTime.apply(this,arguments);i(this);return+this}[Symbol.for("constructDateFrom")](t){return new o(+new Date(t),this.timeZone)}}const a=/^(get|set)(?!UTC)/;Object.getOwnPropertyNames(Date.prototype).forEach((t=>{if(!a.test(t))return;const e=t.replace(a,"$1UTC");if(!o.prototype[e])return;if(t.startsWith("get")){o.prototype[t]=function(){return this.internal[e]()}}else{o.prototype[t]=function(){Date.prototype[e].apply(this.internal,arguments);u(this);return+this};o.prototype[e]=function(){Date.prototype[e].apply(this,arguments);i(this);return+this}}}));function i(t){t.internal.setTime(+t);t.internal.setUTCMinutes(t.internal.getUTCMinutes()-t.getTimezoneOffset())}function u(t){Date.prototype.setFullYear.call(t,t.internal.getUTCFullYear(),t.internal.getUTCMonth(),t.internal.getUTCDate());Date.prototype.setHours.call(t,t.internal.getUTCHours(),t.internal.getUTCMinutes(),t.internal.getUTCSeconds(),t.internal.getUTCMilliseconds());s(t)}function s(t){const e=(0,n.Y)(t.timeZone,t);const r=new Date(+t);r.setUTCHours(r.getUTCHours()-1);const o=-new Date(+t).getTimezoneOffset();const a=-new Date(+r).getTimezoneOffset();const i=o-a;const u=Date.prototype.getHours.apply(t)!==t.internal.getUTCHours();if(i&&u)t.internal.setUTCMinutes(t.internal.getUTCMinutes()+i);const s=o-e;if(s)Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+s);const c=(0,n.Y)(t.timeZone,t);const l=-new Date(+t).getTimezoneOffset();const d=l-c;const f=c!==e;const p=d-s;if(f&&p){Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+p);const e=(0,n.Y)(t.timeZone,t);const r=c-e;if(r){t.internal.setUTCMinutes(t.internal.getUTCMinutes()+r);Date.prototype.setUTCMinutes.call(t,Date.prototype.getUTCMinutes.call(t)+r)}}}},18711:(t,e,r)=>{r.d(e,{Y:()=>n});function n(t,e,r,n){const o=t[0];const a=t[t.length-1];const{ISOWeek:i,fixedWeeks:u,broadcastCalendar:s}=r??{};const{addDays:c,differenceInCalendarDays:l,differenceInCalendarMonths:d,endOfBroadcastWeek:f,endOfISOWeek:p,endOfMonth:v,endOfWeek:h,isAfter:m,startOfBroadcastWeek:g,startOfISOWeek:y,startOfWeek:b}=n;const w=s?g(o,n):i?y(o):b(o);const _=s?f(a,n):i?p(v(a)):h(v(a));const x=l(_,w);const A=d(a,o)+1;const Y=[];for(let t=0;t<=x;t++){const r=c(w,t);if(e&&m(r,e)){break}Y.push(r)}const k=s?35:42;const O=k*A;if(u&&Y.length<O){const t=O-Y.length;for(let e=0;e<t;e++){const t=c(Y[Y.length-1],1);Y.push(t)}}return Y}},19287:(t,e,r)=>{r.d(e,{k:()=>o});var n=r(97766);function o(t,e,r={}){const o=Object.entries(t).filter((([,t])=>t===true)).reduce(((t,[o])=>{if(r[o]){t.push(r[o])}else if(e[n.pL[o]]){t.push(e[n.pL[o]])}else if(e[n.wc[o]]){t.push(e[n.wc[o]])}return t}),[e[n.UI.Day]]);return o}},19330:(t,e,r)=>{r.d(e,{i:()=>a});var n=r(41594);var o=r(51409);function a(t){const{components:e}=(0,o.w)();return n.createElement(e.Button,{...t})}},19502:(t,e,r)=>{r.d(e,{A:()=>L});var n=r(38919);var o=r(942);var a=r(41502);var i=r(52457);var u=r(62246);var s=r(30015);var c=r(94083);var l=r(17437);var d=r(67375);var f=r(67901);var p=r(35975);var v=r(53429);var h=r(41594);var m=r.n(h);var g=r(47855);var y=r(6502);function b(t){"@babel/helpers - typeof";return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}var w=["css"];function _(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=A(t,"string");return"symbol"==b(e)?e:e+""}function A(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function Y(){return Y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Y.apply(null,arguments)}function k(t,e){if(null==t)return{};var r,n,o=O(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}function S(t,e){return T(t)||M(t,e)||E(t,e)||I()}function I(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(t,e){if(t){if("string"==typeof t)return C(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?C(t,e):void 0}}function C(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function M(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function T(t){if(Array.isArray(t))return t}function j(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var D=function t(e){var r=e.label,i=e.field,u=e.fieldState,c=e.interval,m=c===void 0?30:c,b=e.disabled,x=e.loading,A=e.placeholder,O=e.helpText,I=e.isClearable,E=I===void 0?true:I;var C=(0,h.useState)(false),M=S(C,2),T=M[0],j=M[1];var D=(0,h.useRef)(null);var L=(0,h.useMemo)((function(){var t=(0,d["default"])((0,f["default"])(new Date,0),0);var e=(0,d["default"])((0,f["default"])(new Date,23),59);var r=(0,p.A)({start:t,end:e},{step:m});return r.map((function(t){return(0,v["default"])(t,a.Bd.hoursMinutes)}))}),[m]);var N=(0,s.t)({isOpen:T,isDropdown:true}),H=N.triggerRef,W=N.triggerWidth,K=N.position,B=N.popoverRef;var z=(0,g.v)({options:L.map((function(t){return{label:t,value:t}})),isOpen:T,selectedValue:i.value,onSelect:function t(e){i.onChange(e.value);j(false)},onClose:function t(){return j(false)}}),U=z.activeIndex,R=z.setActiveIndex;(0,h.useEffect)((function(){if(T&&U>=0&&D.current){D.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[T,U]);return(0,l.Y)(y.A,{label:r,field:i,fieldState:u,disabled:b,loading:x,placeholder:A,helpText:O},(function(t){var e;var r=t.css,u=k(t,w);return(0,l.Y)("div",null,(0,l.Y)("div",{css:P.wrapper,ref:H},(0,l.Y)("input",Y({},u,{ref:i.ref,css:[r,P.input,true?"":0,true?"":0],type:"text",onClick:function t(e){e.stopPropagation();j((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();j((function(t){return!t}))}if(e.key==="Tab"){j(false)}},value:(e=i.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=e.target.value;i.onChange(r)},autoComplete:"off","data-input":true})),(0,l.Y)(o.A,{name:"clock",width:32,height:32,style:P.icon}),E&&i.value&&(0,l.Y)(n.A,{variant:"text",buttonCss:P.clearButton,onClick:function t(){return i.onChange("")}},(0,l.Y)(o.A,{name:"times",width:12,height:12}))),(0,l.Y)(s.Z,{isOpen:T,onClickOutside:function t(){return j(false)},onEscape:function t(){return j(false)}},(0,l.Y)("div",{css:[P.popover,_(_(_({},a.V8?"right":"left",K.left),"top",K.top),"maxWidth",W),true?"":0,true?"":0],ref:B},(0,l.Y)("ul",{css:P.list},L.map((function(t,e){return(0,l.Y)("li",{key:e,css:P.listItem,ref:U===e?D:null,"data-active":U===e},(0,l.Y)("button",{type:"button",css:P.itemButton,onClick:function e(){i.onChange(t);j(false)},onMouseOver:function t(){return R(e)},onMouseLeave:function t(){return e!==U&&R(-1)},onFocus:function t(){return R(e)}},t))}))))))}))};const L=D;var P={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,l.AH)("&[data-input]{padding-left:",i.YK[40],";}"+(true?"":0),true?"":0),icon:(0,l.AH)("position:absolute;top:50%;left:",i.YK[8],";transform:translateY(-50%);color:",i.I6.icon["default"],";"+(true?"":0),true?"":0),popover:(0,l.AH)("position:absolute;width:100%;background-color:",i.I6.background.white,";box-shadow:",i.r7.popover,";height:380px;overflow-y:auto;border-radius:",i.Vq[6],";"+(true?"":0),true?"":0),list:true?{name:"v5al3",styles:"list-style:none;padding:0;margin:0"}:0,listItem:(0,l.AH)("width:100%;height:40px;cursor:pointer;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;&[data-active='true']{background-color:",i.I6.background.hover,";}:hover{background-color:",i.I6.background.hover,";}"+(true?"":0),true?"":0),itemButton:(0,l.AH)(c.x.resetButton,";",u.I.body(),";margin:",i.YK[4]," ",i.YK[12],";width:100%;height:100%;&:focus,&:active,&:hover{background:none;color:",i.I6.text.primary,";}"+(true?"":0),true?"":0),clearButton:(0,l.AH)("position:absolute;top:50%;right:",i.YK[4],";transform:translateY(-50%);width:32px;height:32px;",c.x.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",i.Vq[2],";:hover{background-color:",i.I6.background.hover,";}"+(true?"":0),true?"":0)}},19552:(t,e,r)=>{r.d(e,{f:()=>b});var n=r(52457);var o=r(5396);var a=r(17437);var i=r(41594);var u=r.n(i);function s(t){return d(t)||l(t)||v(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return h(t)}function f(t,e){return g(t)||m(t,e)||v(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function g(t){if(Array.isArray(t))return t}function y(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var b=u().forwardRef((function(t,e){var r=t.src,n=t.width,u=t.height,c=t.brushSize,l=t.trackStack,d=t.pointer,p=t.setTrackStack,v=t.setPointer;var h=(0,i.useState)(false),m=f(h,2),g=m[0],y=m[1];var b=(0,i.useState)({x:0,y:0}),_=f(b,2),x=_[0],A=_[1];var Y=(0,i.useRef)(null);var k=function t(r){var n=(0,o.dX)(e),a=n.canvas,i=n.context;if(!a||!i){return}var u=a.getBoundingClientRect();var s=(r.clientX-u.left)*(a.width/u.width);var c=(r.clientY-u.top)*(a.height/u.height);i.globalCompositeOperation="destination-out";i.beginPath();i.moveTo(s,c);y(true);A({x:s,y:c})};var O=function t(r){var n=(0,o.dX)(e),a=n.canvas,i=n.context;if(!a||!i||!Y.current){return}var u=a.getBoundingClientRect();var s={x:(r.clientX-u.left)*(a.width/u.width),y:(r.clientY-u.top)*(a.height/u.height)};if(g){(0,o.kd)(i,s)}Y.current.style.left="".concat(s.x,"px");Y.current.style.top="".concat(s.y,"px")};var S=function t(r){var n=(0,o.dX)(e),a=n.canvas,i=n.context;if(!i||!a){return}y(false);i.closePath();var u=a.getBoundingClientRect();var c={x:(r.clientX-u.left)*(a.width/u.width),y:(r.clientY-u.top)*(a.height/u.height)};if((0,o.KG)(x,c)===0){(0,o.kd)(i,{x:c.x+1,y:c.y+1})}p((function(t){var e=t.slice(0,d);return[].concat(s(e),[i.getImageData(0,0,1024,1024)])}));v((function(t){return t+1}))};var I=function t(){var n=(0,o.dX)(e),a=n.canvas,i=n.context;if(!a||!i){return}var u=new Image;u.src=r;u.onload=function(){i.clearRect(0,0,a.width,a.height);var t=u.width/u.height;var e=a.width/a.height;var r;var n;if(e>t){n=a.height;r=a.height*t}else{r=a.width;n=a.width/t}var o=(a.width-r)/2;var s=(a.height-n)/2;i.drawImage(u,o,s,r,n);if(l.length===0){p([i.getImageData(0,0,a.width,a.height)])}};i.lineJoin="round";i.lineCap="round"};var E=function t(){if(!Y.current){return}document.body.style.cursor="none";Y.current.style.display="block"};var C=function t(){if(!Y.current){return}document.body.style.cursor="auto";Y.current.style.display="none"};(0,i.useEffect)((function(){I()}),[]);return(0,a.Y)("div",{css:w.wrapper},(0,a.Y)("canvas",{ref:e,width:n,height:u,onMouseDown:k,onMouseMove:O,onMouseUp:S,onMouseEnter:E,onMouseLeave:C}),(0,a.Y)("div",{ref:Y,css:w.customCursor(c)}))}));var w={wrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,a.AH)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",n.Vq.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",n.I6.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",n.fE.highest,";display:none;"+(true?"":0),true?"":0)}}},20207:(t,e,r)=>{r.d(e,{F:()=>i});var n=r(26496);var o=r(40525);var a=r(49164);function i(t,e){const r=(0,a.a)(t,e?.in);const i=(0,n.m)(r,(0,o.D)(r));const u=i+1;return u}var u=null&&i},21289:(t,e,r)=>{r.d(e,{J:()=>n});function n(t,e){let{startMonth:r,endMonth:n}=t;const{startOfYear:o,startOfDay:a,startOfMonth:i,endOfMonth:u,addYears:s,endOfYear:c,newDate:l,today:d}=e;const{fromYear:f,toYear:p,fromMonth:v,toMonth:h}=t;if(!r&&v){r=v}if(!r&&f){r=e.newDate(f,0,1)}if(!n&&h){n=h}if(!n&&p){n=l(p,11,31)}const m=t.captionLayout==="dropdown"||t.captionLayout==="dropdown-years";if(r){r=i(r)}else if(f){r=l(f,0,1)}else if(!r&&m){r=o(s(t.today??d(),-100))}if(n){n=u(n)}else if(p){n=l(p,11,31)}else if(!n&&m){n=c(t.today??d())}return[r?a(r):r,n?a(n):n]}},21309:(t,e,r)=>{r.d(e,{t:()=>o});var n=r(74084);function o(t,e,r){const[o,a]=(0,n.x)(r?.in,t,e);return o.getFullYear()===a.getFullYear()&&o.getMonth()===a.getMonth()}var a=null&&o},21337:(t,e,r)=>{r.d(e,{I:()=>a});const n=5;const o=4;function a(t,e){const r=e.startOfMonth(t);const a=r.getDay()>0?r.getDay():7;const i=e.addDays(t,-a+1);const u=e.addDays(i,n*7-1);const s=e.getMonth(t)===e.getMonth(u)?n:o;return s}},21607:(t,e,r)=>{r.d(e,{A:()=>w});var n=r(52457);var o=r(62246);var a=r(7443);var i=r(94083);var u=r(47849);var s=r(17437);var c=r(41594);var l=r.n(c);var d=r(6502);function f(t,e){return g(t)||m(t,e)||v(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function g(t){if(Array.isArray(t))return t}function y(t,e,r,n){if(!e.current){return 0}var o=e.current.getBoundingClientRect();var a=o.width;var i=t-o.left;var u=Math.max(0,Math.min(i,a));var s=u/a*100;var c=Math.floor(r+s/100*(n-r));return c}var b=function t(e){var r=e.field,n=e.fieldState,o=e.label,i=e.min,l=i===void 0?0:i,p=e.max,v=p===void 0?100:p,h=e.isMagicAi,m=h===void 0?false:h,g=e.hasBorder,b=g===void 0?false:g;var w=(0,c.useRef)(null);var x=(0,c.useState)(r.value),A=f(x,2),Y=A[0],k=A[1];var O=(0,c.useRef)(null);var S=(0,c.useRef)(null);var I=(0,a.d)(Y);(0,c.useEffect)((function(){r.onChange(I)}),[I,r.onChange]);(0,c.useEffect)((function(){var t=false;var e=function e(r){if(r.target!==S.current){return}t=true;document.body.style.userSelect="none"};var r=function e(r){if(!t||!O.current){return}k(y(r.clientX,O,l,v))};var n=function e(){t=false;document.body.style.userSelect="auto"};window.addEventListener("mousedown",e);window.addEventListener("mousemove",r);window.addEventListener("mouseup",n);return function(){window.removeEventListener("mousedown",e);window.removeEventListener("mousemove",r);window.removeEventListener("mouseup",n)}}),[l,v]);var E=(0,c.useMemo)((function(){return Math.floor((Y-l)/(v-l)*100)}),[Y,l,v]);return(0,s.Y)(d.A,{field:r,fieldState:n,label:o,isMagicAi:m},(function(){return(0,s.Y)("div",{css:_.wrapper(b)},(0,s.Y)("div",{css:_.track,ref:O,onKeyDown:u.lQ,onClick:function t(e){k(y(e.clientX,O,l,v))}},(0,s.Y)("div",{css:_.fill,style:{width:"".concat(E,"%")}}),(0,s.Y)("div",{css:_.thumb(m),style:{left:"".concat(E,"%")},ref:S})),(0,s.Y)("input",{type:"text",css:_.input,value:String(Y),onChange:function t(e){k(Number(e.target.value))},ref:w,onFocus:function t(){var e;(e=w.current)===null||e===void 0||e.select()}}))}))};const w=b;var _={wrapper:function t(e){return(0,s.AH)("display:grid;grid-template-columns:1fr 45px;gap:",n.YK[20],";align-items:center;",e&&(0,s.AH)("border:1px solid ",n.I6.stroke.disable,";border-radius:",n.Vq[6],";padding:",n.YK[12]," ",n.YK[10]," ",n.YK[12]," ",n.YK[16],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},track:(0,s.AH)("position:relative;height:4px;background-color:",n.I6.bg.gray20,";border-radius:",n.Vq[50],";width:100%;flex-shrink:0;cursor:pointer;"+(true?"":0),true?"":0),fill:(0,s.AH)("position:absolute;left:0;top:0;height:100%;background:",n.I6.ai.gradient_1,";width:50%;border-radius:",n.Vq[50],";"+(true?"":0),true?"":0),thumb:function t(e){return(0,s.AH)("position:absolute;top:50%;transform:translate(-50%, -50%);width:20px;height:20px;border-radius:",n.Vq.circle,";&::before{content:'';position:absolute;top:50%;left:50%;width:8px;height:8px;transform:translate(-50%, -50%);border-radius:",n.Vq.circle,";background-color:",n.I6.background.white,";cursor:pointer;}",e&&(0,s.AH)("background:",n.I6.ai.gradient_1,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},input:(0,s.AH)(o.I.caption("medium"),";height:32px;border:1px solid ",n.I6.stroke.border,";border-radius:",n.Vq[6],";text-align:center;color:",n.I6.text.primary,";&:focus-visible{",i.x.inputFocus,";}"+(true?"":0),true?"":0)}},21721:(t,e,r)=>{r.d(e,{$:()=>n});function n(t){return t instanceof Date||typeof t==="object"&&Object.prototype.toString.call(t)==="[object Date]"}var o=null&&n},21843:(t,e,r)=>{r.d(e,{P:()=>a});var n=r(27256);var o=r(49164);function a(t,e){const r=(0,o.a)(t,e?.in);const a=r.getFullYear();const i=r.getMonth();const u=(0,n.w)(r,0);u.setFullYear(a,i+1,0);u.setHours(0,0,0,0);return u.getDate()}var i=null&&a},22471:(t,e,r)=>{var n=r(90865);function o(t,e){const r=[];const n=new Date(e.start);n.setUTCSeconds(0,0);const o=new Date(e.end);o.setUTCSeconds(0,0);const a=+o;let i=tzOffset(t,n);while(+n<a){n.setUTCMonth(n.getUTCMonth()+1);const e=tzOffset(t,n);if(e!=i){const e=new Date(n);e.setUTCMonth(e.getUTCMonth()-1);const o=+n;i=tzOffset(t,e);while(+e<o){e.setUTCDate(e.getUTCDate()+1);const n=tzOffset(t,e);if(n!=i){const n=new Date(e);n.setUTCDate(n.getUTCDate()-1);const o=+e;i=tzOffset(t,n);while(+n<o){n.setUTCHours(n.getUTCHours()+1);const e=tzOffset(t,n);if(e!==i){r.push({date:new Date(n),change:e-i,offset:e})}i=e}}i=n}}i=e}return r}},22507:(t,e,r)=>{r.d(e,{P:()=>o});var n=r(41594);function o(t){const{calendarMonth:e,displayIndex:r,...o}=t;return n.createElement("div",{...o})}},22596:(t,e,r)=>{r.d(e,{P:()=>o});var n=r(74084);function o(t,e){const[r,o]=(0,n.x)(t,e.start,e.end);return{start:r,end:o}}},23462:(t,e,r)=>{r.d(e,{P:()=>o});var n=r(32850);function o(t,e,r,o){let a=(o??new n.i0(r)).format(t,"PPPP");if(e?.today){a=`Today, ${a}`}return a}},24595:(t,e,r)=>{r.d(e,{C:()=>n});function n(t){const e=[];return t.reduce(((t,e)=>[...t,...e.weeks]),e)}},25631:(t,e,r)=>{r.d(e,{A:()=>i});var n=r(76314);var o=r.n(n);var a=o()((function(t){return t[1]}));a.push([t.id,'/* Variables declaration */\n/* prettier-ignore */\n.rdp-root {\n  --rdp-accent-color: blue; /* The accent color used for selected days and UI elements. */\n  --rdp-accent-background-color: #f0f0ff; /* The accent background color used for selected days and UI elements. */\n\n  --rdp-day-height: 44px; /* The height of the day cells. */\n  --rdp-day-width: 44px; /* The width of the day cells. */\n  \n  --rdp-day_button-border-radius: 100%; /* The border radius of the day cells. */\n  --rdp-day_button-border: 2px solid transparent; /* The border of the day cells. */\n  --rdp-day_button-height: 42px; /* The height of the day cells. */\n  --rdp-day_button-width: 42px; /* The width of the day cells. */\n  \n  --rdp-selected-border: 2px solid var(--rdp-accent-color); /* The border of the selected days. */\n  --rdp-disabled-opacity: 0.5; /* The opacity of the disabled days. */\n  --rdp-outside-opacity: 0.75; /* The opacity of the days outside the current month. */\n  --rdp-today-color: var(--rdp-accent-color); /* The color of the today\'s date. */\n  \n  --rdp-dropdown-gap: 0.5rem;/* The gap between the dropdowns used in the month captons. */\n  \n  --rdp-months-gap: 2rem; /* The gap between the months in the multi-month view. */\n  \n  --rdp-nav_button-disabled-opacity: 0.5; /* The opacity of the disabled navigation buttons. */\n  --rdp-nav_button-height: 2.25rem; /* The height of the navigation buttons. */\n  --rdp-nav_button-width: 2.25rem; /* The width of the navigation buttons. */\n  --rdp-nav-height: 2.75rem; /* The height of the navigation bar. */\n  \n  --rdp-range_middle-background-color: var(--rdp-accent-background-color); /* The color of the background for days in the middle of a range. */\n  --rdp-range_middle-color: inherit;/* The color of the range text. */\n  \n  --rdp-range_start-color: white; /* The color of the range text. */\n  --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%); /* Used for the background of the start of the selected range. */\n  --rdp-range_start-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the start of the selected range. */\n  \n  --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%); /* Used for the background of the end of the selected range. */\n  --rdp-range_end-color: white;/* The color of the range text. */\n  --rdp-range_end-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the end of the selected range. */\n  \n  --rdp-week_number-border-radius: 100%; /* The border radius of the week number. */\n  --rdp-week_number-border: 2px solid transparent; /* The border of the week number. */\n  \n  --rdp-week_number-height: var(--rdp-day-height); /* The height of the week number cells. */\n  --rdp-week_number-opacity: 0.75; /* The opacity of the week number. */\n  --rdp-week_number-width: var(--rdp-day-width); /* The width of the week number cells. */\n  --rdp-weeknumber-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-weekday-opacity: 0.75; /* The opacity of the weekday. */\n  --rdp-weekday-padding: 0.5rem 0rem; /* The padding of the weekday. */\n  --rdp-weekday-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-gradient-direction: 90deg;\n\n  --rdp-animation_duration: 0.3s;\n  --rdp-animation_timing: cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.rdp-root[dir="rtl"] {\n  --rdp-gradient-direction: -90deg;\n}\n\n.rdp-root[data-broadcast-calendar="true"] {\n  --rdp-outside-opacity: unset;\n}\n\n/* Root of the component. */\n.rdp-root {\n  position: relative; /* Required to position the navigation toolbar. */\n  box-sizing: border-box;\n}\n\n.rdp-root * {\n  box-sizing: border-box;\n}\n\n.rdp-day {\n  width: var(--rdp-day-width);\n  height: var(--rdp-day-height);\n  text-align: center;\n}\n\n.rdp-day_button {\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  justify-content: center;\n  align-items: center;\n  display: flex;\n\n  width: var(--rdp-day_button-width);\n  height: var(--rdp-day_button-height);\n  border: var(--rdp-day_button-border);\n  border-radius: var(--rdp-day_button-border-radius);\n}\n\n.rdp-day_button:disabled {\n  cursor: revert;\n}\n\n.rdp-caption_label {\n  z-index: 1;\n\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n\n  white-space: nowrap;\n  border: 0;\n}\n\n.rdp-dropdown:focus-visible ~ .rdp-caption_label {\n  outline: 5px auto Highlight;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\n.rdp-button_next,\n.rdp-button_previous {\n  border: none;\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  appearance: none;\n\n  width: var(--rdp-nav_button-width);\n  height: var(--rdp-nav_button-height);\n}\n\n.rdp-button_next:disabled,\n.rdp-button_next[aria-disabled="true"],\n.rdp-button_previous:disabled,\n.rdp-button_previous[aria-disabled="true"] {\n  cursor: revert;\n\n  opacity: var(--rdp-nav_button-disabled-opacity);\n}\n\n.rdp-chevron {\n  display: inline-block;\n  fill: var(--rdp-accent-color);\n}\n\n.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n  transform-origin: 50%;\n}\n\n.rdp-dropdowns {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: var(--rdp-dropdown-gap);\n}\n.rdp-dropdown {\n  z-index: 2;\n\n  /* Reset */\n  opacity: 0;\n  appearance: none;\n  position: absolute;\n  inset-block-start: 0;\n  inset-block-end: 0;\n  inset-inline-start: 0;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: inherit;\n  border: none;\n  line-height: inherit;\n}\n\n.rdp-dropdown_root {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n}\n\n.rdp-dropdown_root[data-disabled="true"] .rdp-chevron {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-month_caption {\n  display: flex;\n  align-content: center;\n  height: var(--rdp-nav-height);\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-months {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: var(--rdp-months-gap);\n  max-width: fit-content;\n}\n\n.rdp-month_grid {\n  border-collapse: collapse;\n}\n\n.rdp-nav {\n  position: absolute;\n  inset-block-start: 0;\n  inset-inline-end: 0;\n\n  display: flex;\n  align-items: center;\n\n  height: var(--rdp-nav-height);\n}\n\n.rdp-weekday {\n  opacity: var(--rdp-weekday-opacity);\n  padding: var(--rdp-weekday-padding);\n  font-weight: 500;\n  font-size: smaller;\n  text-align: var(--rdp-weekday-text-align);\n  text-transform: var(--rdp-weekday-text-transform);\n}\n\n.rdp-week_number {\n  opacity: var(--rdp-week_number-opacity);\n  font-weight: 400;\n  font-size: small;\n  height: var(--rdp-week_number-height);\n  width: var(--rdp-week_number-width);\n  border: var(--rdp-week_number-border);\n  border-radius: var(--rdp-week_number-border-radius);\n  text-align: var(--rdp-weeknumber-text-align);\n}\n\n/* DAY MODIFIERS */\n.rdp-today:not(.rdp-outside) {\n  color: var(--rdp-today-color);\n}\n\n.rdp-selected {\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-selected .rdp-day_button {\n  border: var(--rdp-selected-border);\n}\n\n.rdp-outside {\n  opacity: var(--rdp-outside-opacity);\n}\n\n.rdp-disabled {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-hidden {\n  visibility: hidden;\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_start {\n  background: var(--rdp-range_start-background);\n}\n\n.rdp-range_start .rdp-day_button {\n  background-color: var(--rdp-range_start-date-background-color);\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_middle {\n  background-color: var(--rdp-range_middle-background-color);\n}\n\n.rdp-range_middle .rdp-day_button {\n  border-color: transparent;\n  border: unset;\n  border-radius: unset;\n  color: var(--rdp-range_middle-color);\n}\n\n.rdp-range_end {\n  background: var(--rdp-range_end-background);\n  color: var(--rdp-range_end-color);\n}\n\n.rdp-range_end .rdp-day_button {\n  color: var(--rdp-range_start-color);\n  background-color: var(--rdp-range_end-date-background-color);\n}\n\n.rdp-range_start.rdp-range_end {\n  background: revert;\n}\n\n.rdp-focusable {\n  cursor: pointer;\n}\n\n@keyframes rdp-slide_in_left {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(0);\n  }\n}\n\n@keyframes rdp-slide_in_right {\n  0% {\n    transform: translateX(100%);\n  }\n  100% {\n    transform: translateX(0);\n  }\n}\n\n@keyframes rdp-slide_out_left {\n  0% {\n    transform: translateX(0);\n  }\n  100% {\n    transform: translateX(-100%);\n  }\n}\n\n@keyframes rdp-slide_out_right {\n  0% {\n    transform: translateX(0);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n.rdp-weeks_before_enter {\n  animation: rdp-slide_in_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_before_exit {\n  animation: rdp-slide_out_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_after_enter {\n  animation: rdp-slide_in_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-weeks_after_exit {\n  animation: rdp-slide_out_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir="rtl"] .rdp-weeks_after_enter {\n  animation: rdp-slide_in_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir="rtl"] .rdp-weeks_before_exit {\n  animation: rdp-slide_out_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir="rtl"] .rdp-weeks_before_enter {\n  animation: rdp-slide_in_right var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-root[dir="rtl"] .rdp-weeks_after_exit {\n  animation: rdp-slide_out_left var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n@keyframes rdp-fade_in {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes rdp-fade_out {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.rdp-caption_after_enter {\n  animation: rdp-fade_in var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_after_exit {\n  animation: rdp-fade_out var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_before_enter {\n  animation: rdp-fade_in var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n\n.rdp-caption_before_exit {\n  animation: rdp-fade_out var(--rdp-animation_duration)\n    var(--rdp-animation_timing) forwards;\n}\n',""]);const i=a},26418:(t,e,r)=>{r.r(e);r.d(e,{default:()=>xr});var n=r(17437);var o=r(97665);var a=r(87496);var i=r(12470);var u=r(41594);var s=r.n(u);var c=r(49785);var l=r(47767);var d=r(52457);var f=r(62246);var p=r(45538);var v=r(942);var h=r(1438);var m=s().forwardRef((function(t,e){var r=t.children,o=t.className,a=t.bordered,i=a===void 0?false:a,u=t.wrapperCss;return(0,n.Y)("div",{ref:e,className:o,css:[b.wrapper(i),u,true?"":0,true?"":0]},r)}));m.displayName="Box";var g=s().forwardRef((function(t,e){var r=t.children,o=t.className,a=t.separator,i=a===void 0?false:a,u=t.tooltip;return(0,n.Y)("div",{ref:e,className:o,css:b.title(i)},(0,n.Y)("span",null,r),(0,n.Y)(p.A,{when:u},(0,n.Y)(h.A,{content:u},(0,n.Y)(v.A,{name:"info",width:20,height:20}))))}));g.displayName="BoxTitle";var y=s().forwardRef((function(t,e){var r=t.children,o=t.className;return(0,n.Y)("div",{ref:e,className:o,css:b.subtitle},(0,n.Y)("span",null,r))}));y.displayName="BoxSubtitle";var b={wrapper:function t(e){return(0,n.AH)("background-color:",d.I6.background.white,";border-radius:",d.Vq[8],";padding:",d.YK[12]," ",d.YK[20]," ",d.YK[20],";",e&&(0,n.AH)("border:1px solid ",d.I6.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},title:function t(e){return(0,n.AH)(f.I.body("medium"),";color:",d.I6.text.title,";display:flex;gap:",d.YK[4],";align-items:center;",e&&(0,n.AH)("border-bottom:1px solid ",d.I6.stroke.divider,";padding:",d.YK[12]," ",d.YK[20],";"+(true?"":0),true?"":0)," &>div{height:20px;svg{color:",d.I6.icon.hints,";}}&>span{display:inline-block;}"+(true?"":0),true?"":0)},subtitle:(0,n.AH)(f.I.caption(),";color:",d.I6.text.hints,";"+(true?"":0),true?"":0)};var w=r(4704);var _=r(27793);var x=r(15298);var A=r(41502);var Y=r(98880);var k=r(94083);var O=r(47849);var S=r(7443);var I=r(30015);var E=r(47855);var C=r(37567);var M=r(8798);var T=r(6502);function j(t){"@babel/helpers - typeof";return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}var D=["css"];function L(t,e,r){return(e=P(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function P(t){var e=N(t,"string");return"symbol"==j(e)?e:e+""}function N(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function H(){return H=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},H.apply(null,arguments)}function W(t,e){if(null==t)return{};var r,n,o=K(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function K(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}function B(t){return R(t)||U(t)||G(t)||z()}function z(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function U(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function R(t){if(Array.isArray(t))return V(t)}function F(t,e){return $(t)||Q(t,e)||G(t,e)||q()}function q(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function G(t,e){if(t){if("string"==typeof t)return V(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?V(t,e):void 0}}function V(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Q(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function $(t){if(Array.isArray(t))return t}function Z(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var X=function t(e){var r;var o=e.field,a=e.fieldState,s=e.options,c=e.onChange,l=c===void 0?O.lQ:c,d=e.handleSearchOnChange,h=e.label,m=e.placeholder,g=m===void 0?"":m,y=e.disabled,b=e.readOnly,_=e.loading,k=e.isSearchable,j=k===void 0?false:k,P=e.helpText;var N=(r=o.value)!==null&&r!==void 0?r:[];var K=N.map((function(t){return String(t.id)}));var z=(0,u.useRef)(null);var U=(0,u.useState)(false),R=F(U,2),q=R[0],G=R[1];var V=(0,u.useState)(""),Q=F(V,2),$=Q[0],Z=Q[1];var X=(0,S.d)($);var J=s.filter((function(t){return t.title.toLowerCase().includes(X.toLowerCase())&&!K.includes(String(t.id))}));(0,u.useEffect)((function(){if(d){d(X)}else{}}),[X,d]);var et=(0,I.t)({isOpen:q,isDropdown:true,dependencies:[J.length]}),rt=et.triggerRef,nt=et.triggerWidth,ot=et.position,at=et.popoverRef;var it=(0,E.v)({options:J.map((function(t){return{label:t.title,value:t}})),isOpen:q,selectedValue:null,onSelect:function t(e){o.onChange([].concat(B(N),[e.value]));l([].concat(B(N),[e.value]));G(false);Z("")},onClose:function t(){G(false);Z("");Z("")}}),ut=it.activeIndex,st=it.setActiveIndex;var ct=function t(e){if(Array.isArray(N)){var r=N.filter((function(t){return t.id!==e}));o.onChange(r);l(r)}};(0,u.useEffect)((function(){if(q&&ut>=0&&z.current){z.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[q,ut]);return(0,n.Y)(T.A,{fieldState:a,field:o,label:h,disabled:y,readOnly:b,helpText:P},(function(t){var e=t.css,r=W(t,D);return(0,n.Y)("div",{css:tt.mainWrapper},(0,n.Y)("div",{ref:rt},(0,n.Y)("div",{css:tt.inputWrapper},(0,n.Y)("div",{css:tt.leftIcon},(0,n.Y)(v.A,{name:"search",width:24,height:24})),(0,n.Y)("input",H({},r,{onClick:function t(e){e.stopPropagation();G((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();G(true)}if(e.key==="Tab"){G(false)}},className:"tutor-input-field",css:[e,tt.input,true?"":0,true?"":0],autoComplete:"off",readOnly:b||!j,placeholder:g,value:$,onChange:function t(e){Z(e.target.value)}})))),(0,n.Y)(p.A,{when:N.length>0,fallback:(0,n.Y)(p.A,{when:!_,fallback:(0,n.Y)(w.YE,null)},(0,n.Y)(x.A,{size:"small",emptyStateImage:M.A,emptyStateImage2x:C.A,imageAltText:(0,i.__)("Illustration of a no course selected","tutor"),title:(0,i.__)("No course selected","tutor"),description:(0,i.__)("Select a course to add as a prerequisite.","tutor")}))},(0,n.Y)("div",{css:tt.courseList},(0,n.Y)(Y.A,{each:N},(function(t,e){return(0,n.Y)("div",{key:e,css:tt.courseCard({onPopover:false})},(0,n.Y)("div",{css:tt.imageWrapper},(0,n.Y)("img",{src:t.image,alt:t.title,css:tt.image})),(0,n.Y)("div",{css:tt.cardContent},(0,n.Y)("span",{css:tt.cardTitle},t.title),(0,n.Y)("p",{css:f.I.tiny()},t.id)),(0,n.Y)("button",{type:"button",css:tt.removeButton,"data-visually-hidden":true,onClick:function e(){return ct(t.id)}},(0,n.Y)(v.A,{name:"times",width:14,height:14})))})))),(0,n.Y)(I.Z,{isOpen:q,onClickOutside:function t(){G(false);Z("")},onEscape:function t(){G(false);Z("")}},(0,n.Y)("div",{css:[tt.optionsWrapper,L(L(L({},A.V8?"right":"left",ot.left),"top",ot.top),"maxWidth",nt),true?"":0,true?"":0],ref:at},(0,n.Y)("ul",{css:[tt.options,true?"":0,true?"":0]},(0,n.Y)(p.A,{when:J.length>0,fallback:(0,n.Y)("li",{css:tt.emptyOption},(0,n.Y)("p",null,(0,i.__)("No courses found","tutor")))},(0,n.Y)(Y.A,{each:J},(function(t,e){return(0,n.Y)("li",{key:t.id,ref:ut===e?z:null},(0,n.Y)("button",{type:"button",css:tt.courseCard({onPopover:true,isActive:ut===e}),onClick:function e(){o.onChange([].concat(B(N),[t]));l([].concat(B(N),[t]));G(false);Z("")},onMouseOver:function t(){return st(e)},onMouseLeave:function t(){return e!==ut&&st(-1)},onFocus:function t(){return st(e)},"aria-selected":ut===e},(0,n.Y)("div",{css:tt.imageWrapper},(0,n.Y)("img",{src:t.image,alt:t.title,css:tt.image})),(0,n.Y)("div",{css:tt.cardContent},(0,n.Y)("span",{css:tt.cardTitle},t.title),(0,n.Y)("p",{css:f.I.tiny()},t.id))))})))))))}))};const J=X;var tt={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:true?{name:"1712kcx",styles:"width:100%;display:flex;justify-content:space-between;align-items:center;position:relative"}:0,leftIcon:(0,n.AH)("position:absolute;left:",d.YK[8],";top:",d.YK[8],";color:",d.I6.icon["default"],";display:flex;"+(true?"":0),true?"":0),input:(0,n.AH)(f.I.body(),";width:100%;padding-right:",d.YK[32],";padding-left:",d.YK[36],";",k.x.textEllipsis,";border:1px solid ",d.I6.stroke["default"],";&.tutor-input-field{padding-right:",d.YK[32],";padding-left:",d.YK[36],";}"+(true?"":0),true?"":0),courseList:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";max-height:256px;height:100%;margin-top:",d.YK[8],";",k.x.overflowYAuto,";"+(true?"":0),true?"":0),optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:(0,n.AH)("z-index:",d.fE.dropdown,";background-color:",d.I6.background.white,";list-style-type:none;box-shadow:",d.r7.popover,";padding:",d.YK[4]," 0;margin:0;max-height:400px;border-radius:",d.Vq[6],";",k.x.overflowYAuto,";"+(true?"":0),true?"":0),courseCard:function t(e){var r=e.onPopover,o=r===void 0?false:r,a=e.isActive,i=a===void 0?false:a;return(0,n.AH)(k.x.resetButton,";width:100%;cursor:",o?"pointer":"default",";position:relative;padding:",d.YK[8],";border:1px solid transparent;border-radius:",d.Vq.card,";display:grid;grid-template-columns:76px 1fr;gap:",d.YK[10],";align-items:center;background-color:",d.I6.background.white,";[data-visually-hidden]{opacity:0;}",i&&(0,n.AH)("background-color:",d.I6.background.hover,";border-color:",d.I6.stroke["default"],";"+(true?"":0),true?"":0)," &:hover{background-color:",d.I6.background.hover,";",!o&&(0,n.AH)("background-color:",d.I6.background.white,";border-color:",d.I6.stroke["default"],";"+(true?"":0),true?"":0)," [data-visually-hidden]{opacity:1;}}",d.EA.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},imageWrapper:true?{name:"4wam81",styles:"height:42px"}:0,image:(0,n.AH)("width:100%;height:100%;border-radius:",d.Vq[4],";object-fit:cover;object-position:center;"+(true?"":0),true?"":0),cardContent:true?{name:"1fttcpj",styles:"display:flex;flex-direction:column"}:0,cardTitle:(0,n.AH)(f.I.small("medium"),";",k.x.text.ellipsis(1),";"+(true?"":0),true?"":0),removeButton:(0,n.AH)(k.x.resetButton,";position:absolute;top:50%;right:",d.YK[8],";transform:translateY(-50%);display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",d.Vq.circle,";background:",d.I6.background.white,";&:focus,&:active,&:hover{background:",d.I6.background.white,";}svg{color:",d.I6.icon["default"],";}:hover{svg{color:",d.I6.icon.hover,";}}:focus{box-shadow:",d.r7.focus,";}:focus-visible{opacity:1;}"+(true?"":0),true?"":0),emptyOption:(0,n.AH)(f.I.caption("medium"),";padding:",d.YK[8],";text-align:center;"+(true?"":0),true?"":0)};var et=r(57215);var rt=r(83788);var nt=r(46201);var ot=r(29832);var at=r(38919);var it=r(50707);function ut(t,e){return ft(t)||dt(t,e)||ct(t,e)||st()}function st(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ct(t,e){if(t){if("string"==typeof t)return lt(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?lt(t,e):void 0}}function lt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function dt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function ft(t){if(Array.isArray(t))return t}function pt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var vt=function t(e){var r=e.certificates,o=e.selectedCertificate,a=e.currentCertificate,s=e.onSelectCertificate,c=e.closeModal;var l=(0,u.useState)(o),d=ut(l,2),f=d[0],m=d[1];var g=(0,u.useState)(a),y=ut(g,2),b=y[0],w=y[1];var _=(0,u.useRef)(null);var x=r.findIndex((function(t){return t.key===b.key}));var Y=Math.max(-1,x-1);var k=Math.min(r.length,x+1);(0,u.useEffect)((function(){var t=function t(e){if(e.key==="ArrowLeft"){S("previous")}else if(e.key==="ArrowRight"){S("next")}else if(e.key==="Enter"){O(b)}else if(e.key==="Escape"){c({action:"CLOSE"})}};window.addEventListener("keydown",t);return function(){window.removeEventListener("keydown",t)}}),[x,r]);(0,u.useEffect)((function(){if(_.current){_.current.focus()}}),[]);var O=function t(e){if(e.key===f){return}s(e);m(e.key)};var S=function t(e){if(e==="previous"&&x>0){w(r[Y])}else if(e==="next"&&x<r.length-1){w(r[k])}};return(0,n.Y)("div",{css:mt.container},b&&(0,n.Y)("div",{css:mt.content},(0,n.Y)("div",{css:mt.certificateAndActions},(0,n.Y)("img",{css:mt.certificate,src:b.preview_src,alt:b.name}),(0,n.Y)("div",{css:mt.actionsWrapper},(0,n.Y)(h.A,{placement:"right",content:(0,i.__)("Close","tutor")},(0,n.Y)("button",{ref:_,type:"button",css:[mt.actionButton,mt.closeButton,true?"":0,true?"":0],onClick:function t(){c({action:"CLOSE"})}},(0,n.Y)(v.A,{name:"cross",width:40,height:40}))),(0,n.Y)(p.A,{when:b.edit_url},(function(t){return(0,n.Y)(h.A,{placement:"right",content:(0,i.__)("Edit in Certificate Builder","tutor")},(0,n.Y)("button",{type:"button",css:[mt.actionButton,mt.editButton,true?"":0,true?"":0],onClick:function e(){window.open(t,"_blank","noopener")}},(0,n.Y)(v.A,{name:"edit",width:40,height:40})))}))))),(0,n.Y)("div",{css:mt.navigatorWrapper},(0,n.Y)("div",{css:mt.navigator},(0,n.Y)("button",{type:"button",css:[mt.actionButton,mt.navigatorButton,true?"":0,true?"":0],onClick:function t(){return S("previous")},disabled:Y<0},(0,n.Y)(v.A,{name:!A.V8?"chevronLeft":"chevronRight",width:40,height:40})),(0,n.Y)(at.A,{variant:"primary",onClick:function t(){O(b);c({action:"CONFIRM"})},disabled:f===b.key},f===b.key?(0,i.__)("Selected","tutor"):(0,i.__)("Select","tutor")),(0,n.Y)("button",{type:"button",css:[mt.actionButton,mt.navigatorButton,true?"":0,true?"":0],onClick:function t(){return S("next")},disabled:k>r.length-1},(0,n.Y)(v.A,{name:!A.V8?"chevronRight":"chevronLeft",width:40,height:40})))))};const ht=vt;var mt={container:(0,n.AH)("width:100%;height:100%;",k.x.display.flex("column"),";justify-content:center;align-items:center;gap:",d.YK[16],";position:relative;"+(true?"":0),true?"":0),content:(0,n.AH)(k.x.display.flex("column"),";justify-content:center;align-items:center;object-fit:contain;max-width:80vw;max-height:calc(100vh - 200px);width:100%;height:100%;"+(true?"":0),true?"":0),certificateAndActions:(0,n.AH)("position:relative;",k.x.display.flex(),";justify-content:center;align-items:center;gap:",d.YK[20],";height:100%;"+(true?"":0),true?"":0),certificate:true?{name:"ukfjzf",styles:"width:100%;height:100%;object-fit:contain"}:0,actionsWrapper:(0,n.AH)("position:absolute;top:0;right:-",d.YK[56],";bottom:0;",k.x.display.flex("column"),";justify-content:space-between;",d.EA.smallMobile,"{right:-",d.YK[32],";}"+(true?"":0),true?"":0),actionButton:(0,n.AH)("place-self:center start;",k.x.resetButton,";display:inline-flex;align-items:center;justify-content:center;&:focus,&:active,&:hover{background:none;}svg{color:",d.I6.action.secondary["default"],";transition:color 0.3s ease-in-out;}"+(true?"":0),true?"":0),closeButton:true?{name:"j2duhg",styles:"place-self:center start"}:0,editButton:true?{name:"ak8e88",styles:"place-self:center end"}:0,navigatorWrapper:(0,n.AH)(true?"":0,true?"":0),navigator:(0,n.AH)(k.x.display.flex(),";gap:",d.YK[16],";justify-content:center;background:",d.I6.background.white,";padding:",d.YK[12],";border-radius:",d.Vq[8],";"+(true?"":0),true?"":0),navigatorButton:(0,n.AH)("svg{color:",d.I6.icon["default"],";}:disabled{cursor:not-allowed;svg{color:",d.I6.icon.hints,";}}"+(true?"":0),true?"":0)};var gt=r(21708);var yt=(0,gt.p9)();var bt=function t(e){var r,a;var u=e.selectedCertificate,s=u===void 0?"":u,c=e.data,l=e.orientation,d=e.onSelectCertificate;var f=(0,it.h)(),h=f.showModal;var m=(0,o.jE)();var g=m.getQueryData(["CourseDetails",yt]);var y=(r=((a=g===null||g===void 0?void 0:g.course_certificates_templates)!==null&&a!==void 0?a:[]).filter((function(t){return t.orientation===l&&(c.is_default?t.is_default===true:t.is_default===false)})))!==null&&r!==void 0?r:[];return(0,n.Y)("div",{css:_t.wrapper({isSelected:s===c.key,isLandScape:l==="landscape"})},(0,n.Y)("div",{"data-overlay":true,onClick:function t(){return d(c.key)},onKeyDown:function t(e){if(e.key==="Enter"||e.key===" "){d(c.key)}}}),(0,n.Y)(p.A,{when:c.preview_src,fallback:(0,n.Y)("div",{css:_t.emptyCard},(0,n.Y)(v.A,{name:"outlineNone",width:49,height:49}),(0,n.Y)("span",null,(0,i.__)("None","tutor")))},(function(t){return(0,n.Y)("img",{css:_t.certificateImage,src:t,alt:c.name})})),(0,n.Y)(p.A,{when:c.preview_src||c.key!==s},(0,n.Y)("div",{"data-footer-actions":true,css:_t.footerWrapper},(0,n.Y)(p.A,{when:c.preview_src},(0,n.Y)(at.A,{variant:"secondary",isOutlined:true,size:"small",onClick:function t(){h({component:ht,props:{certificates:y,currentCertificate:c,selectedCertificate:s,onSelectCertificate:function t(e){d(e.key)}}})}},(0,i.__)("Preview","tutor"))),(0,n.Y)(p.A,{when:c.key!==s},(0,n.Y)(at.A,{variant:"primary",size:"small",onClick:function t(){return d(c.key)}},(0,i.__)("Select","tutor"))))),(0,n.Y)("div",{css:_t.checkIcon({isSelected:s===c.key})},(0,n.Y)(v.A,{name:"checkFilledWhite",width:32,height:32})))};const wt=bt;var _t={wrapper:function t(e){var r=e.isSelected,o=r===void 0?false:r,a=e.isLandScape,i=a===void 0?false:a;return(0,n.AH)(k.x.centeredFlex,";background-color:",d.I6.surface.courseBuilder,";max-height:",i?"154px":"217px",";min-height:",i?"154px":"217px",";height:100%;position:relative;outline:",o?"2px":"1px"," solid ",o?d.I6.stroke.brand:d.I6.stroke["default"],";border-radius:",d.Vq.card,";transition:all 0.15s ease-in-out;[data-overlay]{position:absolute;top:0;left:0;right:0;bottom:0;border-radius:",d.Vq.card,";}",o&&(0,n.AH)("[data-overlay]{background:",d.I6.brand.blue,";opacity:0.1;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{border-color:",d.I6.stroke.brand,";[data-footer-actions]{opacity:1;}[data-overlay]{background:",d.I6.brand.blue,";opacity:0.1;}}"+(true?"":0),true?"":0)},emptyCard:(0,n.AH)(k.x.flexCenter(),";flex-direction:column;height:100%;width:100%;gap:",d.YK[8],";",f.I.caption("medium"),";svg{color:",d.I6.color.black[20],";}"+(true?"":0),true?"":0),certificateImage:(0,n.AH)("width:100%;height:100%;object-fit:contain;border-radius:",d.Vq.card,";"+(true?"":0),true?"":0),footerWrapper:(0,n.AH)("opacity:0;position:absolute;left:0px;right:0px;bottom:0px;",k.x.flexCenter(),";align-items:center;gap:",d.YK[4],";padding-block:",d.YK[8],";background:",d.I6.bg.white,";border-bottom-left-radius:",d.Vq.card,";border-bottom-right-radius:",d.Vq.card,";"+(true?"":0),true?"":0),checkIcon:function t(e){var r=e.isSelected,o=r===void 0?false:r;return(0,n.AH)("opacity:",o?1:0,";position:absolute;top:-14px;right:-14px;border-bottom-left-radius:",d.Vq.card,";svg{color:",d.I6.icon.brand,";}"+(true?"":0),true?"":0)}};var xt=r(48465);const At=r.p+"images/ce66848f153ead20b9a1a038f1d68fe7-certificates-2x.webp";const Yt=r.p+"images/d68012d900d127a80f22fbc9302fad33-certificates.webp";var kt=!!xt.P.tutor_pro_url;var Ot=function t(){if(kt){return null}return(0,n.Y)("div",{css:It.emptyState},(0,n.Y)("img",{css:It.placeholderImage,src:Yt,srcSet:"".concat(Yt," 1x, ").concat(At," 2x"),alt:(0,i.__)("Pro Placeholder","tutor")}),(0,n.Y)("div",{css:It.featureAndActionWrapper},(0,n.Y)("h5",{css:It.title},(0,i.__)("Award Students with Custom Certificates","tutor")),(0,n.Y)("div",{css:It.featuresWithTitle},(0,n.Y)("div",null,(0,i.__)("Celebrate success with personalized certificates. Recognize student achievements with unique designs that inspire and motivate students.","tutor")),(0,n.Y)("div",{css:It.features},(0,n.Y)("div",{css:It.feature},(0,n.Y)(v.A,{name:"materialCheck",width:20,height:20,style:It.checkIcon}),(0,n.Y)("span",null,(0,i.__)("Design personalized certificates that highlight their accomplishments and boost their confidence.","tutor"))),(0,n.Y)("div",{css:It.feature},(0,n.Y)(v.A,{name:"materialCheck",width:20,height:20,style:It.checkIcon}),(0,n.Y)("span",null,(0,i.__)("Inspire them with a touch of credibility and recognition tailored just for them.","tutor")))))),(0,n.Y)("div",{css:It.actionsButton},(0,n.Y)(at.A,{variant:"primary",icon:(0,n.Y)(v.A,{name:"crown",width:24,height:24}),onClick:function t(){window.open(xt.A.TUTOR_PRICING_PAGE,"_blank","noopener")}},(0,i.__)("Get Tutor LMS Pro","tutor"))))};const St=Ot;var It={emptyState:(0,n.AH)("padding-bottom:",d.YK[12],";",k.x.display.flex("column")," gap:",d.YK[20],";"+(true?"":0),true?"":0),placeholderImage:function t(e){var r=e.notFound;return(0,n.AH)("max-width:100%;width:100%;height:",r?"189px":"312px;",";object-fit:cover;object-position:center;border-radius:",d.Vq[6],";"+(true?"":0),true?"":0)},featureAndActionWrapper:(0,n.AH)(k.x.display.flex("column")," align-items:center;gap:",d.YK[12],";"+(true?"":0),true?"":0),title:(0,n.AH)(f.I.heading5("medium")," color:",d.I6.text.primary,";"+(true?"":0),true?"":0),featuresWithTitle:(0,n.AH)(k.x.display.flex("column")," max-width:500px;width:100%;gap:",d.YK[8],";",f.I.body("regular"),";"+(true?"":0),true?"":0),features:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";"+(true?"":0),true?"":0),feature:(0,n.AH)(k.x.display.flex()," gap:",d.YK[12],";color:",d.I6.text.title,";text-wrap:pretty;"+(true?"":0),true?"":0),checkIcon:(0,n.AH)("flex-shrink:0;color:",d.I6.text.success,";"+(true?"":0),true?"":0),actionsButton:(0,n.AH)(k.x.flexCenter()," margin-top:",d.YK[4],";"+(true?"":0),true?"":0)};function Et(t){return Tt(t)||Mt(t)||Lt(t)||Ct()}function Ct(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Mt(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function Tt(t){if(Array.isArray(t))return Pt(t)}function jt(t,e){return Ht(t)||Nt(t,e)||Lt(t,e)||Dt()}function Dt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Lt(t,e){if(t){if("string"==typeof t)return Pt(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pt(t,e):void 0}}function Pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Nt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function Ht(t){if(Array.isArray(t))return t}function Wt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Kt=(0,gt.p9)();var Bt=!!xt.P.tutor_pro_url;var zt=(0,O.GR)(A.oW.TUTOR_CERTIFICATE);var Ut=function t(e){var r;var a=e.isSidebarVisible;var s=(0,o.jE)();var l=s.getQueryData(["CourseDetails",Kt]);var m=(r=l===null||l===void 0?void 0:l.course_certificates_templates)!==null&&r!==void 0?r:[];var g=m.filter((function(t){return t.is_default}));var y=(0,c.xW)();var b=y.watch("tutor_course_certificate_template");var w=(0,u.useState)("templates"),_=jt(w,2),x=_[0],O=_[1];var S=(0,u.useState)("landscape"),I=jt(S,2),E=I[0],T=I[1];var j=(0,u.useState)(b),D=jt(j,2),L=D[0],P=D[1];var N=m.some((function(t){return t.orientation==="landscape"&&(x==="templates"?t.is_default:!t.is_default)}));var H=m.some((function(t){return t.orientation==="portrait"&&(x==="templates"?t.is_default:!t.is_default)}));(0,u.useEffect)((function(){if(m.length){if(g.length===0){O("custom_certificates")}var t=m.some((function(t){return t.orientation==="landscape"}));if(!t&&E==="landscape"){T("portrait")}}if(b==="none"){P(b);return}var e=m.find((function(t){return t.key===b}));if(e){if(E!==e.orientation){T(e.orientation)}O(e.is_default?"templates":"custom_certificates");P(e.key)}}),[b,m]);var W=m.filter((function(t){return t.orientation===E&&(x==="templates"?t===null||t===void 0?void 0:t.is_default:!(t!==null&&t!==void 0&&t.is_default))}));var K=function t(e){O(e);var r=m.some((function(t){return t.orientation==="landscape"&&(e==="templates"?t.is_default:!t.is_default)}));var n=m.some((function(t){return t.orientation==="portrait"&&(e==="templates"?t.is_default:!t.is_default)}));T((function(t){if(r&&n||!r&&!n){return t}return r?"landscape":"portrait"}))};var B=function t(e){T(e)};var z=function t(e){y.setValue("tutor_course_certificate_template",e,{shouldDirty:true});P(e)};var U=[].concat(Et(g.length?[{label:(0,i.__)("Templates","tutor"),value:"templates"}]:[]),[{label:A.vN.isAboveSmallMobile?(0,i.__)("Custom Certificates","tutor"):(0,i.__)("Certificates","tutor"),value:"custom_certificates"}]);return(0,n.Y)(p.A,{when:Bt&&zt,fallback:(0,n.Y)(St,null)},(0,n.Y)(p.A,{when:zt},(0,n.Y)("div",{css:qt.tabs},(0,n.Y)(ot.A,{wrapperCss:qt.tabsWrapper,tabList:U,activeTab:x,onChange:K}),(0,n.Y)("div",{css:qt.orientation},(0,n.Y)(p.A,{when:N&&H},(0,n.Y)(h.A,{delay:200,content:(0,i.__)("Landscape","tutor")},(0,n.Y)("button",{type:"button",css:[k.x.resetButton,qt.orientationButton({isActive:E==="landscape"}),true?"":0,true?"":0],onClick:function t(){return B("landscape")}},(0,n.Y)(v.A,{name:E==="landscape"?"landscapeFilled":"landscape",width:32,height:32}))),(0,n.Y)(h.A,{delay:200,content:(0,i.__)("Portrait","tutor")},(0,n.Y)("button",{type:"button",css:[k.x.resetButton,qt.orientationButton({isActive:E==="portrait"}),true?"":0,true?"":0],onClick:function t(){return B("portrait")}},(0,n.Y)(v.A,{name:E==="portrait"?"portraitFilled":"portrait",width:32,height:32})))))),(0,n.Y)("div",{css:qt.certificateWrapper({hasCertificates:W.length>0,isSidebarVisible:a})},(0,n.Y)(p.A,{when:m.length&&(g.length===0||x==="templates")},(0,n.Y)(wt,{selectedCertificate:L,onSelectCertificate:z,data:{key:"none",name:(0,i.__)("None","tutor"),preview_src:"",background_src:"",orientation:"landscape",url:""},orientation:E})),(0,n.Y)(p.A,{when:W.length>0,fallback:(0,n.Y)("div",{css:qt.emptyState},(0,n.Y)("img",{css:qt.placeholderImage({notFound:true}),src:M.A,srcSet:"".concat(M.A," 1x, ").concat(C.A," 2x"),alt:(0,i.__)("Not Found","tutor")}),(0,n.Y)("div",{css:qt.featureAndActionWrapper},(0,n.Y)("p",{css:(0,n.AH)(f.I.body("medium")," color:",d.I6.text.subdued,";"+(true?"":0),true?"":0)},(0,i.__)("You didn’t create any certificate yet!","tutor"))))},(0,n.Y)(Y.A,{each:W},(function(t){return(0,n.Y)(wt,{key:t.key,selectedCertificate:L,onSelectCertificate:z,data:t,orientation:E})}))))))};const Rt=Ut;var Ft=true?{name:"1vm53vd",styles:"grid-template-columns:1fr;place-items:center"}:0;var qt={tabs:true?{name:"bjn8wh",styles:"position:relative"}:0,tabsWrapper:true?{name:"1vqsuxb",styles:"button{min-width:auto;}"}:0,certificateWrapper:function t(e){var r=e.hasCertificates,o=e.isSidebarVisible;return(0,n.AH)("display:grid;grid-template-columns:repeat(",o?3:4,", 1fr);gap:",d.YK[16],";padding-top:",d.YK[12],";",!r&&Ft," ",d.EA.smallMobile,"{grid-template-columns:1fr 1fr;}"+(true?"":0),true?"":0)},orientation:(0,n.AH)(k.x.display.flex()," gap:",d.YK[8],";position:absolute;height:32px;right:0;bottom:",d.YK[4],";"+(true?"":0),true?"":0),orientationButton:function t(e){var r=e.isActive;return(0,n.AH)("display:inline-flex;color:",r?d.I6.icon.brand:d.I6.icon["default"],";border-radius:",d.Vq[4],";&:focus,&:active,&:hover{background:none;color:",r?d.I6.icon.brand:d.I6.icon["default"],";}&:focus-visible{outline:2px solid ",d.I6.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0)},emptyState:(0,n.AH)("padding-block:",d.YK[16]," ",d.YK[12],";",k.x.display.flex("column")," gap:",d.YK[20],";"+(true?"":0),true?"":0),placeholderImage:function t(e){var r=e.notFound;return(0,n.AH)("max-width:100%;width:100%;height:",r?"189px":"312px;",";object-fit:cover;object-position:center;border-radius:",d.Vq[6],";"+(true?"":0),true?"":0)},featureAndActionWrapper:(0,n.AH)(k.x.display.flex("column")," align-items:center;gap:",d.YK[12],";"+(true?"":0),true?"":0),actionsButton:(0,n.AH)(k.x.flexCenter()," margin-top:",d.YK[4],";"+(true?"":0),true?"":0)};const Gt=r.p+"images/0807df3bda222a2334c674c106073bdf-prerequisites-2x.webp";const Vt=r.p+"images/ec5640e219546af0a227e9787705bec1-prerequisites.webp";var Qt=!!xt.P.tutor_pro_url;var $t=function t(){return(0,n.Y)("div",{css:Xt.emptyState},(0,n.Y)("img",{css:Xt.placeholderImage,src:Vt,srcSet:"".concat(Vt," 1x, ").concat(Gt," 2x"),alt:(0,i.__)("Pro Placeholder","tutor")}),(0,n.Y)("div",{css:Xt.featureAndActionWrapper},(0,n.Y)("div",{css:Xt.featuresWithTitle},(0,n.Y)("div",null,(0,i.__)("Guide Students with Course Prerequisites","tutor")),(0,n.Y)(p.A,{when:!Qt},(0,n.Y)("div",{css:Xt.features},(0,n.Y)("div",{css:Xt.feature},(0,n.Y)(v.A,{name:"materialCheck",width:20,height:20,style:Xt.checkIcon}),(0,n.Y)("span",null,(0,i.__)("Easily set prerequisites to structure your courses and guide student progress.","tutor"))),(0,n.Y)("div",{css:Xt.feature},(0,n.Y)(v.A,{name:"materialCheck",width:20,height:20,style:Xt.checkIcon}),(0,n.Y)("span",null,(0,i.__)("Offer customized learning journeys by setting multiple prerequisites for any course.","tutor"))))))))};const Zt=$t;var Xt={emptyState:(0,n.AH)("padding:",d.YK[12]," ",d.YK[12]," ",d.YK[24]," ",d.YK[12],";",k.x.display.flex("column")," gap:",d.YK[20],";border:1px solid ",d.I6.stroke.divider,";border-radius:",d.Vq.card,";background-color:",d.I6.background.white,";"+(true?"":0),true?"":0),placeholderImage:(0,n.AH)("max-width:100%;width:100%;height:112px;object-fit:cover;object-position:center;border-radius:",d.Vq[6],";"+(true?"":0),true?"":0),featureAndActionWrapper:(0,n.AH)(k.x.display.flex("column")," align-items:center;gap:",d.YK[12],";padding-inline:",d.YK[4],";"+(true?"":0),true?"":0),featuresWithTitle:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";",f.I.caption("medium"),";"+(true?"":0),true?"":0),features:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";"+(true?"":0),true?"":0),feature:(0,n.AH)(f.I.small(),";",k.x.display.flex()," gap:",d.YK[12],";color:",d.I6.text.title,";text-wrap:pretty;"+(true?"":0),true?"":0),checkIcon:(0,n.AH)("flex-shrink:0;color:",d.I6.text.success,";"+(true?"":0),true?"":0),actionsButton:(0,n.AH)(k.x.flexCenter()," margin-top:",d.YK[4],";"+(true?"":0),true?"":0)};var Jt=r(47989);var te=r(85420);var ee=r(53429);var re=r(46068);var ne=r(33191);var oe=r(81730);var ae=r(41594);function ie(t){"@babel/helpers - typeof";return ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ie(t)}function ue(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ue=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(M([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==ie(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(ie(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,C.prototype={constructor:C,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:M(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function se(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function ce(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){se(a,n,o,i,u,"next",t)}function u(t){se(a,n,o,i,u,"throw",t)}i(void 0)}))}}function le(t,e){return he(t)||ve(t,e)||fe(t,e)||de()}function de(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fe(t,e){if(t){if("string"==typeof t)return pe(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?pe(t,e):void 0}}function pe(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ve(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function he(t){if(Array.isArray(t))return t}var me=(0,gt.p9)();var ge=function t(e){var r=e.data,o=e.topicId;var a=(0,u.useState)(false),s=le(a,2),c=s[0],l=s[1];var f=(0,u.useState)(false),p=le(f,2),h=p[0],m=p[1];var g=(0,ne.sx)(String(me),{"post-id":r.ID,"event-id":r.meeting_data.id});var y=(0,u.useRef)(null);var b=(0,u.useRef)(null);var w=r.meeting_data,_=r.post_title;var x=function(){var t=ce(ue().mark((function t(){var e;return ue().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return g.mutateAsync();case 2:e=r.sent;if(e.status_code===200){m(false)}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var Y=(0,ee["default"])(new Date(w.start_datetime),A.Bd.day);var S=(0,ee["default"])(new Date(w.start_datetime),A.Bd.month);var I=(0,ee["default"])(new Date(w.start_datetime),A.Bd.year);var E=(0,ee["default"])(new Date(w.start_datetime),A.Bd.hoursMinutes).split(" "),C=le(E,2),M=C[0],T=C[1],j=T===void 0?"":T;return(0,n.Y)(ae.Fragment,null,(0,n.Y)("div",{css:be.card({isPopoverOpen:h||c})},(0,n.Y)("div",{css:be.cardTitle},_),(0,n.Y)("div",{css:be.cardContent},(0,n.Y)("span",{css:be.inlineContent},(0,i.__)("Start time","tutor"),(0,n.Y)("div",{css:be.hyphen}),(0,n.Y)("div",{css:be.meetingDateTime,className:"date-time"},(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(Y," ")),(0,n.Y)("span",null,"".concat(S," ")),(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(I,", ")),(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(M," ")),(0,n.Y)("span",null,"".concat(j," ")))),(0,n.Y)("div",{css:be.buttonWrapper},(0,n.Y)(at.A,{variant:"secondary",size:"small",type:"button",onClick:function t(){window.open(w.meet_link,"_blank","noopener")}},(0,i.__)("Start Meeting","tutor")),(0,n.Y)("div",{css:be.actions},(0,n.Y)("button",{ref:y,type:"button",css:k.x.actionButton,"data-visually-hidden":true,onClick:function t(){return l(true)}},(0,n.Y)(v.A,{name:"edit",width:24,height:24})),(0,n.Y)("button",{type:"button",css:k.x.actionButton,"data-visually-hidden":true,onClick:function t(){m(true)},ref:b},(0,n.Y)(v.A,{name:"delete",width:24,height:24})))))),(0,n.Y)(Jt.A,{isOpen:c,triggerRef:y,closePopover:function t(){return l(false)},maxWidth:"306px"},(0,n.Y)(oe.A,{data:r,topicId:o,onCancel:function t(){l(false)}})),(0,n.Y)(re.A,{isOpen:h,triggerRef:b,closePopover:O.lQ,maxWidth:"258px",title:(0,i.sprintf)((0,i.__)('Delete "%s"',"tutor"),_),message:(0,i.__)("Are you sure you want to delete this meeting? This cannot be undone.","tutor"),animationType:te.J6.slideUp,arrow:"auto",hideArrow:true,isLoading:g.isPending,confirmButton:{text:(0,i.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,i.__)("Cancel","tutor"),variant:"text"},onConfirmation:ce(ue().mark((function t(){return ue().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.next=2;return x();case 2:case"end":return e.stop()}}),t)}))),onCancel:function t(){m(false)}}))};const ye=ge;var be={card:function t(e){var r=e.isPopoverOpen,o=r===void 0?false:r;return(0,n.AH)(k.x.display.flex("column")," padding:",d.YK[8]," ",d.YK[12]," ",d.YK[12]," ",d.YK[12],";gap:",d.YK[8],";border-radius:",d.Vq[6],";transition:background 0.3s ease;[data-visually-hidden]{opacity:0;transition:opacity 0.3s ease-in-out;}",o&&(0,n.AH)("background-color:",d.I6.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{background-color:",d.I6.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}}",d.EA.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},cardTitle:(0,n.AH)(f.I.caption("medium")," color:",d.I6.text.title,";"+(true?"":0),true?"":0),cardContent:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";"+(true?"":0),true?"":0),hyphen:(0,n.AH)("width:5px;height:2px;background:",d.I6.stroke["default"],";"+(true?"":0),true?"":0),inlineContent:(0,n.AH)(f.I.small("regular")," ",k.x.display.flex()," align-items:center;gap:",d.YK[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.AH)("padding:",d.YK[4]," ",d.YK[6],";border-radius:",d.Vq[4],";background:",d.I6.background.status.processing,";transition:background 0.3s ease-in-out;"+(true?"":0),true?"":0),buttonWrapper:(0,n.AH)(k.x.display.flex(),";margin-top:",d.YK[8],";justify-content:space-between;"+(true?"":0),true?"":0),actions:(0,n.AH)(k.x.display.flex(),";align-items:center;gap:",d.YK[8],";"+(true?"":0),true?"":0)};var we=r(53804);var _e=r(41594);function xe(t){"@babel/helpers - typeof";return xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xe(t)}function Ae(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ae=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(M([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==xe(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(xe(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,C.prototype={constructor:C,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:M(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function Ye(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function ke(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){Ye(a,n,o,i,u,"next",t)}function u(t){Ye(a,n,o,i,u,"throw",t)}i(void 0)}))}}function Oe(t,e){return Me(t)||Ce(t,e)||Ie(t,e)||Se()}function Se(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ie(t,e){if(t){if("string"==typeof t)return Ee(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ee(t,e):void 0}}function Ee(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Ce(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function Me(t){if(Array.isArray(t))return t}var Te=(0,gt.p9)();var je=function t(e){var r=e.data,o=e.meetingHost,a=e.topicId;var s=(0,u.useState)(false),c=Oe(s,2),l=c[0],f=c[1];var h=(0,u.useState)(false),m=Oe(h,2),g=m[0],y=m[1];var b=(0,ne.aN)(String(Te));var w=(0,u.useRef)(null);var _=(0,u.useRef)(null);var x=r.ID,Y=r.meeting_data,S=r.post_title,I=r.meeting_starts_at;var E=function(){var t=ke(Ae().mark((function t(){var e;return Ae().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return b.mutateAsync(x);case 2:e=r.sent;if(e.success){y(false)}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var C=(0,ee["default"])(new Date(I),A.Bd.day);var M=(0,ee["default"])(new Date(I),A.Bd.month);var T=(0,ee["default"])(new Date(I),A.Bd.year);var j=(0,ee["default"])(new Date(I),A.Bd.hoursMinutes).split(" "),D=Oe(j,2),L=D[0],P=D[1],N=P===void 0?"":P;return(0,n.Y)(_e.Fragment,null,(0,n.Y)("div",{css:Le.card({isPopoverOpen:g||l})},(0,n.Y)("div",{css:Le.cardTitle},S),(0,n.Y)("div",{css:Le.cardContent},(0,n.Y)("span",{css:Le.inlineContent},(0,i.__)("Start time","tutor"),(0,n.Y)("div",{css:Le.hyphen}),(0,n.Y)("div",{css:Le.meetingDateTime,className:"date-time"},(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(C," ")),(0,n.Y)("span",null,"".concat(M," ")),(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(T,", ")),(0,n.Y)("span",{css:(0,n.AH)({fontWeight:d.Wy.semiBold},true?"":0,true?"":0)},"".concat(L," ")),(0,n.Y)("span",null,"".concat(N," ")))),(0,n.Y)(p.A,{when:Y.id},(0,n.Y)("div",{css:Le.inlineContent},(0,i.__)("Meeting Token","tutor"),(0,n.Y)("div",{css:Le.hyphen}),(0,n.Y)("div",null,Y.id))),(0,n.Y)(p.A,{when:Y.password},(0,n.Y)("div",{css:Le.inlineContent},(0,i.__)("Password","tutor"),(0,n.Y)("div",{css:Le.hyphen}),(0,n.Y)("div",null,Y.password))),(0,n.Y)("div",{css:Le.buttonWrapper},(0,n.Y)(at.A,{variant:"secondary",size:"small",type:"button",onClick:function t(){window.open(Y.start_url,"_blank","noopener")}},(0,i.__)("Start Meeting","tutor")),(0,n.Y)("div",{css:Le.actions},(0,n.Y)("button",{ref:w,type:"button",css:k.x.actionButton,"data-visually-hidden":true,onClick:function t(){f(true)}},(0,n.Y)(v.A,{name:"edit",width:24,height:24})),(0,n.Y)("button",{type:"button",css:k.x.actionButton,"data-visually-hidden":true,onClick:function t(){return y(true)},ref:_},(0,n.Y)(v.A,{name:"delete",width:24,height:24})))))),(0,n.Y)(Jt.A,{isOpen:l,triggerRef:w,closePopover:function t(){return f(false)},maxWidth:"306px"},(0,n.Y)(we.A,{data:r,meetingHost:o,topicId:a,onCancel:function t(){f(false)}})),(0,n.Y)(re.A,{isOpen:g,triggerRef:_,closePopover:O.lQ,maxWidth:"258px",title:(0,i.sprintf)((0,i.__)('Delete "%s"',"tutor"),S),message:(0,i.__)("Are you sure you want to delete this meeting? This cannot be undone.","tutor"),animationType:te.J6.slideUp,arrow:"auto",hideArrow:true,isLoading:b.isPending,confirmButton:{text:(0,i.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,i.__)("Cancel","tutor"),variant:"text"},onConfirmation:ke(Ae().mark((function t(){return Ae().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.next=2;return E();case 2:case"end":return e.stop()}}),t)}))),onCancel:function t(){y(false)}}))};const De=je;var Le={card:function t(e){var r=e.isPopoverOpen,o=r===void 0?false:r;return(0,n.AH)(k.x.display.flex("column")," padding:",d.YK[8]," ",d.YK[12]," ",d.YK[12]," ",d.YK[12],";gap:",d.YK[8],";border-radius:",d.Vq[6],";transition:background 0.3s ease;[data-visually-hidden]{opacity:0;transition:opacity 0.3s ease-in-out;}",o&&(0,n.AH)("background-color:",d.I6.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{background-color:",d.I6.background.hover,";[data-visually-hidden]{opacity:1;}.date-time{background:none;}}",d.EA.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},cardTitle:(0,n.AH)(f.I.caption("medium")," color:",d.I6.text.title,";"+(true?"":0),true?"":0),cardContent:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";"+(true?"":0),true?"":0),hyphen:(0,n.AH)("width:5px;height:2px;background:",d.I6.stroke["default"],";"+(true?"":0),true?"":0),inlineContent:(0,n.AH)(f.I.small("regular")," ",k.x.display.flex()," align-items:center;gap:",d.YK[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.AH)("padding:",d.YK[4]," ",d.YK[6],";border-radius:",d.Vq[4],";background:",d.I6.background.status.processing,";transition:background 0.3s ease-in-out;"+(true?"":0),true?"":0),buttonWrapper:(0,n.AH)(k.x.display.flex(),";margin-top:",d.YK[8],";justify-content:space-between;"+(true?"":0),true?"":0),actions:(0,n.AH)(k.x.display.flex(),";align-items:center;gap:",d.YK[8],";"+(true?"":0),true?"":0)};const Pe=r.p+"images/ce30a118f93885425aa8ace20559a99e-live-class-2x.webp";const Ne=r.p+"images/b3a93f8abedeeefea14556d1f4bac6b1-live-class.webp";var He=r(15888);function We(t,e){return Re(t)||Ue(t,e)||Be(t,e)||Ke()}function Ke(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Be(t,e){if(t){if("string"==typeof t)return ze(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ze(t,e):void 0}}function ze(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function Ue(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function Re(t){if(Array.isArray(t))return t}function Fe(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var qe=!!xt.P.tutor_pro_url;var Ge=(0,O.GR)(A.oW.TUTOR_ZOOM_INTEGRATION);var Ve=(0,O.GR)(A.oW.TUTOR_GOOGLE_MEET_INTEGRATION);var Qe=(0,gt.p9)();var $e=true?{name:"1d3w5wq",styles:"width:100%"}:0;var Ze=true?{name:"1d3w5wq",styles:"width:100%"}:0;var Xe=function t(){var e,r,a;var s=(0,o.jE)();var c=s.getQueryData(["CourseDetails",Qe]);var l=(e=c===null||c===void 0?void 0:c.zoom_meetings)!==null&&e!==void 0?e:[];var d=(r=c===null||c===void 0?void 0:c.zoom_users)!==null&&r!==void 0?r:{};var f=(a=c===null||c===void 0?void 0:c.google_meet_meetings)!==null&&a!==void 0?a:[];var h=(0,u.useState)(null),m=We(h,2),g=m[0],y=m[1];var b=(0,u.useRef)(null);var w=(0,u.useRef)(null);if(qe&&!Ge&&!Ve){return null}return(0,n.Y)("div",{css:tr.liveClass},(0,n.Y)("span",{css:tr.label},(0,i.__)("Schedule Live Class","tutor"),!qe&&(0,n.Y)(_.A,{content:(0,i.__)("Pro","tutor")})),(0,n.Y)(p.A,{when:qe,fallback:(0,n.Y)(x.A,{size:"small",removeBorder:false,emptyStateImage:Ne,emptyStateImage2x:Pe,imageAltText:(0,i.__)("Tutor LMS PRO","tutor"),title:(0,i.__)("Bring your courses to life and engage students with interactive live classes.","tutor"),actions:(0,n.Y)(at.A,{size:"small",icon:(0,n.Y)(v.A,{name:"crown",width:24,height:24}),onClick:function t(){window.open(xt.A.TUTOR_PRICING_PAGE,"_blank","noopener")}},(0,i.__)("Get Tutor LMS Pro","tutor"))})},(0,n.Y)(p.A,{when:Ge||Ve},(0,n.Y)(p.A,{when:Ge},(0,n.Y)("div",{css:tr.meetingsWrapper({hasMeeting:l.length>0})},(0,n.Y)(Y.A,{each:l},(function(t){return(0,n.Y)("div",{key:t.ID,css:tr.meeting({hasMeeting:l.length>0})},(0,n.Y)(De,{data:t,meetingHost:d}))})),(0,n.Y)("div",{css:tr.meetingsFooter({hasMeeting:l.length>0})},(0,n.Y)(at.A,{"data-cy":"create-zoom-meeting",variant:"secondary",icon:(0,n.Y)(v.A,{name:"zoomColorize",width:24,height:24}),buttonCss:Ze,onClick:function t(){return y("zoom")},ref:b},(0,i.__)("Create a Zoom Meeting","tutor"))))),(0,n.Y)(p.A,{when:Ve},(0,n.Y)("div",{css:tr.meetingsWrapper({hasMeeting:f.length>0})},(0,n.Y)(Y.A,{each:f},(function(t){return(0,n.Y)("div",{key:t.ID,css:tr.meeting({hasMeeting:f.length>0})},(0,n.Y)(ye,{data:t}))})),(0,n.Y)("div",{css:tr.meetingsFooter({hasMeeting:f.length>0})},(0,n.Y)(at.A,{"data-cy":"create-google-meet-link",variant:"secondary",icon:(0,n.Y)(v.A,{name:"googleMeetColorize",width:24,height:24}),buttonCss:$e,onClick:function t(){return y("google_meet")},ref:w},(0,i.__)("Create a Google Meet Link","tutor"))))))),(0,n.Y)(Jt.A,{triggerRef:b,isOpen:g==="zoom",closePopover:O.lQ,animationType:te.J6.slideUp,closeOnEscape:false,arrow:A.vN.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,n.Y)(we.A,{data:null,meetingHost:d,onCancel:function t(){y(null)}})),(0,n.Y)(Jt.A,{triggerRef:w,isOpen:g==="google_meet",closePopover:O.lQ,animationType:te.J6.slideUp,closeOnEscape:false,arrow:A.vN.isAboveMobile?"auto":"absoluteCenter",hideArrow:true},(0,n.Y)(oe.A,{data:null,onCancel:function t(){y(null)}})))};const Je=(0,He.M)(Xe);var tr={label:(0,n.AH)(k.x.display.inlineFlex()," align-items:center;gap:",d.YK[4],";",f.I.body()," color:",d.I6.text.title,";"+(true?"":0),true?"":0),liveClass:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[8],";"+(true?"":0),true?"":0),meetingsWrapper:function t(e){var r=e.hasMeeting;return(0,n.AH)(k.x.display.flex("column")," background-color:",d.I6.background.white,";border-radius:",d.Vq.card,";",r&&(0,n.AH)("border:1px solid ",d.I6.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},meeting:function t(e){var r=e.hasMeeting;return(0,n.AH)("padding:",d.YK[8]," ",d.YK[8]," ",d.YK[12]," ",d.YK[8],";",r&&(0,n.AH)("border-bottom:1px solid ",d.I6.stroke.divider,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},meetingsFooter:function t(e){var r=e.hasMeeting;return(0,n.AH)("width:100%;",r&&(0,n.AH)("padding:",d.YK[12]," ",d.YK[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var er=r(40804);var rr=r(70956);var nr=r(47839);var or=r(70396);var ar=r(96645);var ir=r(51032);const ur=r.p+"images/a980852605189e7bcfedc3c12a7844d0-attachments-2x.webp";const sr=r.p+"images/8f57a0f5b77c41a0b937b821ca4b4e29-attachments.webp";function cr(){return cr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},cr.apply(null,arguments)}function lr(t){return vr(t)||pr(t)||fr(t)||dr()}function dr(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fr(t,e){if(t){if("string"==typeof t)return hr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?hr(t,e):void 0}}function pr(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function vr(t){if(Array.isArray(t))return hr(t)}function hr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var mr=!!xt.P.tutor_pro_url;var gr=(0,gt.p9)();var yr=(0,O.GR)(A.oW.TUTOR_PREREQUISITES);var br=(0,O.GR)(A.oW.TUTOR_COURSE_ATTACHMENTS);var wr=(0,O.GR)(A.oW.TUTOR_CERTIFICATE);var _r=function t(){var e;var r=(0,l.Zp)();(0,u.useEffect)((function(){if(!gr){r(or.P.Home.buildLink(),{replace:true})}}),[r]);var s=(0,c.xW)();var d=(0,o.jE)();var f=(0,a.C)({queryKey:["CourseDetails",gr]});var v=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_BENEFITS);var h=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_TARGET_AUDIENCE);var b=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.TOTAL_COURSE_DURATION);var Y=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_MATERIALS_INCLUDES);var k=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_REQUIREMENTS);var S=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.CERTIFICATES);var I=(0,ar.A)(A.qP.COURSE_BUILDER.ADDITIONAL.ATTACHMENTS);var E=d.getQueryData(["CourseDetails",gr]);var C=(E===null||E===void 0||(e=E.course_prerequisites)===null||e===void 0?void 0:e.map((function(t){return String(t.id)})))||[];var M=(0,ir.TM)({params:{exclude:[String(gr)].concat(lr(C)),limit:-1},isEnabled:!!yr&&!f});if(!gr){return null}if(!gr){return null}var T=v||h||b||Y||k;var j=T||!mr||S&&wr;var D=!mr||[A.oW.TUTOR_PREREQUISITES,A.oW.TUTOR_COURSE_ATTACHMENTS,A.oW.TUTOR_ZOOM_INTEGRATION,A.oW.TUTOR_GOOGLE_MEET_INTEGRATION].some(O.GR);var L=function t(){return(0,n.Y)("div",{css:Ar.sidebarContent},(0,n.Y)(p.A,{when:!mr||yr},(0,n.Y)("div",null,(0,n.Y)("div",{css:Ar.label},(0,i.__)("Course Prerequisites","tutor"),!mr&&(0,n.Y)(_.A,{content:(0,i.__)("Pro","tutor")})),(0,n.Y)(p.A,{when:mr&&yr,fallback:(0,n.Y)(Zt,null)},(0,n.Y)(c.xI,{name:"course_prerequisites",control:s.control,render:function t(e){var r;return(0,n.Y)(J,cr({},e,{placeholder:(0,i.__)("Search courses for prerequisites","tutor"),options:((r=M.data)===null||r===void 0?void 0:r.results)||[],isSearchable:true,loading:M.isLoading||!!f&&!e.field.value}))}})))),(0,n.Y)(p.A,{when:!mr||I&&br},(0,n.Y)("div",null,(0,n.Y)("div",{css:Ar.label},(0,i.__)("Attachments","tutor"),!mr&&(0,n.Y)(_.A,{content:(0,i.__)("Pro","tutor")})),(0,n.Y)(p.A,{when:mr&&br,fallback:(0,n.Y)(p.A,{when:!mr},(0,n.Y)(x.A,{size:"small",removeBorder:false,emptyStateImage:sr,emptyStateImage2x:ur,title:(0,i.__)((0,i.__)("Provide additional resources like downloadable files and reference materials.","tutor"))}))},(0,n.Y)(c.xI,{name:"course_attachments",control:s.control,render:function t(e){return(0,n.Y)(et.A,cr({},e,{buttonText:(0,i.__)("Upload Attachment","tutor"),selectMultiple:true}))}})))),(0,n.Y)(Je,{visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.SCHEDULE_LIVE_CLASS}),(0,n.Y)(er.A,{section:"Additional.bottom_of_sidebar",form:s}))};return(0,n.Y)("div",{css:Ar.wrapper({showSidebar:D&&j})},(0,n.Y)("div",{css:Ar.leftSide},(0,n.Y)(rr.A,{title:(0,i.__)("Additional","tutor"),backUrl:"/curriculum"}),(0,n.Y)(p.A,{when:j},(0,n.Y)("div",{css:Ar.formWrapper},(0,n.Y)(p.A,{when:T},(0,n.Y)(m,{bordered:true},(0,n.Y)("div",{css:Ar.titleAndSub},(0,n.Y)(g,null,(0,i.__)("Overview","tutor")),(0,n.Y)(y,null,(0,i.__)("Provide essential course information to attract and inform potential students","tutor"))),(0,n.Y)("div",{css:Ar.fieldsWrapper},(0,n.Y)(c.xI,{name:"course_benefits",control:s.control,render:function t(e){return(0,n.Y)(nt.A,cr({},e,{label:(0,i.__)("What Will I Learn?","tutor"),placeholder:(0,i.__)("Define the key takeaways from this course (list one benefit per line)","tutor"),rows:2,enableResize:true,loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_BENEFITS}))}}),(0,n.Y)(c.xI,{name:"course_target_audience",control:s.control,render:function t(e){return(0,n.Y)(nt.A,cr({},e,{label:(0,i.__)("Target Audience","tutor"),placeholder:(0,i.__)("Specify the target audience that will benefit the most from the course. (One Line Per target audience)","tutor"),rows:2,enableResize:true,loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_TARGET_AUDIENCE}))}}),(0,n.Y)(p.A,{when:b},(0,n.Y)("div",{css:Ar.totalCourseDuration},(0,n.Y)(c.xI,{name:"course_duration_hours",control:s.control,render:function t(e){return(0,n.Y)(rt.A,cr({},e,{type:"number",label:(0,i.__)("Total Course Duration","tutor"),placeholder:"0",contentPosition:"right",content:(0,i.__)("hour(s)","tutor"),loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.TOTAL_COURSE_DURATION}))}}),(0,n.Y)(c.xI,{name:"course_duration_minutes",control:s.control,render:function t(e){return(0,n.Y)(rt.A,cr({},e,{type:"number",placeholder:"0",contentPosition:"right",content:(0,i.__)("min(s)","tutor"),loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.TOTAL_COURSE_DURATION}))}}))),(0,n.Y)(c.xI,{name:"course_material_includes",control:s.control,render:function t(e){return(0,n.Y)(nt.A,cr({},e,{label:(0,i.__)("Materials Included","tutor"),placeholder:(0,i.__)("A list of assets you will be providing for the students in this course (One Per Line)","tutor"),rows:4,enableResize:true,loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_MATERIALS_INCLUDES}))}}),(0,n.Y)(c.xI,{name:"course_requirements",control:s.control,render:function t(e){return(0,n.Y)(nt.A,cr({},e,{label:(0,i.__)("Requirements/Instructions","tutor"),placeholder:(0,i.__)("Additional requirements or special instructions for the students (One Per Line)","tutor"),rows:2,enableResize:true,loading:!!f&&!e.field.value,visibilityKey:A.qP.COURSE_BUILDER.ADDITIONAL.COURSE_REQUIREMENTS}))}})))),(0,n.Y)(p.A,{when:!mr||S&&wr},(0,n.Y)(m,{bordered:true},(0,n.Y)("div",{css:Ar.titleAndSub},(0,n.Y)(g,{css:Ar.titleWithBadge},(0,i.__)("Certificate","tutor"),(0,n.Y)(p.A,{when:!mr},(0,n.Y)(_.A,{content:(0,i.__)("Pro","tutor")}))),(0,n.Y)(p.A,{when:mr&&(0,O.GR)(A.oW.TUTOR_CERTIFICATE)},(0,n.Y)(y,null,(0,i.__)("Select a certificate to award your learners.","tutor")))),(0,n.Y)(p.A,{when:!f,fallback:(0,n.Y)(w.YE,null)},(0,n.Y)(Rt,{isSidebarVisible:D})))),(0,n.Y)(er.A,{section:"Additional.after_certificates",form:s}))),(0,n.Y)(p.A,{when:!j&&D},(0,n.Y)("div",{css:Ar.formWrapper},(0,n.Y)(L,null))),(0,n.Y)(p.A,{when:A.vN.isAboveTablet},(0,n.Y)(nr.A,null))),(0,n.Y)(p.A,{when:D&&j},(0,n.Y)("div",{css:Ar.sidebar},(0,n.Y)(L,null))),(0,n.Y)(p.A,{when:!A.vN.isAboveTablet},(0,n.Y)(nr.A,null)))};const xr=_r;var Ar={wrapper:function t(e){var r=e.showSidebar;return(0,n.AH)("display:grid;grid-template-columns:",r?"1fr 338px":"1fr",";width:100%;",d.EA.smallTablet,"{grid-template-columns:1fr;gap:",d.YK[24],";}"+(true?"":0),true?"":0)},leftSide:(0,n.AH)("padding:",d.YK[32]," ",d.YK[32]," ",d.YK[32]," 0;",k.x.display.flex("column")," gap:",d.YK[32],";",d.EA.smallTablet,"{padding:0;padding-top:",d.YK[16],";gap:",d.YK[16],";}"+(true?"":0),true?"":0),formWrapper:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[24],";"+(true?"":0),true?"":0),titleAndSub:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[4],";margin-bottom:",d.YK[20],";"+(true?"":0),true?"":0),titleWithBadge:(0,n.AH)("span{",k.x.display.flex(),";align-items:center;gap:",d.YK[4],";}"+(true?"":0),true?"":0),fieldsWrapper:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[24],";"+(true?"":0),true?"":0),totalCourseDuration:(0,n.AH)(k.x.display.flex()," align-items:end;gap:",d.YK[8],";&>div{flex:1;}"+(true?"":0),true?"":0),sidebar:(0,n.AH)(k.x.display.flex("column")," padding:",d.YK[32]," 0 ",d.YK[32]," ",d.YK[32],";border-left:1px solid ",d.I6.stroke.divider,";min-height:calc(100vh - (",d.$A,"px + ",d.P3,"px));gap:",d.YK[16],";",d.EA.smallTablet,"{padding:0;padding-top:",d.YK[24],";border-left:none;border-top:1px solid ",d.I6.stroke.divider,";}"+(true?"":0),true?"":0),label:(0,n.AH)(k.x.display.inlineFlex()," align-items:center;gap:",d.YK[4],";",f.I.body("medium")," color:",d.I6.text.title,";margin-bottom:",d.YK[8],";"+(true?"":0),true?"":0),sidebarContent:(0,n.AH)(k.x.display.flex("column")," gap:",d.YK[16],";"+(true?"":0),true?"":0)}},26434:(t,e,r)=>{r.d(e,{A:()=>x});var n=r(12470);var o=r.n(n);var a=r(12262);var i=r(942);var u=r(52474);var s=r(50707);var c=r(78621);var l=r(94242);var d=r(48465);var f=r(15888);var p=r(96755);var v=r(12721);var h=r(62352);var m=r(6502);var g=r(17437);var y;var b=!!d.P.tutor_pro_url;var w=(y=d.P.settings)===null||y===void 0?void 0:y.chatgpt_key_exist;var _=function t(e){var r=e.field,o=e.fieldState,d=e.label,f=e.size,y=e.helpText,_=e.buttonText,x=_===void 0?(0,n.__)("Upload Media","tutor"):_,A=e.infoText,Y=e.onChange,k=e.generateWithAi,O=k===void 0?false:k,S=e.previewImageCss,I=e.loading,E=e.onClickAiButton;var C=(0,s.h)(),M=C.showModal;var T=(0,p.A)({options:{type:"image",multiple:false},onChange:function t(e){if(e&&!Array.isArray(e)){var n=e.id,o=e.url,a=e.title;r.onChange({id:n,url:o,title:a});if(Y){Y({id:n,url:o,title:a})}}},initialFiles:r.value}),j=T.openMediaLibrary,D=T.resetFiles;var L=r.value;var P=function t(){j()};var N=function t(){D();r.onChange(null);if(Y){Y(null)}};var H=function t(){if(!b){M({component:c.A,props:{image:h.A,image2x:v.A}})}else if(!w){M({component:l.A,props:{image:h.A,image2x:v.A}})}else{M({component:u.A,isMagicAi:true,props:{title:(0,n.__)("AI Studio","tutor"),icon:(0,g.Y)(i.A,{name:"magicAiColorize",width:24,height:24}),field:r,fieldState:o}});E===null||E===void 0||E()}};return(0,g.Y)(m.A,{label:d,field:r,fieldState:o,helpText:y,onClickAiButton:H,generateWithAi:O},(function(){return(0,g.Y)("div",null,(0,g.Y)(a.A,{size:f,value:L,uploadHandler:P,clearHandler:N,buttonText:x,infoText:A,previewImageCss:S,loading:I}))}))};const x=(0,f.M)(_)},26496:(t,e,r)=>{r.d(e,{m:()=>u});var n=r(13747);var o=r(74084);var a=r(31308);var i=r(8008);function u(t,e,r){const[u,s]=(0,o.x)(r?.in,t,e);const c=(0,i.o)(u);const l=(0,i.o)(s);const d=+c-(0,n.G)(c);const f=+l-(0,n.G)(l);return Math.round((d-f)/a.w4)}var s=null&&u},27256:(t,e,r)=>{r.d(e,{w:()=>o});var n=r(31308);function o(t,e){if(typeof t==="function")return t(e);if(t&&typeof t==="object"&&n._P in t)return t[n._P](e);if(t instanceof Date)return new t.constructor(e);return new Date(e)}var a=null&&o},27372:(t,e,r)=>{r.d(e,{C:()=>o});var n=r(49164);function o(t,e){return(0,n.a)(t,e?.in).getFullYear()}var a=null&&o},27787:(t,e,r)=>{r.d(e,{l:()=>o});var n=r(41594);function o(t){return n.createElement("select",{...t})}},27793:(t,e,r)=>{r.d(e,{A:()=>c});var n=r(17437);var o=r(52457);var a=r(45538);var i=r(34419);var u=r(942);var s=function t(e){var r=e.children,o=e.content,s=e.size,c=s===void 0?"regular":s,f=e.textOnly;return(0,n.Y)("div",{css:d.wrapper({hasChildren:(0,i.O9)(r),size:c})},r,(0,n.Y)(a.A,{when:!(0,i.O9)(r)&&!f},(0,n.Y)(u.A,{name:c==="tiny"?"crownRoundedSmall":"crownRounded",width:l[c].iconSize,height:l[c].iconSize})),(0,n.Y)("div",{css:d.content({hasChildren:(0,i.O9)(r),size:c,textOnly:f})},(0,i.O9)(r)?(0,n.Y)(u.A,{name:c==="tiny"?"crownRoundedSmall":"crownRounded",width:c==="tiny"?l[c].iconSize:16}):o))};const c=s;var l={tiny:{borderRadius:o.YK[10],height:o.YK[10],gap:o.YK[2],iconSize:10,fontSize:"0.5rem",lineHeight:"0.625rem"},small:{borderRadius:o.YK[16],height:o.YK[16],gap:o.YK[4],iconSize:16,fontSize:o.J[10],lineHeight:o.K_[16]},regular:{borderRadius:"22px",height:"22px",gap:"5px",iconSize:22,fontSize:o.J[14],lineHeight:o.K_[18]},large:{borderRadius:"26px",height:"26px",gap:o.YK[6],iconSize:26,fontSize:o.J[16],lineHeight:o.K_[26]}};var d={wrapper:function t(e){var r=e.hasChildren,o=e.size,a=o===void 0?"regular":o;return(0,n.AH)("position:relative;svg{flex-shrink:0;}",!r&&(0,n.AH)("height:",l[a].height,";display:inline-flex;border-radius:",l[a].borderRadius,";align-items:center;gap:",l[a].gap,";overflow:hidden;background:linear-gradient(88.9deg, #d65702 6.26%, #e5803c 91.4%);"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},content:function t(e){var r=e.hasChildren,a=e.size,i=a===void 0?"regular":a,u=e.textOnly;return(0,n.AH)("position:absolute;top:0;right:0;display:flex;flex-shrink:0;transform:translateX(50%) translateY(-50%);",!r&&(0,n.AH)("display:inline-flex;position:static;transform:none;padding:",o.YK[2],";color:",o.I6.icon.white,";margin-right:",l[i].gap,";font-size:",l[i].fontSize,";line-height:",l[i].lineHeight,";",u&&(0,n.AH)("padding:0;padding-inline:",o.YK[6],";margin:0;"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},28890:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/bff40839481a6e109932774fea006137-filmic.png"},29106:(t,e,r)=>{r.d(e,{m:()=>o});var n=r(41594);function o(t){return n.createElement("tbody",{...t})}},29200:(t,e,r)=>{r.d(e,{A:()=>v});var n=r(52457);var o=r(62246);var a=r(47849);var i=r(17437);var u=r(41594);var s=r.n(u);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(null,arguments)}function l(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var d=s().forwardRef((function(t,e){var r=t.id,n=r===void 0?(0,a.Ak)():r,o=t.name,u=t.labelCss,l=t.inputCss,d=t.label,f=d===void 0?"":d,v=t.checked,h=t.value,m=t.disabled,g=m===void 0?false:m,y=t.onChange,b=t.onBlur,w=t.isIndeterminate,_=w===void 0?false:w;var x=function t(e){y===null||y===void 0||y(!_?e.target.checked:true,e)};var A=function t(e){if(typeof e==="string"){return e}if(typeof e==="number"||typeof e==="boolean"||e===null){return String(e)}if(e===undefined){return""}if(s().isValidElement(e)){var r;var n=(r=e.props)===null||r===void 0?void 0:r.children;if(typeof n==="string"){return n}if(Array.isArray(n)){return n.map((function(t){return typeof t==="string"?t:""})).filter(Boolean).join(" ")}}return""};return(0,i.Y)("label",{htmlFor:n,css:[p.container({disabled:g}),u,true?"":0,true?"":0]},(0,i.Y)("input",c({},t,{ref:e,id:n,name:o,type:"checkbox",value:h,checked:!!v,disabled:g,"aria-invalid":t["aria-invalid"],onChange:x,onBlur:b,css:[l,p.checkbox({label:!!f,isIndeterminate:_,disabled:g}),true?"":0,true?"":0]})),(0,i.Y)("span",null),(0,i.Y)("span",{css:[p.label({isDisabled:g}),u,true?"":0,true?"":0],title:A(f)},f))}));var f=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var p={container:function t(e){var r=e.disabled,o=r===void 0?false:r;return(0,i.AH)("position:relative;display:flex;align-items:center;cursor:pointer;user-select:none;color:",n.I6.text.title,";",o&&f,";"+(true?"":0),true?"":0)},label:function t(e){var r=e.isDisabled,a=r===void 0?false:r;return(0,i.AH)(o.I.caption(),";margin-top:",n.YK[2],";color:",n.I6.text.title,";",a&&(0,i.AH)("color:",n.I6.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},checkbox:function t(e){var r=e.label,o=e.isIndeterminate,a=e.disabled;return(0,i.AH)("position:absolute;opacity:0!important;height:0;width:0;&+span{position:relative;cursor:pointer;display:inline-flex;align-items:center;",r&&(0,i.AH)("margin-right:",n.YK[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';background-color:",n.I6.background.white,";border:1px solid ",n.I6.stroke["default"],";border-radius:3px;width:20px;height:20px;}&:checked+span::before{background-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0wLjE2NTM0NCA0Ljg5OTQ2QzAuMTEzMjM1IDQuODQ0OTcgMC4wNzE3MzQ2IDQuNzgxMTUgMC4wNDI5ODg3IDQuNzExM0MtMC4wMTQzMjk2IDQuNTU1NjQgLTAuMDE0MzI5NiA0LjM4NDQ5IDAuMDQyOTg4NyA0LjIyODg0QzAuMDcxMTU0OSA0LjE1ODY4IDAuMTEyNzIzIDQuMDk0NzUgMC4xNjUzNDQgNC4wNDA2OEwxLjAzMzgyIDMuMjAzNkMxLjA4NDkzIDMuMTQzNCAxLjE0ODkgMy4wOTU1NyAxLjIyMDk2IDMuMDYzNjlDMS4yOTAzMiAzLjAzMjEzIDEuMzY1NTQgMy4wMTU2OSAxLjQ0MTY3IDMuMDE1NDRDMS41MjQxOCAzLjAxMzgzIDEuNjA2MDUgMy4wMzAyOSAxLjY4MTU5IDMuMDYzNjlDMS43NTYyNiAzLjA5NzA3IDEuODIzODYgMy4xNDQ1NyAxLjg4MDcxIDMuMjAzNkw0LjUwMDU1IDUuODQyNjhMMTAuMTI0MSAwLjE4ODIwNUMxMC4xNzk0IDAuMTI5NTQ0IDEwLjI0NTQgMC4wODIwNTQyIDEwLjMxODQgMC4wNDgyOTA4QzEwLjM5NDEgMC4wMTU0NjYxIDEwLjQ3NTkgLTAuMDAwOTcyMDU3IDEwLjU1ODMgNC40NDIyOGUtMDVDMTAuNjM1NyAwLjAwMDQ3NTMxOCAxMC43MTIxIDAuMDE3NDc5NSAxMC43ODI0IDAuMDQ5OTI0MkMxMC44NTI3IDAuMDgyMzY4OSAxMC45MTU0IDAuMTI5NTA5IDEwLjk2NjIgMC4xODgyMDVMMTEuODM0NyAxLjAzNzM0QzExLjg4NzMgMS4wOTE0MiAxMS45Mjg4IDEuMTU1MzQgMTEuOTU3IDEuMjI1NUMxMi4wMTQzIDEuMzgxMTYgMTIuMDE0MyAxLjU1MjMxIDExLjk1NyAxLjcwNzk2QzExLjkyODMgMS43Nzc4MSAxMS44ODY4IDEuODQxNjMgMTEuODM0NyAxLjg5NjEzTDQuOTIyOCA4LjgwOTgyQzQuODcxMjkgOC44NzAyMSA0LjgwNzQ3IDguOTE4NzUgNC43MzU2NiA4Ljk1MjE1QzQuNTgyMDIgOS4wMTU5NSA0LjQwOTQ5IDkuMDE1OTUgNC4yNTU4NCA4Ljk1MjE1QzQuMTg0MDQgOC45MTg3NSA0LjEyMDIyIDguODcwMjEgNC4wNjg3MSA4LjgwOTgyTDAuMTY1MzQ0IDQuODk5NDZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');background-repeat:no-repeat;background-size:10px 10px;background-position:center center;border-color:transparent;background-color:",n.I6.icon.brand,";border-radius:",n.Vq[4],";",a&&(0,i.AH)("background-color:",n.I6.icon.disable["default"],";"+(true?"":0),true?"":0),";}",o&&(0,i.AH)("&+span::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='2' fill='none'%3E%3Crect width='10' height='1.5' y='.25' fill='%23fff' rx='.75'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-size:10px;background-position:center center;background-color:",n.I6.brand.blue,";border:0.5px solid ",n.I6.stroke.white,";}"+(true?"":0),true?"":0)," ",a&&(0,i.AH)("&+span{cursor:not-allowed;&::before{border-color:",n.I6.stroke.disable,";}}"+(true?"":0),true?"":0)," &:focus-visible{&+span{border-radius:",n.Vq[2],";outline:2px solid ",n.I6.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const v=d},29214:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/32925d4873712d856f4abc340b3334cb-photo.png"},29318:(t,e,r)=>{r.d(e,{s:()=>o});var n=r(74084);function o(t,e,r){const[o,a]=(0,n.x)(r?.in,t,e);return o.getFullYear()===a.getFullYear()}var a=null&&o},29832:(t,e,r)=>{r.d(e,{A:()=>m});var n=r(52457);var o=r(94083);var a=r(17437);var i=r(41594);var u=r.n(i);function s(t,e){return p(t)||f(t,e)||l(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function p(t){if(Array.isArray(t))return t}function v(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var h=function t(e){var r=e.activeTab,n=e.onChange,o=e.tabList,u=e.orientation,c=u===void 0?"horizontal":u,l=e.disabled,d=l===void 0?false:l,f=e.wrapperCss;var p=(0,i.useRef)(o.map((function(){return(0,i.createRef)()})));var v=(0,i.useState)(),h=s(v,2),m=h[0],g=h[1];(0,i.useEffect)((function(){var t=o.reduce((function(t,e,r){var n,o,a,i;var u=p.current[r];var s={width:((n=u.current)===null||n===void 0?void 0:n.offsetWidth)||0,height:((o=u.current)===null||o===void 0?void 0:o.offsetHeight)||0,left:((a=u.current)===null||a===void 0?void 0:a.offsetLeft)||0,top:((i=u.current)===null||i===void 0?void 0:i.offsetTop)||0};t[e.value]=s;return t}),{});g(t)}),[o]);return(0,a.Y)("div",{css:y.container},(0,a.Y)("div",{css:[y.wrapper(c),f,true?"":0,true?"":0],role:"tablist"},o.map((function(t,e){return(0,a.Y)("button",{key:e,onClick:function e(){n(t.value)},css:y.tabButton({isActive:r===t.value,orientation:c}),disabled:d||t.disabled,type:"button",role:"tab","aria-selected":r===t.value?"true":"false",ref:p.current[e]},t.icon,t.label,t.count!==undefined&&(0,a.Y)("span",null," (",t.count<10&&t.count>0?"0".concat(t.count):t.count,")"),t.activeBadge&&(0,a.Y)("span",{css:y.activeBadge}))}))),(0,a.Y)("span",{css:y.indicator((m===null||m===void 0?void 0:m[r])||{width:0,height:0,left:0,top:0},c)}))};const m=h;var g=true?{name:"vea2vn",styles:"flex-direction:column;align-items:start;box-shadow:none"}:0;var y={container:true?{name:"pw7jst",styles:"position:relative;width:100%"}:0,wrapper:function t(e){return(0,a.AH)("width:100%;display:flex;justify-items:left;align-items:center;flex-wrap:wrap;box-shadow:",n.r7.tabs,";",e==="vertical"&&g,";"+(true?"":0),true?"":0)},indicator:function t(e,r){return(0,a.AH)("width:",e.width,"px;height:3px;position:absolute;left:",e.left,"px;bottom:0;background:",n.I6.brand.blue,";border-radius:",n.Vq[4]," ",n.Vq[4]," 0 0;transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0ms;:dir(rtl){left:auto;right:",e.left,"px;}",r==="vertical"&&(0,a.AH)("width:3px;height:",e.height,"px;top:",e.top,"px;bottom:auto;border-radius:0 ",n.Vq[4]," ",n.Vq[4]," 0;"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},tabButton:function t(e){var r=e.isActive,i=e.orientation;return(0,a.AH)(o.x.resetButton,";font-size:",n.J[15],";line-height:",n.K_[20],";display:flex;justify-content:center;align-items:center;gap:",n.YK[6],";padding:",n.YK[12]," ",n.YK[20],";color:",n.I6.text.subdued,";min-width:130px;position:relative;transition:color 0.3s ease-in-out;border-radius:0px;&:hover,&:focus,&:active{background-color:transparent;color:",n.I6.text.subdued,";box-shadow:none;}&>svg{color:",n.I6.icon["default"],";}",i==="vertical"&&(0,a.AH)("width:100%;border-bottom:1px solid ",n.I6.stroke.border,";justify-content:flex-start;&:hover,&:focus,&:active{border-bottom:1px solid ",n.I6.stroke.border,";}"+(true?"":0),true?"":0)," ",r&&(0,a.AH)("&,&:hover,&:focus,&:active{background-color:",n.I6.background.white,";color:",n.I6.text.primary,";}&>span{color:",n.I6.text.subdued,";}&>svg{color:",n.I6.icon.brand,";}"+(true?"":0),true?"":0)," &:disabled{color:",n.I6.text.disable,";&::before{background:",n.I6.text.disable,";}}&:focus-visible{outline:2px solid ",n.I6.stroke.brand,";outline-offset:-2px;border-radius:",n.Vq[4],";}"+(true?"":0),true?"":0)},activeBadge:(0,a.AH)("display:inline-block;height:8px;width:8px;border-radius:",n.Vq.circle,";background-color:",n.I6.color.success[80],";"+(true?"":0),true?"":0)}},30437:(t,e,r)=>{r.d(e,{A:()=>p});var n=r(66694);var o=r(52457);var a=r(62246);var i=r(17437);var u=r(6502);var s=["css"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(null,arguments)}function l(t,e){if(null==t)return{};var r,n,o=d(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}var f=function t(e){var r=e.field,o=e.fieldState,a=e.label,d=e.options,f=d===void 0?[]:d,p=e.disabled,h=e.wrapperCss,m=e.onSelect,g=e.onSelectRender;return(0,i.Y)(u.A,{field:r,fieldState:o,label:a,disabled:p},(function(t){var e=t.css,o=l(t,s);return(0,i.Y)("div",{css:h},f.map((function(t,a){return(0,i.Y)("div",{key:a},(0,i.Y)(n.A,c({},o,{inputCss:e,value:t.value,label:t.label,disabled:t.disabled||p,labelCss:t.labelCss,checked:r.value===t.value,onChange:function e(){r.onChange(t.value);if(m){m(t)}}})),g&&r.value===t.value&&g(t),t.legend&&(0,i.Y)("span",{css:v.radioLegend},t.legend))})))}))};const p=f;var v={radioLegend:(0,i.AH)("margin-left:",o.YK[28],";",a.I.body(),";color:",o.I6.text.subdued,";"+(true?"":0),true?"":0)}},31094:(t,e,r)=>{r.d(e,{Z:()=>o});var n=r(75583);function o(t,e){const{month:r,defaultMonth:o,today:a=e.today(),numberOfMonths:i=1,endMonth:u,startMonth:s,timeZone:c}=t;let l=r||o||a;const{differenceInCalendarMonths:d,addMonths:f,startOfMonth:p}=e;if(u&&d(u,l)<0){const t=-1*(i-1);l=f(u,t)}if(s&&d(l,s)<0){l=s}l=c?new n.BB(l,c):l;return p(l)}},31240:(t,e,r)=>{r.d(e,{k:()=>n});function n(t){return(e={})=>{const r=e.width?String(e.width):t.defaultWidth;const n=t.formats[r]||t.formats[t.defaultWidth];return n}}},31308:(t,e,r)=>{r.d(e,{_P:()=>O,my:()=>u,w4:()=>s});const n=7;const o=365.2425;const a=Math.pow(10,8)*24*60*60*1e3;const i=-a;const u=6048e5;const s=864e5;const c=6e4;const l=36e5;const d=1e3;const f=525600;const p=43200;const v=1440;const h=60;const m=3;const g=12;const y=4;const b=3600;const w=60;const _=b*24;const x=_*7;const A=_*o;const Y=A/12;const k=Y*3;const O=Symbol.for("constructDateFrom")},31652:(t,e,r)=>{r.d(e,{A:()=>at});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(41594);var u=r.n(i);var s=r(49785);var c=r(38919);var l=r(12262);var d=r(4704);var f=r(942);var p=r(33191);var v=r(21708);var h=r(48465);var m=r(41502);var g=r(52457);var y=r(62246);var b=r(45538);var w=r(15888);var _=r(85420);var x=r(82179);var A=r(30015);var Y=r(96755);var k=r(94083);var O=r(56499);var S=r(6502);var I=r(55435);var E=r(46201);var C=r(41594);function M(t){"@babel/helpers - typeof";return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function T(){return T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},T.apply(null,arguments)}function j(t,e){return H(t)||N(t,e)||L(t,e)||D()}function D(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function L(t,e){if(t){if("string"==typeof t)return P(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(t,e):void 0}}function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function N(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function H(t){if(Array.isArray(t))return t}function W(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(T([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==M(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(M(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,C.prototype={constructor:C,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:T(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function K(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function B(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){K(a,n,o,i,u,"next",t)}function u(t){K(a,n,o,i,u,"throw",t)}i(void 0)}))}}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach((function(e){R(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function R(t,e,r){return(e=F(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function F(t){var e=q(t,"string");return"symbol"==M(e)?e:e+""}function q(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function G(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var V=h.P.supported_video_sources||[];var Q=V.filter((function(t){return t.value!=="html5"}));var $=V.map((function(t){return t.value}));var Z=["vimeo","youtube","html5"];var X={youtube:(0,o.__)("Paste YouTube Video URL","tutor"),vimeo:(0,o.__)("Paste Vimeo Video URL","tutor"),external_url:(0,o.__)("Paste External Video URL","tutor"),shortcode:(0,o.__)("Paste Video Shortcode","tutor"),embedded:(0,o.__)("Paste Embedded Video Code","tutor")};var J={youtube:"youtube",vimeo:"vimeo",shortcode:"shortcode",embedded:"coding"};var tt=function t(e,r){var n={source:"",source_video_id:"",poster:"",poster_url:"",source_html5:"",source_external_url:"",source_shortcode:"",source_youtube:"",source_vimeo:"",source_embedded:""};return e?U(U({},e),r):U(U({},n),r)};var et={youtube:function t(e){var r=e.match(m.j8.YOUTUBE);return r&&r[7].length===11?r[7]:null},vimeo:function t(e){var r=e.match(m.j8.VIMEO);return(r===null||r===void 0?void 0:r[5])||null},shortcode:function t(e){return e.match(m.j8.SHORTCODE)?e:null},url:function t(e){return e.match(m.j8.EXTERNAL_URL)?e:null}};var rt=function(){var t=B(W().mark((function t(e){var r,n;var o,a,i,u,s,c,l;return W().wrap((function t(d){while(1)switch(d.prev=d.next){case 0:o=e.source,a=e.url,i=e.getYouTubeVideoDurationMutation;d.prev=1;u=0;d.t0=o;d.next=d.t0==="vimeo"?6:d.t0==="html5"?19:d.t0==="external_url"?19:d.t0==="youtube"?32:39;break;case 6:d.next=8;return(0,v.y0)(a);case 8:d.t2=r=d.sent;d.t1=d.t2!==null;if(!d.t1){d.next=12;break}d.t1=r!==void 0;case 12:if(!d.t1){d.next=16;break}d.t3=r;d.next=17;break;case 16:d.t3=0;case 17:u=d.t3;return d.abrupt("break",39);case 19:d.next=21;return(0,v.z4)(a);case 21:d.t5=n=d.sent;d.t4=d.t5!==null;if(!d.t4){d.next=25;break}d.t4=n!==void 0;case 25:if(!d.t4){d.next=29;break}d.t6=n;d.next=30;break;case 29:d.t6=0;case 30:u=d.t6;return d.abrupt("break",39);case 32:s=et.youtube(a);if(!s){d.next=38;break}d.next=36;return i.mutateAsync(s);case 36:c=d.sent;u=(0,v.fF)(c.data.duration);case 38:return d.abrupt("break",39);case 39:if(!u){d.next=42;break}l=(0,v.uu)(Math.floor(u));return d.abrupt("return",l);case 42:return d.abrupt("return",null);case 45:d.prev=45;d.t7=d["catch"](1);console.error("Error getting video duration:",d.t7);return d.abrupt("return",null);case 49:case"end":return d.stop()}}),t,null,[[1,45]])})));return function e(r){return t.apply(this,arguments)}}();var nt=true?{name:"10ikx64",styles:"border-style:dashed"}:0;var ot=function t(e){var r,a,u,g,y;var w=e.field,M=e.fieldState,D=e.label,L=e.helpText,P=e.buttonText,N=P===void 0?(0,o.__)("Upload Media","tutor"):P,H=e.infoText,K=e.onChange,z=e.supportedFormats,F=e.loading,q=e.onGetDuration;var G=(0,x.p)({defaultValues:{videoSource:((r=Q[0])===null||r===void 0?void 0:r.value)||"",videoUrl:""}});var ot=(0,p.UB)();var at=(0,i.useState)(false),ut=j(at,2),st=ut[0],ct=ut[1];var lt=(0,i.useState)({hours:0,minutes:0,seconds:0}),dt=j(lt,2),ft=dt[0],pt=dt[1];var vt=(0,i.useState)(""),ht=j(vt,2),mt=ht[0],gt=ht[1];var yt=(0,i.useState)(false),bt=j(yt,2),wt=bt[0],_t=bt[1];var xt=(0,i.useRef)(null);var At=(0,A.t)({isOpen:wt,triggerRef:xt,positionModifier:{top:((a=xt.current)===null||a===void 0?void 0:a.getBoundingClientRect().top)||0,left:0}}),Yt=At.popoverRef,kt=At.position;var Ot=function(){var t=B(W().mark((function t(e){var r,n,o,a;return W().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:if(e){i.next=2;break}return i.abrupt("return");case 2:r=Array.isArray(e)?e[0]:e;n={source:"html5",source_video_id:r.id.toString(),source_html5:r.url};w.onChange(tt(w.value,n));K===null||K===void 0||K(tt(w.value,n));i.prev=6;ct(true);Tt();i.next=11;return(0,v.hp)("external_url",r.url);case 11:o=i.sent;i.next=14;return(0,v.z4)(r.url);case 14:a=i.sent;if(a){i.next=17;break}return i.abrupt("return");case 17:pt((0,v.uu)(Math.floor(a)));if(q){q((0,v.uu)(Math.floor(a)))}if(o){gt(o)}case 20:i.prev=20;ct(false);return i.finish(20);case 23:case"end":return i.stop()}}),t,null,[[6,,20,23]])})));return function e(r){return t.apply(this,arguments)}}();var St=(0,Y.A)({options:{type:z!==null&&z!==void 0&&z.length?z.map((function(t){return"video/".concat(t)})).join(","):"video"},onChange:Ot}),It=St.openMediaLibrary,Et=St.resetFiles;var Ct=(0,Y.A)({options:{type:"image"},onChange:function t(e){if(!e){return}var r=Array.isArray(e)?e[0]:e;var n={poster:r.id.toString(),poster_url:r.url};w.onChange(tt(w.value,n));K===null||K===void 0||K(tt(w.value,n))},initialFiles:(u=w.value)!==null&&u!==void 0&&u.poster?{id:Number(w.value.poster),url:w.value.poster_url,title:""}:null}),Mt=Ct.openMediaLibrary,Tt=Ct.resetFiles;var jt=G.watch("videoSource")||"";var Dt=w.value;(0,i.useEffect)((function(){var t;if(!Dt){return}if(!Dt.source){var e,r;G.setValue("videoSource",(e=Q[0])===null||e===void 0?void 0:e.value);G.setValue("videoUrl",Dt["source_".concat((r=Q[0])===null||r===void 0?void 0:r.value)]||"");return}var n=$.includes(Dt.source);if(!n){w.onChange(tt(Dt,{source:""}));return}G.setValue("videoSource",Dt.source);G.setValue("videoUrl",Dt["source_".concat(Dt.source)]||"");if(!Dt.poster_url&&Z.includes(Dt.source)){var o=Dt.source;ct(true);(0,v.hp)(o,Dt["source_".concat(o)]||"").then((function(t){ct(false);gt(t)}))["finally"]((function(){ct(false)}))}if(Object.values(ft).some((function(t){return t>0}))){return}if(Dt.source==="vimeo"){(0,v.y0)(Dt["source_vimeo"]||"").then((function(t){if(!t){return}pt((0,v.uu)(Math.floor(t)));if(q){q((0,v.uu)(Math.floor(t)))}}))}if(["external_url","html5"].includes(Dt.source)){(0,v.z4)(Dt["source_".concat(Dt.source)]||"").then((function(t){if(!t){return}pt((0,v.uu)(Math.floor(t)));if(q){q((0,v.uu)(Math.floor(t)))}}))}if(Dt.source==="youtube"&&(t=h.P.settings)!==null&&t!==void 0&&t.youtube_api_key_exist){var a;var i=(a=et.youtube(Dt["source_youtube"]||""))!==null&&a!==void 0?a:"";ot.mutateAsync(i).then((function(t){var e=t.data.duration;if(!e){return}var r=(0,v.fF)(e);pt((0,v.uu)(Math.floor(r)));if(q){q((0,v.uu)(Math.floor(r)))}}))}}),[Dt]);if(!$.length){return(0,n.Y)("div",{css:it.emptyMediaWrapper},(0,n.Y)(b.A,{when:D},(0,n.Y)("label",null,D)),(0,n.Y)("div",{css:it.emptyMedia({hasVideoSource:false})},(0,n.Y)("p",{css:it.warningText},(0,n.Y)(f.A,{name:"info",height:20,width:20}),(0,o.__)("No video source selected","tutor")),(0,n.Y)(c.A,{buttonCss:it.selectFromSettingsButton,variant:"secondary",size:"small",icon:(0,n.Y)(f.A,{name:"linkExternal",height:24,width:24}),onClick:function t(){window.open(h.A.VIDEO_SOURCES_SETTINGS_URL,"_blank","noopener")}},(0,o.__)("Select from settings","tutor"))))}var Lt=function t(e){if(e==="video"){It();return}Mt()};var Pt=function t(e){var r=e==="video"?{source:"",source_video_id:"",poster:"",poster_url:""}:{poster:"",poster_url:""};var n=tt(Dt,r);if(e==="video"){Et()}else{Tt()}w.onChange(n);gt("");pt({hours:0,minutes:0,seconds:0});if(K){K(n)}};var Nt=function t(){if(!(Dt!==null&&Dt!==void 0&&Dt.source)||!$.includes(Dt.source)){return false}var e=Dt===null||Dt===void 0?void 0:Dt.source;var r="source_".concat(e);return Dt&&Dt[r]!==""};var Ht=function(){var t=B(W().mark((function t(e){var r,n,o,a,i,u,s;return W().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:ct(true);c.prev=1;r=e.videoSource,n=e.videoUrl;o=R({source:r},"source_".concat(r),n);w.onChange(tt(Dt,o));K===null||K===void 0||K(tt(Dt,o));_t(false);c.next=9;return Promise.all([rt({source:r,url:n,getYouTubeVideoDurationMutation:ot}),Z.includes(r)?(0,v.hp)(r,n):null]);case 9:a=c.sent;i=j(a,2);u=i[0];s=i[1];if(u){pt(u);q===null||q===void 0||q(u)}if(s){gt(s)}case 15:c.prev=15;ct(false);return c.finish(15);case 18:case"end":return c.stop()}}),t,null,[[1,,15,18]])})));return function e(r){return t.apply(this,arguments)}}();var Wt=function t(e){var r=e.trim();if(jt==="embedded")return true;if(jt==="shortcode"){return et.shortcode(r)?true:(0,o.__)("Invalid Shortcode","tutor")}if(!et.url(r)){return(0,o.__)("Invalid URL","tutor")}if(jt==="youtube"&&!et.youtube(r)){return(0,o.__)("Invalid YouTube URL","tutor")}if(jt==="vimeo"&&!et.vimeo(r)){return(0,o.__)("Invalid Vimeo URL","tutor")}return true};return(0,n.Y)(C.Fragment,null,(0,n.Y)(S.A,{label:D,field:w,fieldState:M,helpText:L},(function(){return(0,n.Y)("div",{ref:xt},(0,n.Y)(b.A,{when:!F,fallback:(0,n.Y)("div",{css:it.emptyMedia({hasVideoSource:true})},(0,n.Y)(d.p8,null))},(0,n.Y)(b.A,{when:Nt(),fallback:(0,n.Y)("div",{css:it.emptyMedia({hasVideoSource:true})},(0,n.Y)(b.A,{when:$.includes("html5")},(0,n.Y)(c.A,{"data-cy":"upload-media",size:"small",variant:"secondary",icon:(0,n.Y)(f.A,{name:"monitorPlay",height:24,width:24}),onClick:function t(){Lt("video")}},N)),(0,n.Y)(b.A,{when:$.filter((function(t){return t!=="html5"})).length>0},(0,n.Y)(b.A,{when:!$.includes("html5"),fallback:(0,n.Y)("button",{"data-cy":"add-from-url",type:"button",css:it.urlButton,onClick:function t(){_t((function(t){return!t}))}},(0,o.__)("Add from URL","tutor"))},(0,n.Y)(c.A,{"data-cy":"add-from-url",size:"small",variant:"secondary",icon:(0,n.Y)(f.A,{name:"plusSquareBrand",height:24,width:24}),onClick:function t(){_t((function(t){return!t}))}},(0,o.__)("Add from URL","tutor")))),(0,n.Y)(b.A,{when:$.includes("html5")},(0,n.Y)("p",{css:it.infoTexts},H)))},(function(){var t;return(0,n.Y)("div",{css:it.previewWrapper,"data-cy":"media-preview"},(0,n.Y)("div",{css:it.videoInfoWrapper},(0,n.Y)("div",{css:it.videoInfoCard},(0,n.Y)(f.A,{name:J[Dt===null||Dt===void 0?void 0:Dt.source]||"video",height:36,width:36}),(0,n.Y)("div",{css:it.videoInfo},(0,n.Y)("div",{css:it.videoInfoTitle},(0,n.Y)("div",{css:k.x.text.ellipsis(1)},Z.includes((Dt===null||Dt===void 0?void 0:Dt.source)||"")?Dt===null||Dt===void 0?void 0:Dt["source_".concat(Dt.source)]:(t=V.find((function(t){return t.value===(Dt===null||Dt===void 0?void 0:Dt.source)})))===null||t===void 0?void 0:t.label)))),(0,n.Y)("div",{css:it.actionButtons},(0,n.Y)(b.A,{when:jt!=="html5"},(0,n.Y)("button",{type:"button",css:k.x.actionButton,onClick:function t(){_t(true)}},(0,n.Y)(f.A,{name:"edit",height:24,width:24}))),(0,n.Y)("button",{"data-cy":"remove-video",type:"button",css:k.x.actionButton,onClick:function t(){Pt("video")}},(0,n.Y)(f.A,{name:"cross",height:24,width:24})))),(0,n.Y)("div",{css:it.imagePreview({hasImageInput:Z.includes((Dt===null||Dt===void 0?void 0:Dt.source)||"")})},(0,n.Y)(b.A,{when:Z.includes((Dt===null||Dt===void 0?void 0:Dt.source)||""),fallback:(0,n.Y)("div",{css:it.urlData},G.watch("videoUrl"))},(0,n.Y)(l.A,{value:Dt?{id:Number(Dt.poster)||0,url:Dt.poster_url||mt,title:""}:null,loading:st,isClearAble:!!(Dt!==null&&Dt!==void 0&&Dt.poster),disabled:["vimeo","youtube"].includes((Dt===null||Dt===void 0?void 0:Dt.source)||""),uploadHandler:function t(){return Lt("poster")},clearHandler:function t(){return Pt("poster")},buttonText:(0,o.__)("Upload Thumbnail","tutor"),infoText:(0,o.__)("Upload a thumbnail image for your video","tutor"),emptyImageCss:it.thumbImage,previewImageCss:it.thumbImage,overlayCss:it.thumbImage,replaceButtonText:(0,o.__)("Replace Thumbnail","tutor")}),(0,n.Y)(b.A,{when:ft.hours>0||ft.minutes>0||ft.seconds>0},(0,n.Y)("div",{css:it.duration},ft.hours>0&&"".concat(ft.hours,"h")," ",ft.minutes,"m ",ft.seconds,"s")))))}))))})),(0,n.Y)(A.Z,{isOpen:wt,onClickOutside:function t(){return _t(false)},onEscape:function t(){return _t(false)},animationType:_.J6.fadeIn},(0,n.Y)("div",{ref:Yt,css:[it.popover,R(R(R({},m.V8?"right":"left",kt.left),"top",(g=xt.current)===null||g===void 0?void 0:g.getBoundingClientRect().top),"maxWidth",(y=xt.current)===null||y===void 0?void 0:y.offsetWidth),true?"":0,true?"":0]},(0,n.Y)("div",{css:it.popoverContent},(0,n.Y)(s.xI,{control:G.control,name:"videoSource",rules:U({},(0,O.WN)()),render:function t(e){return(0,n.Y)(I.A,T({},e,{options:Q,disabled:V.length<=1,placeholder:(0,o.__)("Select source","tutor"),hideCaret:V.length<=1}))}}),(0,n.Y)(s.xI,{control:G.control,name:"videoUrl",rules:U(U({},(0,O.WN)()),{},{validate:Wt}),render:function t(e){return(0,n.Y)(E.A,T({},e,{inputCss:nt,rows:2,placeholder:X[jt]||(0,o.__)("Paste Here","tutor")}))}}),(0,n.Y)("div",{css:it.popoverButtonWrapper},(0,n.Y)(c.A,{variant:"text",size:"small",onClick:function t(){_t(false)}},(0,o.__)("Cancel","tutor")),(0,n.Y)(c.A,{"data-cy":"submit-url",variant:"secondary",size:"small",onClick:G.handleSubmit(Ht)},(0,o.__)("Ok","tutor")))))))};const at=(0,w.M)(ot);var it={emptyMediaWrapper:(0,n.AH)(k.x.display.flex("column"),";gap:",g.YK[4],";label{",y.I.caption(),";color:",g.I6.text.title,";}"+(true?"":0),true?"":0),emptyMedia:function t(e){var r=e.hasVideoSource,o=r===void 0?false:r;return(0,n.AH)("width:100%;height:164px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:",g.YK[8],";border:1px dashed ",g.I6.stroke.border,";border-radius:",g.Vq[8],";background-color:",g.I6.background.status.warning,";",o&&(0,n.AH)("background-color:",g.I6.bg.white,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},infoTexts:(0,n.AH)(y.I.tiny(),";color:",g.I6.text.subdued,";"+(true?"":0),true?"":0),warningText:(0,n.AH)(k.x.display.flex(),";align-items:center;gap:",g.YK[4],";",y.I.caption(),";color:",g.I6.text.warning,";"+(true?"":0),true?"":0),selectFromSettingsButton:(0,n.AH)("background:",g.I6.bg.white,";"+(true?"":0),true?"":0),urlData:(0,n.AH)(y.I.caption(),";",k.x.display.flex("column"),";padding:",g.YK[8]," ",g.YK[12],";gap:",g.YK[8],";word-break:break-all;"+(true?"":0),true?"":0),previewWrapper:(0,n.AH)("width:100%;height:100%;border:1px solid ",g.I6.stroke["default"],";border-radius:",g.Vq[8],";overflow:hidden;background-color:",g.I6.bg.white,";"+(true?"":0),true?"":0),videoInfoWrapper:(0,n.AH)(k.x.display.flex(),";justify-content:space-between;align-items:center;gap:",g.YK[20],";padding:",g.YK[8]," ",g.YK[12],";"+(true?"":0),true?"":0),videoInfoCard:(0,n.AH)(k.x.display.flex(),";align-items:center;gap:",g.YK[8],";svg{flex-shrink:0;color:",g.I6.icon.hover,";}"+(true?"":0),true?"":0),videoInfo:(0,n.AH)(k.x.display.flex("column"),";gap:",g.YK[4],";"+(true?"":0),true?"":0),videoInfoTitle:(0,n.AH)(k.x.display.flex(),";",y.I.caption("medium")," word-break:break-all;"+(true?"":0),true?"":0),imagePreview:function t(e){var r=e.hasImageInput;return(0,n.AH)("width:100%;max-height:168px;position:relative;overflow:hidden;background-color:",g.I6.background["default"],";",!r&&(0,n.AH)(k.x.overflowYAuto,";"+(true?"":0),true?"":0),";scrollbar-gutter:auto;&:hover{[data-hover-buttons-wrapper]{opacity:1;}}"+(true?"":0),true?"":0)},duration:(0,n.AH)(y.I.tiny(),";position:absolute;bottom:",g.YK[12],";right:",g.YK[12],";background-color:rgba(0, 0, 0, 0.5);color:",g.I6.text.white,";padding:",g.YK[4]," ",g.YK[8],";border-radius:",g.Vq[6],";pointer-events:none;"+(true?"":0),true?"":0),thumbImage:true?{name:"1fsan1p",styles:"border-radius:0;border:none"}:0,urlButton:(0,n.AH)(k.x.resetButton,";",y.I.small("medium"),";color:",g.I6.text.brand,";border-radius:",g.Vq[2],";padding:0 ",g.YK[4],";margin-bottom:",g.YK[8],";&:focus,&:active,&:hover{background:none;color:",g.I6.text.brand,";}&:focus-visible{outline:2px solid ",g.I6.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0),actionButtons:(0,n.AH)(k.x.display.flex(),";gap:",g.YK[4],";"+(true?"":0),true?"":0),popover:(0,n.AH)("position:absolute;width:100%;z-index:",g.fE.dropdown,";background-color:",g.I6.bg.white,";border-radius:",g.Vq.card,";box-shadow:",g.r7.popover,";"+(true?"":0),true?"":0),popoverContent:(0,n.AH)(k.x.display.flex("column"),";gap:",g.YK[12],";padding:",g.YK[16],";"+(true?"":0),true?"":0),popoverButtonWrapper:(0,n.AH)(k.x.display.flex(),";gap:",g.YK[8],";justify-content:flex-end;"+(true?"":0),true?"":0)}},32850:(t,e,r)=>{r.d(e,{VA:()=>W,i0:()=>H});var n=r(75583);var o=r(80517);var a=r(39669);var i=r(98637);var u=r(2510);var s=r(26496);var c=r(4556);var l=r(80436);var d=r(65636);var f=r(50527);var p=r(1741);var v=r(94528);var h=r(37902);var m=r(11456);var g=r(16331);var y=r(27372);var b=r(64585);var w=r(71271);var _=r(97822);var x=r(21721);var A=r(13249);var Y=r(21309);var k=r(29318);var O=r(78127);var S=r(35093);var I=r(49407);var E=r(90208);var C=r(8008);var M=r(99719);var T=r(74880);var j=r(73524);var D=r(40525);var L=r(56066);var P=r(99905);var N=r(73958);class H{constructor(t,e){this.Date=Date;this.today=()=>{if(this.overrides?.today){return this.overrides.today()}if(this.options.timeZone){return n.BB.tz(this.options.timeZone)}return new this.Date};this.newDate=(t,e,r)=>{if(this.overrides?.newDate){return this.overrides.newDate(t,e,r)}if(this.options.timeZone){return new n.BB(t,e,r,this.options.timeZone)}return new Date(t,e,r)};this.addDays=(t,e)=>this.overrides?.addDays?this.overrides.addDays(t,e):(0,o.f)(t,e);this.addMonths=(t,e)=>this.overrides?.addMonths?this.overrides.addMonths(t,e):(0,a.P)(t,e);this.addWeeks=(t,e)=>this.overrides?.addWeeks?this.overrides.addWeeks(t,e):(0,i.J)(t,e);this.addYears=(t,e)=>this.overrides?.addYears?this.overrides.addYears(t,e):(0,u.e)(t,e);this.differenceInCalendarDays=(t,e)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(t,e):(0,s.m)(t,e);this.differenceInCalendarMonths=(t,e)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(t,e):(0,c.U)(t,e);this.eachMonthOfInterval=t=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(t):(0,l.i)(t);this.endOfBroadcastWeek=t=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(t,this):(0,P.O)(t,this);this.endOfISOWeek=t=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(t):(0,d.g)(t);this.endOfMonth=t=>this.overrides?.endOfMonth?this.overrides.endOfMonth(t):(0,f.p)(t);this.endOfWeek=t=>this.overrides?.endOfWeek?this.overrides.endOfWeek(t,this.options):(0,p.$)(t,this.options);this.endOfYear=t=>this.overrides?.endOfYear?this.overrides.endOfYear(t):(0,v.Q)(t);this.format=(t,e)=>{const r=this.overrides?.format?this.overrides.format(t,e,this.options):(0,h.GP)(t,e,this.options);if(this.options.numerals&&this.options.numerals!=="latn"){return this.replaceDigits(r)}return r};this.getISOWeek=t=>this.overrides?.getISOWeek?this.overrides.getISOWeek(t):(0,m.s)(t);this.getMonth=t=>this.overrides?.getMonth?this.overrides.getMonth(t,this.options):(0,g.t)(t,this.options);this.getYear=t=>this.overrides?.getYear?this.overrides.getYear(t,this.options):(0,y.C)(t,this.options);this.getWeek=t=>this.overrides?.getWeek?this.overrides.getWeek(t,this.options):(0,b.N)(t,this.options);this.isAfter=(t,e)=>this.overrides?.isAfter?this.overrides.isAfter(t,e):(0,w.d)(t,e);this.isBefore=(t,e)=>this.overrides?.isBefore?this.overrides.isBefore(t,e):(0,_.Y)(t,e);this.isDate=t=>this.overrides?.isDate?this.overrides.isDate(t):(0,x.$)(t);this.isSameDay=(t,e)=>this.overrides?.isSameDay?this.overrides.isSameDay(t,e):(0,A.r)(t,e);this.isSameMonth=(t,e)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(t,e):(0,Y.t)(t,e);this.isSameYear=(t,e)=>this.overrides?.isSameYear?this.overrides.isSameYear(t,e):(0,k.s)(t,e);this.max=t=>this.overrides?.max?this.overrides.max(t):(0,O.T)(t);this.min=t=>this.overrides?.min?this.overrides.min(t):(0,S.j)(t);this.setMonth=(t,e)=>this.overrides?.setMonth?this.overrides.setMonth(t,e):(0,I.Z)(t,e);this.setYear=(t,e)=>this.overrides?.setYear?this.overrides.setYear(t,e):(0,E.i)(t,e);this.startOfBroadcastWeek=t=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(t,this):(0,N.l)(t,this);this.startOfDay=t=>this.overrides?.startOfDay?this.overrides.startOfDay(t):(0,C.o)(t);this.startOfISOWeek=t=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(t):(0,M.b)(t);this.startOfMonth=t=>this.overrides?.startOfMonth?this.overrides.startOfMonth(t):(0,T.w)(t);this.startOfWeek=t=>this.overrides?.startOfWeek?this.overrides.startOfWeek(t,this.options):(0,j.k)(t,this.options);this.startOfYear=t=>this.overrides?.startOfYear?this.overrides.startOfYear(t):(0,D.D)(t);this.options={locale:L.c,...t};this.overrides=e}getDigitMap(){const{numerals:t="latn"}=this.options;const e=new Intl.NumberFormat("en-US",{numberingSystem:t});const r={};for(let t=0;t<10;t++){r[t.toString()]=e.format(t)}return r}replaceDigits(t){const e=this.getDigitMap();return t.replace(/\d/g,(t=>e[t]||t))}formatNumber(t){return this.replaceDigits(t.toString())}}const W=new H;const K=null&&W},32917:(t,e,r)=>{r.r(e);r.d(e,{Button:()=>n.$,CaptionLabel:()=>o.$,Chevron:()=>a.c,Day:()=>i.L,DayButton:()=>u.x,Dropdown:()=>s.m,DropdownNav:()=>c.z,Footer:()=>l.w,Month:()=>d.f,MonthCaption:()=>f.P,MonthGrid:()=>p.D,Months:()=>v.i,MonthsDropdown:()=>h.l,Nav:()=>m.s,NextMonthButton:()=>g.i,Option:()=>y.c,PreviousMonthButton:()=>b.u,Root:()=>w.b,Select:()=>_.l,Week:()=>x.j,WeekNumber:()=>k.u,WeekNumberHeader:()=>O.t,Weekday:()=>A.B,Weekdays:()=>Y.S,Weeks:()=>S.m,YearsDropdown:()=>I.w});var n=r(91327);var o=r(77307);var a=r(83180);var i=r(721);var u=r(80985);var s=r(69618);var c=r(92833);var l=r(57836);var d=r(57933);var f=r(22507);var p=r(71941);var v=r(94632);var h=r(78181);var m=r(56362);var g=r(19330);var y=r(4726);var b=r(87006);var w=r(58219);var _=r(27787);var x=r(80007);var A=r(66213);var Y=r(97328);var k=r(72818);var O=r(73531);var S=r(29106);var I=r(49600)},33127:(t,e,r)=>{r.d(e,{C:()=>i});var n=r(16687);var o=r(55115);var a=r(47012);function i(t,e){const r=(0,a.G)(t,e);const i=(0,n.N)(t,e);const u=(0,o.f)(t,e);switch(t.mode){case"single":return r;case"multiple":return i;case"range":return u;default:return undefined}}},35093:(t,e,r)=>{r.d(e,{j:()=>a});var n=r(27256);var o=r(49164);function a(t,e){let r;let a=e?.in;t.forEach((t=>{if(!a&&typeof t==="object")a=n.w.bind(null,t);const e=(0,o.a)(t,a);if(!r||r>e||isNaN(+e))r=e}));return(0,n.w)(a,r||NaN)}var i=null&&a},35179:(t,e,r)=>{r.d(e,{s:()=>n});function n(t){return"Go to the Next Month"}},35227:(t,e,r)=>{r.d(e,{a:()=>n});function n(t){return"Choose the Month"}},35975:(t,e,r)=>{r.d(e,{A:()=>u});var n=r(92890);var o=r(10123);var a=r(619);var i=r(70551);function u(t,e){var r;(0,i.A)(1,arguments);var u=(0,a.A)((0,o["default"])(t.start));var s=(0,o["default"])(t.end);var c=u.getTime();var l=s.getTime();if(c>=l){throw new RangeError("Invalid interval")}var d=[];var f=u;var p=Number((r=e===null||e===void 0?void 0:e.step)!==null&&r!==void 0?r:1);if(p<1||isNaN(p))throw new RangeError("`options.step` must be a number equal to or greater than 1");while(f.getTime()<=l){d.push((0,o["default"])(f));f=(0,n["default"])(f,p)}return d}},36306:(t,e,r)=>{r.d(e,{Z:()=>o});var n=r(32850);function o(t,e=n.VA){return e.format(t,"LLLL")}},37567:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/9b6a9d99adc17e88c06f2ee6fb3c387e-not-found-2x.webp"},37902:(t,e,r)=>{r.d(e,{GP:()=>h});var n=r(56066);var o=r(89441);var a=r(89269);var i=r(67403);var u=r(87982);var s=r(93035);var c=r(49164);const l=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;const d=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;const f=/^'([^]*?)'?$/;const p=/''/g;const v=/[a-zA-Z]/;function h(t,e,r){const f=(0,o.q)();const p=r?.locale??f.locale??n.c;const h=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??f.firstWeekContainsDate??f.locale?.options?.firstWeekContainsDate??1;const g=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??f.weekStartsOn??f.locale?.options?.weekStartsOn??0;const y=(0,c.a)(t,r?.in);if(!(0,s.f)(y)){throw new RangeError("Invalid time value")}let b=e.match(d).map((t=>{const e=t[0];if(e==="p"||e==="P"){const r=i.m[e];return r(t,p.formatLong)}return t})).join("").match(l).map((t=>{if(t==="''"){return{isToken:false,value:"'"}}const e=t[0];if(e==="'"){return{isToken:false,value:m(t)}}if(a._[e]){return{isToken:true,value:t}}if(e.match(v)){throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`")}return{isToken:false,value:t}}));if(p.localize.preprocessor){b=p.localize.preprocessor(y,b)}const w={firstWeekContainsDate:h,weekStartsOn:g,locale:p};return b.map((n=>{if(!n.isToken)return n.value;const o=n.value;if(!r?.useAdditionalWeekYearTokens&&(0,u.xM)(o)||!r?.useAdditionalDayOfYearTokens&&(0,u.ef)(o)){(0,u.Ss)(o,e,String(t))}const i=a._[o[0]];return i(y,o,p.localize,w)})).join("")}function m(t){const e=t.match(f);if(!e){return t}return e[1].replace(p,"'")}var g=null&&h},38547:(t,e,r)=>{r.d(e,{A:()=>i});var n=r(97766);var o;(function(t){t[t["Today"]=0]="Today";t[t["Selected"]=1]="Selected";t[t["LastFocused"]=2]="LastFocused";t[t["FocusedModifier"]=3]="FocusedModifier"})(o||(o={}));function a(t){return!t[n.pL.disabled]&&!t[n.pL.hidden]&&!t[n.pL.outside]}function i(t,e,r,i){let u;let s=-1;for(const c of t){const t=e(c);if(a(t)){if(t[n.pL.focused]&&s<o.FocusedModifier){u=c;s=o.FocusedModifier}else if(i?.isEqualTo(c)&&s<o.LastFocused){u=c;s=o.LastFocused}else if(r(c.date)&&s<o.Selected){u=c;s=o.Selected}else if(t[n.pL.today]&&s<o.Today){u=c;s=o.Today}}}if(!u){u=t.find((t=>a(e(t))))}return u}},38945:(t,e,r)=>{const n=Symbol.for("constructDateFrom")},39669:(t,e,r)=>{r.d(e,{P:()=>a});var n=r(27256);var o=r(49164);function a(t,e,r){const a=(0,o.a)(t,r?.in);if(isNaN(e))return(0,n.w)(r?.in||t,NaN);if(!e){return a}const i=a.getDate();const u=(0,n.w)(r?.in||t,a.getTime());u.setMonth(a.getMonth()+e+1,0);const s=u.getDate();if(i>=s){return u}else{a.setFullYear(u.getFullYear(),u.getMonth(),i);return a}}var i=null&&a},39903:(t,e,r)=>{r.d(e,{G:()=>o});var n=r(84608);function o(t){if(t?.formatMonthCaption&&!t.formatCaption){t.formatCaption=t.formatMonthCaption}if(t?.formatYearCaption&&!t.formatYearDropdown){t.formatYearDropdown=t.formatYearCaption}return{...n,...t}}},40052:(t,e,r)=>{r.d(e,{B:()=>o});const n={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};const o=(t,e,r)=>{let o;const a=n[t];if(typeof a==="string"){o=a}else if(e===1){o=a.one}else{o=a.other.replace("{{count}}",e.toString())}if(r?.addSuffix){if(r.comparison&&r.comparison>0){return"in "+o}else{return o+" ago"}}return o}},40525:(t,e,r)=>{r.d(e,{D:()=>o});var n=r(49164);function o(t,e){const r=(0,n.a)(t,e?.in);r.setFullYear(r.getFullYear(),0,1);r.setHours(0,0,0,0);return r}var a=null&&o},40716:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/7c935ca7690aecae8c42142d8cec660e-sketch.png"},40804:(t,e,r)=>{r.d(e,{A:()=>G});var n=r(44091);var o=r(1261);var a=r(41594);var i=r(17437);function u(t){"@babel/helpers - typeof";return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}function l(t,e,r){return e&&c(t.prototype,e),r&&c(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function d(t,e,r){return e=h(e),f(t,v()?Reflect.construct(e,r||[],h(t).constructor):e.apply(t,r))}function f(t,e){if(e&&("object"==u(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return p(t)}function p(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function v(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(v=function e(){return!!t})()}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}function m(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&g(t,e)}function g(t,e){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},g(t,e)}function y(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=w(t,"string");return"symbol"==u(e)?e:e+""}function w(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var _=function(t){function e(){var t;s(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++){n[o]=arguments[o]}t=d(this,e,[].concat(n));y(t,"state",{hasError:false,error:null});return t}m(e,t);return l(e,[{key:"componentDidCatch",value:function t(e,r){var n,o;console.error("Error rendering ".concat(this.props.componentName,":"),e,r);(n=(o=this.props).onError)===null||n===void 0||n.call(o,e,r)}},{key:"render",value:function t(){var e=this.props,r=e.children,n=e.fallback,a=e.showError;var u=this.state,s=u.hasError,c=u.error;if(s){if(n){return n}return a?(0,i.Y)(o.A,{type:"danger"},"Error rendering ",this.props.componentName,": ",(c===null||c===void 0?void 0:c.message)||(c===null||c===void 0?void 0:c.toString())):null}return r}}],[{key:"getDerivedStateFromError",value:function t(e){return{hasError:true,error:e}}}])}(a.Component);y(_,"defaultProps",{showError:true,componentName:"Component"});const x=_;var A=function t(e){var r=e.component;return(0,i.Y)(x,{componentName:"content"},r)};const Y=A;var k=r(49804);var O=r(85271);var S=r(57215);var I=r(26434);var E=r(97011);var C=r(30437);var M=r(55435);var T=r(47275);var j=r(46201);var D=r(19502);var L=r(31652);var P=r(70349);var N=r(49785);function H(){return H=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},H.apply(null,arguments)}var W=function t(e){var r=e.name,n=e.label,a=e.buttonText,u=e.helpText,s=e.infoText,c=e.placeholder,l=e.type,d=e.options,f=e.defaultValue,p=e.rules,v=e.form;var h=function t(e){var f=function(){switch(l){case"text":return(0,i.Y)(E.A,H({},e,{label:n,placeholder:c,helpText:u}));case"number":return(0,i.Y)(E.A,H({},e,{type:"number",label:n,placeholder:c,helpText:u}));case"password":return(0,i.Y)(E.A,H({},e,{type:"password",label:n,placeholder:c,helpText:u}));case"textarea":return(0,i.Y)(j.A,H({},e,{label:n,placeholder:c,helpText:u}));case"select":return(0,i.Y)(M.A,H({},e,{label:n,options:d||[],placeholder:c,helpText:u}));case"radio":return(0,i.Y)(C.A,H({},e,{label:n,options:d||[]}));case"checkbox":return(0,i.Y)(k.A,H({},e,{label:n}));case"switch":return(0,i.Y)(T.A,H({},e,{label:n,helpText:u}));case"date":return(0,i.Y)(O.A,H({},e,{label:n,placeholder:c,helpText:u}));case"time":return(0,i.Y)(D.A,H({},e,{label:n,placeholder:c,helpText:u}));case"image":return(0,i.Y)(I.A,H({},e,{label:n,buttonText:a,helpText:u,infoText:s}));case"video":return(0,i.Y)(L.A,H({},e,{label:n,buttonText:a,helpText:u,infoText:s}));case"uploader":return(0,i.Y)(S.A,H({},e,{label:n,buttonText:a,helpText:u}));case"WPEditor":return(0,i.Y)(P.A,H({},e,{label:n,placeholder:c,helpText:u}));default:return(0,i.Y)(o.A,{type:"danger"},"Unsupported field type: ",l)}}();return(0,i.Y)(x,{componentName:"field ".concat(r),onError:function t(e,n){console.warn("Field ".concat(r," failed to render:"),{error:e,errorInfo:n})}},f)};return(0,i.Y)(N.xI,{name:r,control:v.control,defaultValue:f!==null&&f!==void 0?f:"",rules:p,render:function t(e){return h(e)}})};const K=W;var B=r(41594);function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},z.apply(null,arguments)}function U(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=R(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function t(){};return{s:o,n:function e(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,u=!1;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();return i=e.done,e},e:function t(e){u=!0,a=e},f:function t(){try{i||null==r["return"]||r["return"]()}finally{if(u)throw a}}}}function R(t,e){if(t){if("string"==typeof t)return F(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?F(t,e):void 0}}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var q=function t(e){var r=e.section,o=e.namePrefix,a=e.form;var u=(0,n._)(),s=u.fields,c=u.contents;var l=function t(){var e=r.split(".");var n=s;var o=U(e),a;try{for(o.s();!(a=o.n()).done;){var i=a.value;if(!n[i])return[];n=n[i]}}catch(t){o.e(t)}finally{o.f()}return Array.isArray(n)?n:[]};var d=function t(){var e=r.split(".");var n=c;var o=U(e),a;try{for(o.s();!(a=o.n()).done;){var i=a.value;if(!n[i])return[];n=n[i]}}catch(t){o.e(t)}finally{o.f()}return Array.isArray(n)?n:[]};return(0,i.Y)(B.Fragment,null,l().map((function(t){return(0,i.Y)(K,z({key:t.name,form:a},t,{name:o?"".concat(o).concat(t.name):t.name}))})),d().map((function(t,e){var r=t.component;return(0,i.Y)(Y,{key:e,component:r})})))};const G=q},41113:t=>{function e(t,e){if(e.styleSheet){e.styleSheet.cssText=t}else{while(e.firstChild){e.removeChild(e.firstChild)}e.appendChild(document.createTextNode(t))}}t.exports=e},44880:(t,e,r)=>{r.d(e,{B:()=>o});var n=r(18525);class o extends n.q{static tz(t,...e){return e.length?new o(...e,t):new o(Date.now(),t)}toISOString(){const[t,e,r]=this.tzComponents();const n=`${t}${e}:${r}`;return this.internal.toISOString().slice(0,-1)+n}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){const[t,e,r,n]=this.internal.toUTCString().split(" ");return`${t?.slice(0,-1)} ${r} ${e} ${n}`}toTimeString(){const t=this.internal.toUTCString().split(" ")[4];const[e,r,n]=this.tzComponents();return`${t} GMT${e}${r}${n} (${a(this.timeZone,this)})`}toLocaleString(t,e){return Date.prototype.toLocaleString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}toLocaleDateString(t,e){return Date.prototype.toLocaleDateString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}toLocaleTimeString(t,e){return Date.prototype.toLocaleTimeString.call(this,t,{...e,timeZone:e?.timeZone||this.timeZone})}tzComponents(){const t=this.getTimezoneOffset();const e=t>0?"-":"+";const r=String(Math.floor(Math.abs(t)/60)).padStart(2,"0");const n=String(Math.abs(t)%60).padStart(2,"0");return[e,r,n]}withTimeZone(t){return new o(+this,t)}[Symbol.for("constructDateFrom")](t){return new o(+new Date(t),this.timeZone)}}function a(t,e){return new Intl.DateTimeFormat("en-GB",{timeZone:t,timeZoneName:"long"}).format(e).slice(12)}},45157:(t,e,r)=>{r.d(e,{G:()=>a});var n=r(32850);var o=r(52044);function a(t,e,r=n.VA){return(0,o.R)(t,e.from,false,r)||(0,o.R)(t,e.to,false,r)||(0,o.R)(e,t.from,false,r)||(0,o.R)(e,t.to,false,r)}},45179:(t,e,r)=>{r.d(e,{g:()=>a});var n=r(97766);var o=r(87331);function a(t,e,r){const{disabled:a,hidden:i,modifiers:u,showOutsideDays:s,broadcastCalendar:c,today:l}=e;const{isSameDay:d,isSameMonth:f,startOfMonth:p,isBefore:v,endOfMonth:h,isAfter:m}=r;const g=e.startMonth&&p(e.startMonth);const y=e.endMonth&&h(e.endMonth);const b={[n.pL.focused]:[],[n.pL.outside]:[],[n.pL.disabled]:[],[n.pL.hidden]:[],[n.pL.today]:[]};const w={};for(const e of t){const{date:t,displayMonth:n}=e;const p=Boolean(n&&!f(t,n));const h=Boolean(g&&v(t,g));const _=Boolean(y&&m(t,y));const x=Boolean(a&&(0,o.k)(t,a,r));const A=Boolean(i&&(0,o.k)(t,i,r))||h||_||!c&&!s&&p||c&&s===false&&p;const Y=d(t,l??r.today());if(p)b.outside.push(e);if(x)b.disabled.push(e);if(A)b.hidden.push(e);if(Y)b.today.push(e);if(u){Object.keys(u).forEach((n=>{const a=u?.[n];const i=a?(0,o.k)(t,a,r):false;if(!i)return;if(w[n]){w[n].push(e)}else{w[n]=[e]}}))}}return t=>{const e={[n.pL.focused]:false,[n.pL.disabled]:false,[n.pL.hidden]:false,[n.pL.outside]:false,[n.pL.today]:false};const r={};for(const r in b){const n=b[r];e[r]=n.some((e=>e===t))}for(const e in w){r[e]=w[e].some((e=>e===t))}return{...e,...r}}}},46068:(t,e,r)=>{r.d(e,{A:()=>y});var n=r(38919);var o=r(41502);var a=r(52457);var i=r(62246);var u=r(85420);var s=r(30015);var c=r(94083);var l=r(17437);var d=r(12470);var f=r.n(d);function p(t){"@babel/helpers - typeof";return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function v(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=m(t,"string");return"symbol"==p(e)?e:e+""}function m(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var g=function t(e){var r,a,i,c,f;var p=e.arrow,h=e.triggerRef,m=e.isOpen,g=e.title,y=e.message,w=e.onConfirmation,_=e.onCancel,x=e.isLoading,A=x===void 0?false:x,Y=e.gap,k=e.maxWidth,O=e.closePopover,S=e.animationType,I=S===void 0?u.J6.slideLeft:S,E=e.hideArrow,C=E===void 0?false:E,M=e.confirmButton,T=e.cancelButton,j=e.positionModifier;var D=(0,s.t)({triggerRef:h,isOpen:m,arrow:p,gap:Y,positionModifier:j}),L=D.position,P=D.triggerWidth,N=D.popoverRef;return(0,l.Y)(s.Z,{isOpen:m,onClickOutside:O,animationType:I},(0,l.Y)("div",{css:[b.wrapper(p?L.arrowPlacement:undefined,C),v(v(v({},o.V8?"right":"left",L.left),"top",L.top),"maxWidth",k!==null&&k!==void 0?k:P),true?"":0,true?"":0],ref:N},(0,l.Y)("div",{css:b.content},(0,l.Y)("div",{css:b.body},(0,l.Y)("div",{css:b.title},g),(0,l.Y)("p",{css:b.description},y)),(0,l.Y)("div",{css:b.footer({isDelete:(r=M===null||M===void 0?void 0:M.isDelete)!==null&&r!==void 0?r:false})},(0,l.Y)(n.A,{variant:(a=T===null||T===void 0?void 0:T.variant)!==null&&a!==void 0?a:"text",size:"small",onClick:_!==null&&_!==void 0?_:O},(i=T===null||T===void 0?void 0:T.text)!==null&&i!==void 0?i:(0,d.__)("Cancel","tutor")),(0,l.Y)(n.A,{"data-cy":"confirm-button",variant:(c=M===null||M===void 0?void 0:M.variant)!==null&&c!==void 0?c:"text",onClick:function t(){w();O()},loading:A,size:"small"},(f=M===null||M===void 0?void 0:M.text)!==null&&f!==void 0?f:(0,d.__)("Ok","tutor"))))))};const y=g;var b={wrapper:function t(e,r){return(0,l.AH)("position:absolute;width:100%;z-index:",a.fE.dropdown,";&::before{",e&&!r&&(0,l.AH)("content:'';position:absolute;border:",a.YK[8]," solid transparent;",e==="left"&&b.arrowLeft," ",e==="right"&&b.arrowRight," ",e==="top"&&b.arrowTop," ",e==="bottom"&&b.arrowBottom,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},arrowLeft:(0,l.AH)("border-right-color:",a.I6.surface.tutor,";top:50%;transform:translateY(-50%);left:-",a.YK[16],";"+(true?"":0),true?"":0),arrowRight:(0,l.AH)("border-left-color:",a.I6.surface.tutor,";top:50%;transform:translateY(-50%);right:-",a.YK[16],";"+(true?"":0),true?"":0),arrowTop:(0,l.AH)("border-bottom-color:",a.I6.surface.tutor,";left:50%;transform:translateX(-50%);top:-",a.YK[16],";"+(true?"":0),true?"":0),arrowBottom:(0,l.AH)("border-top-color:",a.I6.surface.tutor,";left:50%;transform:translateX(-50%);bottom:-",a.YK[16],";"+(true?"":0),true?"":0),content:(0,l.AH)("background-color:",a.I6.surface.tutor,";box-shadow:",a.r7.popover,";border-radius:",a.Vq[6],";::-webkit-scrollbar{background-color:",a.I6.surface.tutor,";width:10px;}::-webkit-scrollbar-thumb{background-color:",a.I6.action.secondary["default"],";border-radius:",a.Vq[6],";}"+(true?"":0),true?"":0),title:(0,l.AH)(i.I.small("medium"),";color:",a.I6.text.primary,";"+(true?"":0),true?"":0),description:(0,l.AH)(i.I.small(),";color:",a.I6.text.subdued,";"+(true?"":0),true?"":0),body:(0,l.AH)("padding:",a.YK[16]," ",a.YK[20]," ",a.YK[12],";",c.x.display.flex("column"),";gap:",a.YK[8],";"+(true?"":0),true?"":0),footer:function t(e){var r=e.isDelete,n=r===void 0?false:r;return(0,l.AH)(c.x.display.flex(),";padding:",a.YK[4]," ",a.YK[16]," ",a.YK[8],";justify-content:end;gap:",a.YK[10],";",n&&(0,l.AH)("button:last-of-type{color:",a.I6.text.error,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},46483:(t,e,r)=>{r.d(e,{j:()=>n});class n{constructor(t,e){this.days=e;this.weekNumber=t}}},47012:(t,e,r)=>{r.d(e,{G:()=>o});var n=r(74612);function o(t,e){const{selected:r,required:o,onSelect:a}=t;const[i,u]=(0,n.j)(r,a?r:undefined);const s=!a?i:r;const{isSameDay:c}=e;const l=t=>s?c(s,t):false;const d=(t,e,r)=>{let n=t;if(!o&&s&&s&&c(t,s)){n=undefined}if(!a){u(n)}if(o){a?.(n,t,e,r)}else{a?.(n,t,e,r)}return n};return{selected:s,select:d,isSelected:l}}},47839:(t,e,r)=>{r.d(e,{A:()=>A});var n=r(23777);var o=r(78137);var a=r(38919);var i=r(942);var u=r(41502);var s=r(52457);var c=r(45538);var l=r(28312);var d=r(17437);var f=r(12470);var p=r.n(f);var v=r(49785);var h=r(47767);function m(t){"@babel/helpers - typeof";return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return(e=w(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t){var e=_(t,"string");return"symbol"==m(e)?e:e+""}function _(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x=function t(e){var r=e.styleModifier;var p=(0,o._)(),m=p.steps,g=p.setSteps;var b=(0,h.Zp)();var w=(0,l.G)(n.A);var _=(0,v.xW)();var x=m.findIndex((function(t){return t.path===w}));var A=Math.max(-1,x-1);var k=Math.min(m.length,x+1);var O=m[A];var S=m[k];var I=_.watch("post_title");var E=function t(){g((function(t){return t.map((function(t,e){if(e===x){return y(y({},t),{},{isActive:false})}if(e===A){return y(y({},t),{},{isActive:true})}return t}))}));b(O.path)};var C=function t(){g((function(t){return t.map((function(t,e){if(e===x){return y(y({},t),{},{isActive:false})}if(e===k){return y(y({},t),{},{isActive:true})}return t}))}));b(S.path)};return(0,d.Y)("div",{css:[Y.wrapper,r,true?"":0,true?"":0]},(0,d.Y)(c.A,{when:x>0},(0,d.Y)(a.A,{variant:"tertiary",iconPosition:"right",size:"small",onClick:E,buttonCss:(0,d.AH)("padding:",s.YK[4],";svg{color:",s.I6.icon["default"],";}"+(true?"":0),true?"":0),disabled:A<0},(0,d.Y)(i.A,{name:!u.V8?"chevronLeft":"chevronRight",height:24,width:24}))),(0,d.Y)(c.A,{when:x<m.length-1&&I},(0,d.Y)(a.A,{variant:"tertiary",icon:(0,d.Y)(i.A,{name:!u.V8?"chevronRight":"chevronLeft",height:24,width:24}),iconPosition:"right",size:"small",onClick:C,buttonCss:(0,d.AH)("padding:",s.YK[4]," ",s.YK[4]," ",s.YK[4]," ",s.YK[12],";svg{color:",s.I6.icon["default"],";}"+(true?"":0),true?"":0),disabled:!I||k>=m.length},(0,f.__)("Next","tutor"))))};const A=x;var Y={wrapper:(0,d.AH)("width:100%;display:flex;justify-content:end;height:32px;align-items:center;gap:",s.YK[16],";"+(true?"":0),true?"":0)}},48665:(t,e,r)=>{r.d(e,{j:()=>n});class n{constructor(t,e){this.date=t;this.weeks=e}}},49164:(t,e,r)=>{r.d(e,{a:()=>o});var n=r(27256);function o(t,e){return(0,n.w)(e||t,t)}var a=null&&o},49407:(t,e,r)=>{r.d(e,{Z:()=>i});var n=r(27256);var o=r(21843);var a=r(49164);function i(t,e,r){const i=(0,a.a)(t,r?.in);const u=i.getFullYear();const s=i.getDate();const c=(0,n.w)(r?.in||t,0);c.setFullYear(u,e,15);c.setHours(0,0,0,0);const l=(0,o.P)(c);i.setMonth(e,Math.min(s,l));return i}var u=null&&i},49600:(t,e,r)=>{r.d(e,{w:()=>a});var n=r(41594);var o=r(51409);function a(t){const{components:e}=(0,o.w)();return n.createElement(e.Dropdown,{...t})}},49804:(t,e,r)=>{r.d(e,{A:()=>p});var n=r(29200);var o=r(52457);var a=r(62246);var i=r(17437);var u=r(6502);var s=["css"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(null,arguments)}function l(t,e){if(null==t)return{};var r,n,o=d(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}var f=function t(e){var r=e.field,o=e.fieldState,a=e.disabled,d=e.value,f=e.onChange,p=e.label,h=e.description,m=e.isHidden,g=e.labelCss;return(0,i.Y)(u.A,{field:r,fieldState:o,isHidden:m},(function(t){var e=t.css,o=l(t,s);return(0,i.Y)("div",null,(0,i.Y)(n.A,c({},r,o,{inputCss:e,labelCss:g,value:d,disabled:a,checked:r.value,label:p,onChange:function t(){r.onChange(!r.value);if(f){f(!r.value)}}})),h&&(0,i.Y)("p",{css:v.description},h))}))};const p=f;var v={description:(0,i.AH)(a.I.small()," color:",o.I6.text.hints,";padding-left:30px;margin-top:",o.YK[6],";"+(true?"":0),true?"":0)}},50527:(t,e,r)=>{r.d(e,{p:()=>o});var n=r(49164);function o(t,e){const r=(0,n.a)(t,e?.in);const o=r.getMonth();r.setFullYear(r.getFullYear(),o+1,0);r.setHours(23,59,59,999);return r}var a=null&&o},50558:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/dec5e33b385ba1a7c841dde2b6c1a5af-illustration.png"},51032:(t,e,r)=>{r.d(e,{TM:()=>h,gr:()=>g});var n=r(97286);var o=r(24880);var a=r(94747);var i=r(4862);var u=r(21508);function s(t){"@babel/helpers - typeof";return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==s(e)?e:e+""}function p(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var v=function t(e){return i.b.get(u.A.GET_COURSE_LIST,{params:e})};var h=function t(e){var r=e.params,a=e.isEnabled;return(0,n.I)({queryKey:["PrerequisiteCourses",r],queryFn:function t(){return v(l({exclude:r.exclude,limit:r.limit,offset:r.offset,filter:r.filter},r.post_status&&{post_status:r.post_status})).then((function(t){return t.data}))},placeholderData:o.rX,enabled:a})};var m=function t(e){var r=e.courseId,n=e.builder;return i.b.post(u.A.TUTOR_UNLINK_PAGE_BUILDER,{course_id:r,builder:n})};var g=function t(){return(0,a.n)({mutationFn:m})};var y=function t(e){return wpAjaxInstance.get(endpoints.BUNDLE_LIST,{params:e})};var b=function t(e){var r=e.params,n=e.isEnabled;return useQuery({queryKey:["PrerequisiteCourses",r],queryFn:function t(){return y(l({exclude:r.exclude,limit:r.limit,offset:r.offset,filter:r.filter},r.post_status&&{post_status:r.post_status})).then((function(t){return t.data}))},placeholderData:keepPreviousData,enabled:n})}},51409:(t,e,r)=>{r.d(e,{S:()=>o,w:()=>a});var n=r(41594);const o=(0,n.createContext)(undefined);function a(){const t=(0,n.useContext)(o);if(t===undefined){throw new Error("useDayPicker() must be used within a custom component.")}return t}},52044:(t,e,r)=>{r.d(e,{R:()=>o});var n=r(32850);function o(t,e,r=false,o=n.VA){let{from:a,to:i}=t;const{differenceInCalendarDays:u,isSameDay:s}=o;if(a&&i){const t=u(i,a)<0;if(t){[a,i]=[i,a]}const n=u(e,a)>=(r?1:0)&&u(i,e)>=(r?1:0);return n}if(!r&&i){return s(i,e)}if(!r&&a){return s(a,e)}return false}const a=(t,e)=>o(t,e,false,defaultDateLib)},52474:(t,e,r)=>{r.d(e,{A:()=>l});var n=r(11260);var o=r(92887);var a=r(3884);var i=r(95790);var u=r(17437);function s(){var t=(0,n.A6)(),e=t.state;switch(e){case"generation":return(0,u.Y)(o.u,null);case"magic-fill":return(0,u.Y)(a.A,null);default:return null}}var c=function t(e){var r=e.title,o=e.icon,a=e.closeModal,c=e.field,l=e.fieldState;return(0,u.Y)(i.A,{onClose:a,title:r,icon:o,maxWidth:1e3},(0,u.Y)(n.co,{field:c,fieldState:l,onCloseModal:a},(0,u.Y)(s,null)))};const l=c},52756:(t,e,r)=>{r.d(e,{Z:()=>o});var n=r(32850);function o(t,e,r){return(r??new n.i0(e)).format(t,"cccccc")}},52854:(t,e,r)=>{r.d(e,{C:()=>a});var n=r(52457);var o=r(17437);var a={wrapper:(0,o.AH)("min-width:1000px;display:grid;grid-template-columns:1fr 330px;",n.EA.tablet,"{min-width:auto;grid-template-columns:1fr;width:100%;}"+(true?"":0),true?"":0),left:(0,o.AH)("display:flex;justify-content:center;align-items:center;background-color:#f7f7f7;z-index:",n.fE.level,";"+(true?"":0),true?"":0),right:(0,o.AH)("padding:",n.YK[20],";display:flex;flex-direction:column;align-items:space-between;z-index:",n.fE.positive,";"+(true?"":0),true?"":0),rightFooter:(0,o.AH)("display:flex;flex-direction:column;gap:",n.YK[8],";margin-top:auto;padding-top:80px;"+(true?"":0),true?"":0)}},53581:(t,e,r)=>{r.d(e,{i:()=>i});var n=r(41594);var o=r(38547);var a=r(91725);function i(t,e,r,i,u){const{autoFocus:s}=t;const[c,l]=(0,n.useState)();const d=(0,o.A)(e.days,r,i||(()=>false),c);const[f,p]=(0,n.useState)(s?d:undefined);const v=()=>{l(f);p(undefined)};const h=(r,n)=>{if(!f)return;const o=(0,a.O)(r,n,f,e.navStart,e.navEnd,t,u);if(!o)return;e.goToDay(o);p(o)};const m=t=>Boolean(d?.isEqualTo(t));const g={isFocusTarget:m,setFocused:p,focused:f,blur:v,moveFocus:h};return g}},53804:(t,e,r)=>{r.d(e,{A:()=>z});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(53429);var u=r(41594);var s=r.n(u);var c=r(49785);var l=r(38919);var d=r(4704);var f=r(85271);var p=r(97011);var v=r(46201);var h=r(19502);var m=r(52457);var g=r(62246);var y=r(82179);var b=r(33191);var w=r(80953);var _=r(21708);var x=r(55435);var A=r(48465);var Y=r(41502);var k=r(45538);var O=r(55077);var S=r(94083);var I=r(34419);var E=r(56499);function C(t){"@babel/helpers - typeof";return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}function M(){return M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},M.apply(null,arguments)}function T(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */T=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new M(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(j([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==C(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(C(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,M.prototype={constructor:M,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:j(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach((function(e){L(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function L(t,e,r){return(e=P(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function P(t){var e=N(t,"string");return"symbol"==C(e)?e:e+""}function N(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function H(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function W(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){H(a,n,o,i,u,"next",t)}function u(t){H(a,n,o,i,u,"throw",t)}i(void 0)}))}}var K=(0,_.p9)();var B=function t(e){var r,a,s,m,g,_,S,C,j;var L=e.onCancel,P=e.data,N=e.meetingHost,H=e.topicId,B=e.meetingId;var z=(0,O.$)({defaultValue:true}),R=z.ref,F=z.isScrolling;var q=(0,w.gK)(B?B:"",H?H:"");var G=P!==null&&P!==void 0?P:q.data;var V=(r=(a=G===null||G===void 0?void 0:G.meeting_starts_at)!==null&&a!==void 0?a:G===null||G===void 0?void 0:G.meeting_data.start_time)!==null&&r!==void 0?r:"";var Q=(0,y.p)({defaultValues:{meeting_name:(s=G===null||G===void 0?void 0:G.post_title)!==null&&s!==void 0?s:"",meeting_summary:(m=G===null||G===void 0?void 0:G.post_content)!==null&&m!==void 0?m:"",meeting_date:V?(0,i["default"])(new Date(V),Y.Bd.yearMonthDay):"",meeting_time:V?(0,i["default"])(new Date(V),Y.Bd.hoursMinutes):"",meeting_duration:G!==null&&G!==void 0&&G.meeting_data.duration?String(G===null||G===void 0?void 0:G.meeting_data.duration):"40",meeting_duration_unit:(g=G===null||G===void 0?void 0:G.meeting_data.duration_unit)!==null&&g!==void 0?g:"min",meeting_timezone:(_=G===null||G===void 0?void 0:G.meeting_data.timezone)!==null&&_!==void 0?_:"",auto_recording:(S=G===null||G===void 0||(C=G.meeting_data.settings)===null||C===void 0?void 0:C.auto_recording)!==null&&S!==void 0?S:"none",meeting_password:(j=G===null||G===void 0?void 0:G.meeting_data.password)!==null&&j!==void 0?j:"",meeting_host:Object.keys(N).length===1?Object.keys(N)[0]:""},shouldFocusError:true,mode:"onChange"});var $=(0,b.s1)();var Z=A.P.timezones;var X=Object.keys(Z).map((function(t){return{label:Z[t],value:t}}));var J=Object.keys(N).map((function(t){return{label:N[t],value:t}}));var tt=function(){var t=W(T().mark((function t(e){var r;return T().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(K){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return $.mutateAsync(D(D(D({},G&&{meeting_id:Number(G.ID)}),H&&{topic_id:Number(H)}),{},{course_id:K,meeting_title:e.meeting_name,meeting_summary:e.meeting_summary,meeting_date:e.meeting_date,meeting_time:e.meeting_time,meeting_duration:Number(e.meeting_duration),meeting_duration_unit:e.meeting_duration_unit,meeting_timezone:e.meeting_timezone,auto_recording:e.auto_recording,meeting_password:e.meeting_password,click_form:H?"course_builder":"metabox",meeting_host:e.meeting_host}));case 4:r=n.sent;if(r.data){L();Q.reset()}case 6:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){if((0,I.O9)(G)){var t,e;Q.reset({meeting_name:G.post_title,meeting_summary:G.post_content,meeting_date:V?(0,i["default"])(new Date(V),Y.Bd.yearMonthDay):"",meeting_time:V?(0,i["default"])(new Date(V),Y.Bd.hoursMinutes):"",meeting_duration:String(G.meeting_data.duration),meeting_duration_unit:G.meeting_data.duration_unit,meeting_timezone:G.meeting_data.timezone,auto_recording:(t=(e=G.meeting_data.settings)===null||e===void 0?void 0:e.auto_recording)!==null&&t!==void 0?t:"none",meeting_password:G.meeting_data.password,meeting_host:G.meeting_data.host_id})}var r=setTimeout((function(){Q.setFocus("meeting_name")}),250);return function(){clearTimeout(r)}}),[G]);return(0,n.Y)("div",{css:U.container},(0,n.Y)("div",{css:U.formWrapper,ref:R},(0,n.Y)(k.A,{when:!q.isLoading,fallback:(0,n.Y)(d.p8,null)},(0,n.Y)(c.xI,{name:"meeting_name",control:Q.control,rules:{required:(0,o.__)("Name is required","tutor")},render:function t(e){return(0,n.Y)(p.A,M({},e,{label:(0,o.__)("Meeting Name","tutor"),placeholder:(0,o.__)("Enter meeting name","tutor")}))}}),(0,n.Y)(c.xI,{name:"meeting_summary",control:Q.control,rules:{required:(0,o.__)("Summary is required","tutor")},render:function t(e){return(0,n.Y)(v.A,M({},e,{label:(0,o.__)("Meeting Summary","tutor"),placeholder:(0,o.__)("Enter meeting summary","tutor"),rows:3,enableResize:true}))}}),(0,n.Y)("div",{css:U.meetingDateTimeWrapper},(0,n.Y)(c.xI,{name:"meeting_date",control:Q.control,rules:{required:(0,o.__)("Date is required","tutor")},render:function t(e){return(0,n.Y)(f.A,M({},e,{label:(0,o.__)("Meeting Date","tutor"),placeholder:(0,o.__)("Enter meeting date","tutor"),disabledBefore:(new Date).toISOString()}))}}),(0,n.Y)(c.xI,{name:"meeting_time",control:Q.control,rules:{required:(0,o.__)("Time is required","tutor"),validate:E.XA},render:function t(e){return(0,n.Y)(h.A,M({},e,{placeholder:(0,o.__)("Start time","tutor")}))}}),(0,n.Y)("div",{css:U.meetingTimeWrapper},(0,n.Y)(c.xI,{name:"meeting_duration",control:Q.control,rules:{required:(0,o.__)("Duration is required","tutor")},render:function t(e){return(0,n.Y)(p.A,M({},e,{label:(0,o.__)("Meeting Duration","tutor"),placeholder:(0,o.__)("Duration","tutor"),type:"number",selectOnFocus:true}))}}),(0,n.Y)(c.xI,{name:"meeting_duration_unit",control:Q.control,rules:{required:(0,o.__)("Duration unit is required","tutor")},render:function t(e){return(0,n.Y)(x.A,M({},e,{label:(0,n.Y)("span",null," "),options:[{label:(0,o.__)("Minutes","tutor"),value:"min"},{label:(0,o.__)("Hours","tutor"),value:"hr"}]}))}}))),(0,n.Y)(c.xI,{name:"meeting_timezone",control:Q.control,rules:{required:(0,o.__)("Timezone is required","tutor")},render:function t(e){return(0,n.Y)(x.A,M({},e,{label:(0,o.__)("Timezone","tutor"),placeholder:(0,o.__)("Select timezone","tutor"),options:X,selectOnFocus:true,isSearchable:true}))}}),(0,n.Y)(c.xI,{name:"auto_recording",control:Q.control,rules:{required:(0,o.__)("Auto recording is required","tutor")},render:function t(e){return(0,n.Y)(x.A,M({},e,{label:(0,o.__)("Auto Recording","tutor"),placeholder:(0,o.__)("Select auto recording option","tutor"),options:[{label:(0,o.__)("No recordings","tutor"),value:"none"},{label:(0,o.__)("Local","tutor"),value:"local"},{label:(0,o.__)("Cloud","tutor"),value:"cloud"}]}))}}),(0,n.Y)(c.xI,{name:"meeting_password",control:Q.control,rules:{required:(0,o.__)("Password is required","tutor")},render:function t(e){return(0,n.Y)(p.A,M({},e,{label:(0,o.__)("Meeting Password","tutor"),placeholder:(0,o.__)("Enter meeting password","tutor"),type:"password",isPassword:true}))}}),(0,n.Y)(c.xI,{name:"meeting_host",control:Q.control,rules:{required:(0,o.__)("Meeting host is required","tutor")},render:function t(e){return(0,n.Y)(x.A,M({},e,{label:(0,o.__)("Meeting Host","tutor"),placeholder:(0,o.__)("Enter meeting host","tutor"),options:J,disabled:(0,I.O9)(G),selectOnFocus:true,isSearchable:true}))}}))),(0,n.Y)("div",{css:U.buttonWrapper({isScrolling:F})},(0,n.Y)(l.A,{variant:"text",size:"small",onClick:L},(0,o.__)("Cancel","tutor")),(0,n.Y)(l.A,{"data-cy":"save-zoom-meeting",loading:$.isPending,variant:"primary",size:"small",onClick:Q.handleSubmit(tt)},G||B?(0,o.__)("Update Meeting","tutor"):(0,o.__)("Create Meeting","tutor"))))};const z=B;var U={container:(0,n.AH)(S.x.display.flex("column")," background:",m.I6.background.white,";padding-block:",m.YK[12],";border-radius:",m.Vq.card,";box-shadow:",m.r7.popover,";",g.I.caption("regular"),";*>label{font-size:",m.J[15],";color:",m.I6.text.title,";}"+(true?"":0),true?"":0),formWrapper:(0,n.AH)(S.x.display.flex("column"),";",S.x.overflowYAuto,";padding-inline:",m.YK[12],";padding-bottom:",m.YK[8],";gap:",m.YK[12],";height:400px;"+(true?"":0),true?"":0),meetingDateTimeWrapper:(0,n.AH)(S.x.display.flex("column")," gap:",m.YK[6],";"+(true?"":0),true?"":0),meetingTimeWrapper:(0,n.AH)(S.x.display.flex()," justify-content:space-between;align-items:flex-start;gap:",m.YK[6],";"+(true?"":0),true?"":0),buttonWrapper:function t(e){var r=e.isScrolling,o=r===void 0?false:r;return(0,n.AH)(S.x.display.flex()," padding-top:",m.YK[8],";padding-inline:",m.YK[12],";justify-content:flex-end;gap:",m.YK[8],";z-index:",m.fE.positive,";",o&&(0,n.AH)("box-shadow:",m.r7.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},55056:(t,e,r)=>{function n(t){var e=true?r.nc:0;if(e){t.setAttribute("nonce",e)}}t.exports=n},55077:(t,e,r)=>{r.d(e,{$:()=>b});var n=r(34419);var o=r(41594);var a=r.n(o);function i(t){"@babel/helpers - typeof";return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function u(t,e){return f(t)||d(t,e)||c(t,e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function f(t){if(Array.isArray(t))return t}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=m(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){var e=g(t,"string");return"symbol"==i(e)?e:e+""}function g(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var y={defaultValue:false};var b=function t(e){var r=(0,o.useRef)(null);var a=v(v({},y),e);var i=(0,o.useState)(a.defaultValue),s=u(i,2),c=s[0],l=s[1];(0,o.useEffect)((function(){if(!(0,n.O9)(r.current)){return}if(r.current.scrollHeight<=r.current.clientHeight){l(false);return}var t=function t(e){var r=e.target;if(r.scrollTop+r.clientHeight>=r.scrollHeight){l(false);return}l(r.scrollTop>=0)};r.current.addEventListener("scroll",t);return function(){var e;(e=r.current)===null||e===void 0||e.removeEventListener("scroll",t)}}),[r.current]);return{ref:r,isScrolling:c}}},55115:(t,e,r)=>{r.d(e,{f:()=>u});var n=r(74612);var o=r(80653);var a=r(16660);var i=r(52044);function u(t,e){const{disabled:r,excludeDisabled:u,selected:s,required:c,onSelect:l}=t;const[d,f]=(0,n.j)(s,l?s:undefined);const p=!l?d:s;const v=t=>p&&(0,i.R)(p,t,false,e);const h=(n,i,s)=>{const{min:d,max:v}=t;const h=n?(0,o.M)(n,p,d,v,c,e):undefined;if(u&&r&&h?.from&&h.to){if((0,a.P)({from:h.from,to:h.to},r,e)){h.from=n;h.to=undefined}}if(!l){f(h)}l?.(h,n,i,s);return h};return{selected:p,select:h,isSelected:v}}},56066:(t,e,r)=>{r.d(e,{c:()=>s});var n=r(40052);var o=r(82909);var a=r(95387);var i=r(81107);var u=r(9683);const s={code:"en-US",formatDistance:n.B,formatLong:o.s,formatRelative:a.o,localize:i.k,match:u.Y,options:{weekStartsOn:0,firstWeekContainsDate:1}};var c=null&&s},56362:(t,e,r)=>{r.d(e,{s:()=>i});var n=r(41594);var o=r(97766);var a=r(51409);function i(t){const{onPreviousClick:e,onNextClick:r,previousMonth:i,nextMonth:u,...s}=t;const{components:c,classNames:l,labels:{labelPrevious:d,labelNext:f}}=(0,a.w)();const p=(0,n.useCallback)((t=>{if(u){r?.(t)}}),[u,r]);const v=(0,n.useCallback)((t=>{if(i){e?.(t)}}),[i,e]);return n.createElement("nav",{...s},n.createElement(c.PreviousMonthButton,{type:"button",className:l[o.UI.PreviousMonthButton],tabIndex:i?undefined:-1,"aria-disabled":i?undefined:true,"aria-label":d(i),onClick:v},n.createElement(c.Chevron,{disabled:i?undefined:true,className:l[o.UI.Chevron],orientation:"left"})),n.createElement(c.NextMonthButton,{type:"button",className:l[o.UI.NextMonthButton],tabIndex:u?undefined:-1,"aria-disabled":u?undefined:true,"aria-label":f(u),onClick:p},n.createElement(c.Chevron,{disabled:u?undefined:true,orientation:"right",className:l[o.UI.Chevron]})))}},56690:(t,e,r)=>{r.d(e,{N:()=>n});function n(t){return"Week Number"}},57215:(t,e,r)=>{r.d(e,{A:()=>O});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(38919);var u=r(942);var s=r(6502);var c=r(52457);var l=r(62246);var d=r(98880);var f=r(45538);var p=r(15888);var v=r(96755);var h=r(94083);function m(t,e){return _(t)||w(t,e)||y(t,e)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function w(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function _(t){if(Array.isArray(t))return t}function x(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var A={iso:["iso"],dwg:["dwg"],pdf:["pdf"],doc:["doc","docx"],csv:["csv"],xls:["xls","xlsx"],ppt:["ppt","pptx"],zip:["zip"],archive:["rar","7z","tar","gz"],txt:["txt"],rtf:["rtf"],text:["log"],jpg:["jpg"],png:["png"],image:["jpeg","gif","webp","avif"],mp3:["mp3"],fla:["fla"],audio:["ogg","wav","wma"],mp4:["mp4"],avi:["avi"],ai:["ai"],videoFile:["mkv","mpeg","flv","mov","wmv"],svg:["svg"],css:["css"],javascript:["js"],xml:["xml"],html:["html"],exe:["exe"],psd:["psd"],jsonFile:["json"],dbf:["dbf"]};var Y=function t(e){for(var r=0,n=Object.entries(A);r<n.length;r++){var o=m(n[r],2),a=o[0],i=o[1];if(i.includes(e)){return a}}return"file"};var k=function t(e){var r=e.field,a=e.fieldState,c=e.label,l=e.helpText,p=e.buttonText,m=p===void 0?(0,o.__)("Upload Media","tutor"):p,g=e.selectMultiple,y=g===void 0?false:g,b=e.onChange,w=e.maxFileSize,_=e.maxFiles;var x=r.value;var A=(0,v.A)({options:{multiple:y,maxFiles:_,maxFileSize:w},onChange:function t(e){r.onChange(e);if(b){b(e)}},initialFiles:x?Array.isArray(x)?x:[x]:[]}),k=A.openMediaLibrary,O=A.resetFiles;var I=function t(){k()};var E=function t(e){O();if(y){var n=(Array.isArray(x)?x:x?[x]:[]).filter((function(t){return t.id!==e}));r.onChange(n.length>0?n:null);if(b){b(n.length>0?n:null)}}else{r.onChange(null);if(b){b(null)}}};return(0,n.Y)(s.A,{label:c,field:r,fieldState:a,helpText:l},(function(){return(0,n.Y)(f.A,{when:x,fallback:(0,n.Y)(i.A,{buttonCss:S.uploadButton,icon:(0,n.Y)(u.A,{name:"attach",height:24,width:24}),variant:"secondary",onClick:I},m)},(function(t){return(0,n.Y)("div",{css:S.wrapper({hasFiles:Array.isArray(t)?t.length>0:t!==null})},(0,n.Y)("div",{css:S.attachmentsWrapper},(0,n.Y)(d.A,{each:Array.isArray(t)?t:[t]},(function(t){return(0,n.Y)("div",{key:t.id,css:S.attachmentCardWrapper},(0,n.Y)("div",{css:S.attachmentCard},(0,n.Y)(u.A,{style:S.fileIcon,name:Y(t.ext||"file"),height:40,width:40}),(0,n.Y)("div",{css:S.attachmentCardBody},(0,n.Y)("div",{css:S.attachmentCardTitle},(0,n.Y)("div",{title:t.title,css:h.x.text.ellipsis(1)},t.title),(0,n.Y)("div",{css:S.fileExtension},".".concat(t.ext))),(0,n.Y)("div",{css:S.attachmentCardSubtitle},(0,n.Y)("span",null,"".concat((0,o.__)("Size","tutor"),": ").concat(t.size))))),(0,n.Y)("button",{type:"button",css:S.removeButton,onClick:function e(){E(t.id)}},(0,n.Y)(u.A,{name:"cross",height:24,width:24})))}))),(0,n.Y)("div",{css:S.uploadButtonWrapper({hasFiles:Array.isArray(t)?t.length>0:t!==null})},(0,n.Y)(i.A,{buttonCss:S.uploadButton,icon:(0,n.Y)(u.A,{name:"attach",height:24,width:24}),variant:"secondary",onClick:I,"data-cy":"upload-media"},m)))}))}))};const O=(0,p.M)(k);var S={wrapper:function t(e){var r=e.hasFiles;return(0,n.AH)("display:flex;flex-direction:column;position:relative;",r&&(0,n.AH)("background-color:",c.I6.background.white,";padding:",c.YK[16]," 0 ",c.YK[16]," ",c.YK[16],";border:1px solid ",c.I6.stroke["default"],";border-radius:",c.Vq.card,";gap:",c.YK[8],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},attachmentsWrapper:(0,n.AH)("max-height:260px;padding-right:",c.YK[16],";",h.x.overflowYAuto,";"+(true?"":0),true?"":0),attachmentCardWrapper:(0,n.AH)(h.x.display.flex(),";justify-content:space-between;align-items:center;gap:",c.YK[20],";padding:",c.YK[4]," ",c.YK[12]," ",c.YK[4]," 0;border-radius:",c.Vq[6],";button{opacity:0;}&:hover,&:focus-within{background:",c.I6.background.hover,";button{opacity:1;}}",c.EA.smallTablet,"{button{opacity:1;}}"+(true?"":0),true?"":0),attachmentCard:(0,n.AH)(h.x.display.flex(),";align-items:center;gap:",c.YK[8],";overflow:hidden;"+(true?"":0),true?"":0),attachmentCardBody:(0,n.AH)(h.x.display.flex("column"),";gap:",c.YK[4],";"+(true?"":0),true?"":0),attachmentCardTitle:(0,n.AH)(h.x.display.flex(),";",l.I.caption("medium")," word-break:break-all;"+(true?"":0),true?"":0),fileExtension:true?{name:"ozd7xs",styles:"flex-shrink:0"}:0,attachmentCardSubtitle:(0,n.AH)(l.I.tiny("regular")," ",h.x.display.flex(),";align-items:center;gap:",c.YK[8],";color:",c.I6.text.hints,";svg{color:",c.I6.icon["default"],";}"+(true?"":0),true?"":0),uploadButtonWrapper:function t(e){var r=e.hasFiles;return(0,n.AH)(r&&(0,n.AH)("margin-right:",c.YK[16],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},uploadButton:true?{name:"1d3w5wq",styles:"width:100%"}:0,fileIcon:(0,n.AH)("flex-shrink:0;color:",c.I6.icon["default"],";"+(true?"":0),true?"":0),removeButton:(0,n.AH)(h.x.crossButton,";background:none;transition:none;flex-shrink:0;"+(true?"":0),true?"":0)}},57836:(t,e,r)=>{r.d(e,{w:()=>o});var n=r(41594);function o(t){return n.createElement("div",{...t})}},57899:(t,e,r)=>{r.d(e,{g:()=>n});function n(t,e,r,n){if(!t)return undefined;if(!e)return undefined;const{startOfYear:o,endOfYear:a,addYears:i,getYear:u,isBefore:s,isSameYear:c}=n;const l=o(t);const d=a(e);const f=[];let p=l;while(s(p,d)||c(p,d)){f.push(p);p=i(p,1)}return f.map((t=>{const e=r.formatYearDropdown(t,n);return{value:u(t),label:e,disabled:false}}))}},57933:(t,e,r)=>{r.d(e,{f:()=>o});var n=r(41594);function o(t){const{calendarMonth:e,displayIndex:r,...o}=t;return n.createElement("div",{...o},t.children)}},58071:(t,e,r)=>{r.d(e,{_:()=>v});var n=r(41594);var o=r(18711);var a=r(9727);var i=r(14151);var u=r(31094);var s=r(72631);var c=r(21289);var l=r(80807);var d=r(93987);var f=r(24595);var p=r(74612);function v(t,e){const[r,v]=(0,c.J)(t,e);const{startOfMonth:h,endOfMonth:m}=e;const g=(0,u.Z)(t,e);const[y,b]=(0,p.j)(g,t.month?g:undefined);(0,n.useEffect)((()=>{const r=(0,u.Z)(t,e);b(r)}),[t.timeZone]);const w=(0,i.o)(y,v,t,e);const _=(0,o.Y)(w,t.endMonth?m(t.endMonth):undefined,t,e);const x=(0,s.S)(w,_,t,e);const A=(0,f.C)(x);const Y=(0,a._)(x);const k=(0,d.E)(y,r,t,e);const O=(0,l.Q)(y,v,t,e);const{disableNavigation:S,onMonthChange:I}=t;const E=t=>A.some((e=>e.days.some((e=>e.isEqualTo(t)))));const C=t=>{if(S){return}let e=h(t);if(r&&e<h(r)){e=h(r)}if(v&&e>h(v)){e=h(v)}b(e);I?.(e)};const M=t=>{if(E(t)){return}C(t.date)};const T={months:x,weeks:A,days:Y,navStart:r,navEnd:v,previousMonth:k,nextMonth:O,goToMonth:C,goToDay:M};return T}},58219:(t,e,r)=>{r.d(e,{b:()=>o});var n=r(41594);function o(t){const{rootRef:e,...r}=t;return n.createElement("div",{...r,ref:e})}},59830:(t,e,r)=>{r.d(e,{Z:()=>a,n:()=>o});var n=r(32850);function o(t,e,r,o){let a=(o??new n.i0(r)).format(t,"PPPP");if(e.today)a=`Today, ${a}`;if(e.selected)a=`${a}, selected`;return a}const a=o},61025:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/56f20c93d8e28423f724fe4e914fbd21-3d.png"},61578:(t,e,r)=>{var n=r(44880);const o=t=>e=>TZDate.tz(t,+new Date(e))},61757:(t,e,r)=>{r.d(e,{U:()=>n});function n(){return``}},61797:(t,e,r)=>{r.d(e,{P:()=>o});var n=r(32850);class o{constructor(t,e,r=n.VA){this.date=t;this.displayMonth=e;this.outside=Boolean(e&&!r.isSameMonth(t,e));this.dateLib=r}isEqualTo(t){return this.dateLib.isSameDay(t.date,this.date)&&this.dateLib.isSameMonth(t.displayMonth,this.displayMonth)}}},61978:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/fb8df26f9102747dfafc31d912d6d074-retro.png"},62352:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/9c13bda85170ee68f15380378d920fd1-generate-image.webp"},63612:(t,e,r)=>{r.d(e,{L:()=>n});function n(t,e,r,n,o){const{startOfMonth:a,startOfYear:i,endOfYear:u,eachMonthOfInterval:s,getMonth:c}=o;const l=s({start:i(t),end:u(t)});const d=l.map((t=>{const i=n.formatMonthDropdown(t,o);const u=c(t);const s=e&&t<a(e)||r&&t>a(r)||false;return{value:u,label:i,disabled:s}}));return d}},64585:(t,e,r)=>{r.d(e,{N:()=>u});var n=r(31308);var o=r(73524);var a=r(7629);var i=r(49164);function u(t,e){const r=(0,i.a)(t,e?.in);const u=+(0,o.k)(r,e)-+(0,a.b)(r,e);return Math.round(u/n.my)+1}var s=null&&u},65636:(t,e,r)=>{r.d(e,{g:()=>o});var n=r(1741);function o(t,e){return(0,n.$)(t,{...e,weekStartsOn:1})}var a=null&&o},66213:(t,e,r)=>{r.d(e,{B:()=>o});var n=r(41594);function o(t){return n.createElement("th",{...t})}},66504:(t,e,r)=>{r.d(e,{F:()=>n});function n(t,e){const r=t<0?"-":"";const n=Math.abs(t).toString().padStart(e,"0");return r+n}},66564:(t,e,r)=>{r.d(e,{J:()=>o});var n=r(97766);function o(t,e={},r={}){let o={...e?.[n.UI.Day]};Object.entries(t).filter((([,t])=>t===true)).forEach((([t])=>{o={...o,...r?.[t]}}));return o}},66694:(t,e,r)=>{r.d(e,{A:()=>d});var n=r(52457);var o=r(62246);var a=r(47849);var i=r(17437);var u=r(41594);var s=r.n(u);var c=s().forwardRef((function(t,e){var r=t.name,n=t.checked,o=t.readOnly,u=t.disabled,s=u===void 0?false:u,c=t.labelCss,d=t.label,f=t.icon,p=t.value,v=t.onChange,h=t.onBlur;var m=(0,a.Ak)();return(0,i.Y)("label",{htmlFor:m,css:[l.container(s),c,true?"":0,true?"":0]},(0,i.Y)("input",{ref:e,id:m,name:r,type:"radio",checked:n,readOnly:o,value:p,disabled:s,onChange:v,onBlur:h,css:[l.radio(d),true?"":0,true?"":0]}),(0,i.Y)("span",null),f,d)}));var l={container:function t(e){return(0,i.AH)(o.I.caption(),";display:flex;align-items:center;cursor:pointer;user-select:none;",e&&(0,i.AH)("color:",n.I6.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},radio:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";return(0,i.AH)("position:absolute;opacity:0;height:0;width:0;cursor:pointer;&+span{position:relative;cursor:pointer;height:18px;width:18px;background-color:",n.I6.background.white,";border:2px solid ",n.I6.stroke["default"],";border-radius:100%;",e&&(0,i.AH)("margin-right:",n.YK[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';position:absolute;left:3px;top:3px;background-color:",n.I6.background.white,";width:8px;height:8px;border-radius:100%;}&:checked+span{border-color:",n.I6.action.primary["default"],";}&:checked+span::before{background-color:",n.I6.action.primary["default"],";}&:focus-visible{&+span{outline:2px solid ",n.I6.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const d=c},67375:(t,e,r)=>{r.d(e,{default:()=>i});var n=r(94188);var o=r(10123);var a=r(70551);function i(t,e){(0,a.A)(2,arguments);var r=(0,o["default"])(t);var i=(0,n.A)(e);r.setMinutes(i);return r}},67403:(t,e,r)=>{r.d(e,{m:()=>i});const n=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}};const o=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}};const a=(t,e)=>{const r=t.match(/(P+)(p+)?/)||[];const a=r[1];const i=r[2];if(!i){return n(t,e)}let u;switch(a){case"P":u=e.dateTime({width:"short"});break;case"PP":u=e.dateTime({width:"medium"});break;case"PPP":u=e.dateTime({width:"long"});break;case"PPPP":default:u=e.dateTime({width:"full"});break}return u.replace("{{date}}",n(a,e)).replace("{{time}}",o(i,e))};const i={p:o,P:a}},67901:(t,e,r)=>{r.d(e,{default:()=>i});var n=r(94188);var o=r(10123);var a=r(70551);function i(t,e){(0,a.A)(2,arguments);var r=(0,o["default"])(t);var i=(0,n.A)(e);r.setHours(i);return r}},67918:(t,e,r)=>{r.d(e,{b:()=>o,t:()=>a});var n=r(32850);function o(t,e,r){return(r??new n.i0(e)).format(t,"LLLL y")}const a=o},68587:(t,e,r)=>{r.d(e,{s:()=>f});var n=r(41594);var o=r(97766);const a=t=>{if(t instanceof HTMLElement)return t;return null};const i=t=>[...t.querySelectorAll("[data-animated-month]")??[]];const u=t=>a(t.querySelector("[data-animated-month]"));const s=t=>a(t.querySelector("[data-animated-caption]"));const c=t=>a(t.querySelector("[data-animated-weeks]"));const l=t=>a(t.querySelector("[data-animated-nav]"));const d=t=>a(t.querySelector("[data-animated-weekdays]"));function f(t,e,{classNames:r,months:a,focused:f,dateLib:p}){const v=(0,n.useRef)(null);const h=(0,n.useRef)(a);const m=(0,n.useRef)(false);(0,n.useLayoutEffect)((()=>{const n=h.current;h.current=a;if(!e||!t.current||!(t.current instanceof HTMLElement)||a.length===0||n.length===0||a.length!==n.length){return}const g=p.isSameMonth(a[0].date,n[0].date);const y=p.isAfter(a[0].date,n[0].date);const b=y?r[o.X5.caption_after_enter]:r[o.X5.caption_before_enter];const w=y?r[o.X5.weeks_after_enter]:r[o.X5.weeks_before_enter];const _=v.current;const x=t.current.cloneNode(true);if(x instanceof HTMLElement){const t=i(x);t.forEach((t=>{if(!(t instanceof HTMLElement))return;const e=u(t);if(e&&t.contains(e)){t.removeChild(e)}const r=s(t);if(r){r.classList.remove(b)}const n=c(t);if(n){n.classList.remove(w)}}));v.current=x}else{v.current=null}if(m.current||g||f){return}const A=_ instanceof HTMLElement?i(_):[];const Y=i(t.current);if(Y&&Y.every((t=>t instanceof HTMLElement))&&A&&A.every((t=>t instanceof HTMLElement))){m.current=true;const e=[];t.current.style.isolation="isolate";const n=l(t.current);if(n){n.style.zIndex="1"}Y.forEach(((a,i)=>{const u=A[i];if(!u){return}a.style.position="relative";a.style.overflow="hidden";const l=s(a);if(l){l.classList.add(b)}const f=c(a);if(f){f.classList.add(w)}const p=()=>{m.current=false;if(t.current){t.current.style.isolation=""}if(n){n.style.zIndex=""}if(l){l.classList.remove(b)}if(f){f.classList.remove(w)}a.style.position="";a.style.overflow="";if(a.contains(u)){a.removeChild(u)}};e.push(p);u.style.pointerEvents="none";u.style.position="absolute";u.style.overflow="hidden";u.setAttribute("aria-hidden","true");const v=d(u);if(v){v.style.opacity="0"}const h=s(u);if(h){h.classList.add(y?r[o.X5.caption_before_exit]:r[o.X5.caption_after_exit]);h.addEventListener("animationend",p)}const g=c(u);if(g){g.classList.add(y?r[o.X5.weeks_before_exit]:r[o.X5.weeks_after_exit])}a.insertBefore(u,a.firstChild)}))}}))}},69030:(t,e,r)=>{r.d(e,{l:()=>n});function n(t,e,r,n,o,a,i){const{ISOWeek:u,broadcastCalendar:s}=a;const{addDays:c,addMonths:l,addWeeks:d,addYears:f,endOfBroadcastWeek:p,endOfISOWeek:v,endOfWeek:h,max:m,min:g,startOfBroadcastWeek:y,startOfISOWeek:b,startOfWeek:w}=i;const _={day:c,week:d,month:l,year:f,startOfWeek:t=>s?y(t,i):u?b(t):w(t),endOfWeek:t=>s?p(t,i):u?v(t):h(t)};let x=_[t](r,e==="after"?1:-1);if(e==="before"&&n){x=m([n,x])}else if(e==="after"&&o){x=g([o,x])}return x}},69545:(t,e,r)=>{r.d(e,{y:()=>n});function n(){return""}},69564:(t,e,r)=>{r.d(e,{K:()=>n});function n(t){return(e,r={})=>{const n=e.match(t.matchPattern);if(!n)return null;const o=n[0];const a=e.match(t.parsePattern);if(!a)return null;let i=t.valueCallback?t.valueCallback(a[0]):a[0];i=r.valueCallback?r.valueCallback(i):i;const u=e.slice(o.length);return{value:i,rest:u}}}},69618:(t,e,r)=>{r.d(e,{m:()=>a});var n=r(41594);var o=r(97766);function a(t){const{options:e,className:r,components:a,classNames:i,...u}=t;const s=[i[o.UI.Dropdown],r].join(" ");const c=e?.find((({value:t})=>t===u.value));return n.createElement("span",{"data-disabled":u.disabled,className:i[o.UI.DropdownRoot]},n.createElement(a.Select,{className:s,...u},e?.map((({value:t,label:e,disabled:r})=>n.createElement(a.Option,{key:t,value:t,disabled:r},e)))),n.createElement("span",{className:i[o.UI.CaptionLabel],"aria-hidden":true},c?.label,n.createElement(a.Chevron,{orientation:"down",size:18,className:i[o.UI.Chevron]})))}},70349:(t,e,r)=>{r.d(e,{A:()=>G});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(3771);var u=r.n(i);var s=r(41594);var c=r.n(s);var l=r(1261);var d=r(38919);var f=r(4704);var p=r(942);var v=r(1438);var h=r(87177);var m=r(31414);var g=r(68654);var y=r(50707);var b=r(78621);var w=r(94242);var _=r(48465);var x=r(41502);var A=r(52457);var Y=r(98880);var k=r(45538);var O=r(94083);var S=r(31309);var I=r(43228);var E=r(6502);function C(t){"@babel/helpers - typeof";return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}var M;function T(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */T=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new M(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(j([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==C(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(C(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,M.prototype={constructor:M,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:j(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function j(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function D(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){j(a,n,o,i,u,"next",t)}function u(t){j(a,n,o,i,u,"throw",t)}i(void 0)}))}}function L(t,e){return K(t)||W(t,e)||N(t,e)||P()}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(t,e){if(t){if("string"==typeof t)return H(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?H(t,e):void 0}}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function W(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function K(t){if(Array.isArray(t))return t}function B(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var z={droip:"droipColorized",elementor:"elementorColorized",gutenberg:"gutenbergColorized",divi:"diviColorized"};var U=!!_.P.tutor_pro_url;var R=(M=_.P.settings)===null||M===void 0?void 0:M.chatgpt_key_exist;var F=function t(e){var r=e.editorUsed,a=e.onBackToWPEditorClick,i=e.onCustomEditorButtonClick;var u=(0,y.h)(),c=u.showModal;var f=(0,s.useState)(""),v=L(f,2),h=v[0],m=v[1];return(0,n.Y)("div",{css:V.editorOverlay},(0,n.Y)(k.A,{when:r.name!=="gutenberg"},(0,n.Y)(d.A,{variant:"tertiary",size:"small",buttonCss:V.editWithButton,icon:(0,n.Y)(p.A,{name:"arrowLeft",height:24,width:24}),loading:h==="back_to",onClick:D(T().mark((function t(){var e,i;return T().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:u.next=2;return c({component:g.A,props:{title:(0,o.__)("Back to WordPress Editor","tutor"),description:(0,n.Y)(l.A,{type:"warning",icon:"warning"},(0,o.__)("Warning: Switching to the WordPress default editor may cause issues with your current layout, design, and content.","tutor")),confirmButtonText:(0,o.__)("Confirm","tutor"),confirmButtonVariant:"primary"},depthIndex:A.fE.highest});case 2:e=u.sent;i=e.action;if(!(i==="CONFIRM")){u.next=12;break}u.prev=5;m("back_to");u.next=9;return a===null||a===void 0?void 0:a(r.name);case 9:u.prev=9;m("");return u.finish(9);case 12:case"end":return u.stop()}}),t,null,[[5,,9,12]])})))},(0,o.__)("Back to WordPress Editor","tutor"))),(0,n.Y)(d.A,{variant:"tertiary",size:"small",buttonCss:V.editWithButton,loading:h==="edit_with",icon:z[r.name]&&(0,n.Y)(p.A,{name:z[r.name],height:24,width:24}),onClick:D(T().mark((function t(){return T().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.prev=0;m("edit_with");e.next=4;return i===null||i===void 0?void 0:i(r);case 4:window.location.href=r.link;case 5:e.prev=5;m("");return e.finish(5);case 8:case"end":return e.stop()}}),t,null,[[0,,5,8]])})))},(0,o.sprintf)((0,o.__)("Edit with %s","tutor"),r===null||r===void 0?void 0:r.label)))};var q=function t(e){var r,a,i;var u=e.label,c=e.field,l=e.fieldState,d=e.disabled,g=e.readOnly,C=e.loading,M=e.placeholder,j=e.helpText,P=e.onChange,N=e.generateWithAi,H=N===void 0?false:N,W=e.onClickAiButton,K=e.hasCustomEditorSupport,B=K===void 0?false:K,q=e.isMinimal,G=q===void 0?false:q,Q=e.hideMediaButtons,$=Q===void 0?false:Q,Z=e.hideQuickTags,X=Z===void 0?false:Z,J=e.editors,tt=J===void 0?[]:J,et=e.editorUsed,rt=et===void 0?{name:"classic",label:"Classic Editor",link:""}:et,nt=e.isMagicAi,ot=nt===void 0?false:nt,at=e.autoFocus,it=at===void 0?false:at,ut=e.onCustomEditorButtonClick,st=e.onBackToWPEditorClick,ct=e.onFullScreenChange,lt=e.min_height,dt=e.max_height,ft=e.toolbar1,pt=e.toolbar2;var vt=(0,y.h)(),ht=vt.showModal;var mt=((r=_.P.settings)===null||r===void 0?void 0:r.hide_admin_bar_for_users)==="off";var gt=(a=_.P.current_user)===null||a===void 0||(a=a.roles)===null||a===void 0?void 0:a.includes(x.gt.ADMINISTRATOR);var yt=(i=_.P.current_user)===null||i===void 0||(i=i.roles)===null||i===void 0?void 0:i.includes(x.gt.TUTOR_INSTRUCTOR);var bt=(0,s.useState)(null),wt=L(bt,2),_t=wt[0],xt=wt[1];var At=tt.filter((function(t){return gt||yt&&mt||t.name==="droip"}));var Yt=B&&At.length>0;var kt=Yt&&rt.name!=="classic";var Ot=function t(){if(!U){ht({component:b.A,props:{image:I.A,image2x:S.A}})}else if(!R){ht({component:w.A,props:{image:I.A,image2x:S.A}})}else{ht({component:m.A,isMagicAi:true,props:{title:(0,o.__)("AI Studio","tutor"),icon:(0,n.Y)(p.A,{name:"magicAiColorize",width:24,height:24}),characters:1e3,field:c,fieldState:l,is_html:true}});W===null||W===void 0||W()}};var St=(0,n.Y)("div",{css:V.editorLabel},(0,n.Y)("span",{css:V.labelWithAi},u,(0,n.Y)(k.A,{when:H},(0,n.Y)("button",{type:"button",css:V.aiButton,onClick:Ot},(0,n.Y)(p.A,{name:"magicAiColorize",width:32,height:32})))),(0,n.Y)("div",{css:V.editorsButtonWrapper},(0,n.Y)("span",null,(0,o.__)("Edit with","tutor")),(0,n.Y)("div",{css:V.customEditorButtons},(0,n.Y)(Y.A,{each:At},(function(t){return(0,n.Y)(v.A,{key:t.name,content:t.label,delay:200},(0,n.Y)("button",{type:"button",css:V.customEditorButton,disabled:_t===t.name,onClick:D(T().mark((function e(){return T().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;xt(t.name);r.next=4;return ut===null||ut===void 0?void 0:ut(t);case 4:window.location.href=t.link;case 5:r.prev=5;xt(null);return r.finish(5);case 8:case"end":return r.stop()}}),e,null,[[0,,5,8]])})))},(0,n.Y)(k.A,{when:_t===t.name},(0,n.Y)(f.p8,null)),(0,n.Y)(p.A,{name:z[t.name],height:24,width:24})))})))));return(0,n.Y)(E.A,{label:Yt?St:u,field:c,fieldState:l,disabled:d,readOnly:g,placeholder:M,helpText:j,isMagicAi:ot,generateWithAi:!Yt&&H,onClickAiButton:Ot,replaceEntireLabel:Yt},(function(){var t;if(C){return(0,n.Y)("div",{css:O.x.flexCenter()},(0,n.Y)(f.Ay,{size:20,color:A.I6.icon["default"]}))}return(0,n.Y)("div",{css:V.wrapper({isOverlayVisible:kt})},(0,n.Y)(k.A,{when:kt},(0,n.Y)(F,{editorUsed:rt,onBackToWPEditorClick:st,onCustomEditorButtonClick:ut})),(0,n.Y)(h.A,{value:(t=c.value)!==null&&t!==void 0?t:"",onChange:function t(e){c.onChange(e);if(P){P(e)}},isMinimal:G,hideMediaButtons:$,hideQuickTags:X,autoFocus:it,onFullScreenChange:ct,readonly:g,min_height:lt,max_height:dt,toolbar1:ft,toolbar2:pt}))}))};const G=q;var V={wrapper:function t(e){var r=e.isOverlayVisible,o=r===void 0?false:r;return(0,n.AH)("position:relative;",o&&(0,n.AH)("overflow:hidden;border-radius:",A.Vq[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},editorLabel:true?{name:"1u6jws0",styles:"display:flex;width:100%;align-items:center;justify-content:space-between"}:0,aiButton:(0,n.AH)(O.x.resetButton,";",O.x.flexCenter(),";width:32px;height:32px;border-radius:",A.Vq[4],";:disabled{cursor:not-allowed;}&:focus-visible{outline:2px solid ",A.I6.stroke.brand,";}"+(true?"":0),true?"":0),labelWithAi:(0,n.AH)("display:flex;align-items:center;gap:",A.YK[4],";"+(true?"":0),true?"":0),editorsButtonWrapper:(0,n.AH)("display:flex;align-items:center;gap:",A.YK[8],";color:",A.I6.text.hints,";"+(true?"":0),true?"":0),customEditorButtons:(0,n.AH)("display:flex;align-items:center;gap:",A.YK[4],";"+(true?"":0),true?"":0),customEditorButton:(0,n.AH)(O.x.resetButton," display:flex;align-items:center;justify-content:center;position:relative;border-radius:",A.Vq.circle,";&:focus-visible{outline:2px solid ",A.I6.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0),editorOverlay:(0,n.AH)("position:absolute;height:100%;width:100%;",O.x.flexCenter(),";gap:",A.YK[8],";background-color:",u()(A.I6.background.modal,.6),";border-radius:",A.Vq[6],";z-index:",A.fE.positive,";backdrop-filter:blur(8px);"+(true?"":0),true?"":0),editWithButton:(0,n.AH)("background:",A.I6.action.secondary["default"],";color:",A.I6.text.primary,";box-shadow:inset 0 -1px 0 0 ",u()("#1112133D",.24),",0 1px 0 0 ",u()("#1112133D",.8),";"+(true?"":0),true?"":0)}},70684:(t,e,r)=>{r.d(e,{Hg:()=>s,OE:()=>u,RE:()=>a,Ue:()=>i,m4:()=>n,oM:()=>o});function n(t){return Boolean(t&&typeof t==="object"&&"before"in t&&"after"in t)}function o(t){return Boolean(t&&typeof t==="object"&&"from"in t)}function a(t){return Boolean(t&&typeof t==="object"&&"after"in t)}function i(t){return Boolean(t&&typeof t==="object"&&"before"in t)}function u(t){return Boolean(t&&typeof t==="object"&&"dayOfWeek"in t)}function s(t,e){return Array.isArray(t)&&t.every(e.isDate)}},70956:(t,e,r)=>{r.d(e,{A:()=>p});var n=r(17437);var o=r(47767);var a=r(38919);var i=r(942);var u=r(41502);var s=r(52457);var c=r(62246);var l=r(45538);function d(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var f=function t(e){var r=e.title,s=e.backUrl,c=e.rightButton,d=e.isExternalUrl;var f=(0,o.Zp)();var p=function t(){if(s){if(d){window.location.href=s;return}f(s)}else{f(-1)}};return(0,n.Y)("div",{css:h.wrapper},(0,n.Y)("div",{css:h.left},(0,n.Y)(l.A,{when:s},(0,n.Y)(a.A,{variant:"text",buttonCss:h.button({isRTL:u.V8}),onClick:p},(0,n.Y)(i.A,{name:"back",width:32,height:32}))),(0,n.Y)("h6",{css:h.title},r)),c)};const p=f;var v=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var h={wrapper:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,left:(0,n.AH)("display:flex;align-items:center;gap:",s.YK[16],";"+(true?"":0),true?"":0),button:function t(e){var r=e.isRTL;return(0,n.AH)("padding:0;border-radius:",s.Vq[2],";",r&&v,";"+(true?"":0),true?"":0)},title:(0,n.AH)(c.I.heading6("medium"),";"+(true?"":0),true?"":0)}},71271:(t,e,r)=>{r.d(e,{d:()=>o});var n=r(49164);function o(t,e){return+(0,n.a)(t)>+(0,n.a)(e)}var a=null&&o},71941:(t,e,r)=>{r.d(e,{D:()=>o});var n=r(41594);function o(t){return n.createElement("table",{...t})}},71951:(t,e,r)=>{r.d(e,{C:()=>o});var n=r(66504);const o={y(t,e){const r=t.getFullYear();const o=r>0?r:1-r;return(0,n.F)(e==="yy"?o%100:o,e.length)},M(t,e){const r=t.getMonth();return e==="M"?String(r+1):(0,n.F)(r+1,2)},d(t,e){return(0,n.F)(t.getDate(),e.length)},a(t,e){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h(t,e){return(0,n.F)(t.getHours()%12||12,e.length)},H(t,e){return(0,n.F)(t.getHours(),e.length)},m(t,e){return(0,n.F)(t.getMinutes(),e.length)},s(t,e){return(0,n.F)(t.getSeconds(),e.length)},S(t,e){const r=e.length;const o=t.getMilliseconds();const a=Math.trunc(o*Math.pow(10,r-3));return(0,n.F)(a,e.length)}}},72574:(t,e,r)=>{r.d(e,{A:()=>c});var n=r(52457);var o=r(17437);var a=r(62246);var i=r(94083);var u=r(6502);var s=function t(e){var r=e.field,n=e.fieldState,a=e.label,i=e.options,s=i===void 0?[]:i,c=e.disabled;return(0,o.Y)(u.A,{field:r,fieldState:n,label:a,disabled:c},(function(){return(0,o.Y)("div",{css:l.wrapper},s.map((function(t,e){return(0,o.Y)("button",{type:"button",key:e,css:l.item(r.value===t.value),onClick:function e(){r.onChange(t.value)},disabled:c},(0,o.Y)("img",{src:t.image,alt:t.label,width:64,height:64}),(0,o.Y)("p",null,t.label))})))}))};const c=s;var l={wrapper:(0,o.AH)("display:grid;grid-template-columns:repeat(4, minmax(64px, 1fr));gap:",n.YK[12],";margin-top:",n.YK[4],";"+(true?"":0),true?"":0),item:function t(e){return(0,o.AH)(i.x.resetButton,";display:flex;flex-direction:column;gap:",n.YK[4],";align-items:center;width:100%;cursor:pointer;input{appearance:none;}p{",a.I.small(),";width:100%;",i.x.textEllipsis,";color:",n.I6.text.subdued,";text-align:center;}&:hover,&:focus-visible{",!e&&(0,o.AH)("img{border-color:",n.I6.stroke.hover,";}"+(true?"":0),true?"":0),";}img{border-radius:",n.Vq[6],";border:2px solid ",n.I6.stroke.border,";outline:2px solid transparent;outline-offset:2px;transition:border-color 0.3s ease;",e&&(0,o.AH)("outline-color:",n.I6.stroke.magicAi,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)}}},72631:(t,e,r)=>{r.d(e,{S:()=>i});var n=r(61797);var o=r(46483);var a=r(48665);function i(t,e,r,i){const{addDays:u,endOfBroadcastWeek:s,endOfISOWeek:c,endOfMonth:l,endOfWeek:d,getISOWeek:f,getWeek:p,startOfBroadcastWeek:v,startOfISOWeek:h,startOfWeek:m}=i;const g=t.reduce(((t,g)=>{const y=r.broadcastCalendar?v(g,i):r.ISOWeek?h(g):m(g);const b=r.broadcastCalendar?s(g,i):r.ISOWeek?c(l(g)):d(l(g));const w=e.filter((t=>t>=y&&t<=b));const _=r.broadcastCalendar?35:42;if(r.fixedWeeks&&w.length<_){const t=e.filter((t=>{const e=_-w.length;return t>b&&t<=u(b,e)}));w.push(...t)}const x=w.reduce(((t,e)=>{const a=r.ISOWeek?f(e):p(e);const u=t.find((t=>t.weekNumber===a));const s=new n.P(e,g,i);if(!u){t.push(new o.j(a,[s]))}else{u.days.push(s)}return t}),[]);const A=new a.j(g,x);t.push(A);return t}),[]);if(!r.reverseMonths){return g}else{return g.reverse()}}},72818:(t,e,r)=>{r.d(e,{u:()=>o});var n=r(41594);function o(t){const{week:e,...r}=t;return n.createElement("th",{...r})}},72881:(t,e,r)=>{r.d(e,{D:()=>a,e:()=>o});var n=r(32850);function o(t,e=n.VA){return e.format(t,"yyyy")}const a=o},73524:(t,e,r)=>{r.d(e,{k:()=>a});var n=r(89441);var o=r(49164);function a(t,e){const r=(0,n.q)();const a=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0;const i=(0,o.a)(t,e?.in);const u=i.getDay();const s=(u<a?7:0)+u-a;i.setDate(i.getDate()-s);i.setHours(0,0,0,0);return i}var i=null&&a},73531:(t,e,r)=>{r.d(e,{t:()=>o});var n=r(41594);function o(t){return n.createElement("th",{...t})}},73958:(t,e,r)=>{r.d(e,{l:()=>n});function n(t,e){const r=e.startOfMonth(t);const n=r.getDay();if(n===1){return r}else if(n===0){return e.addDays(r,-1*6)}else{return e.addDays(r,-1*(n-1))}}},74084:(t,e,r)=>{r.d(e,{x:()=>o});var n=r(27256);function o(t,...e){const r=n.w.bind(null,t||e.find((t=>typeof t==="object")));return e.map(r)}},74612:(t,e,r)=>{r.d(e,{j:()=>o});var n=r(41594);function o(t,e){const[r,o]=(0,n.useState)(t);const a=e===undefined?r:e;return[a,o]}},74880:(t,e,r)=>{r.d(e,{w:()=>o});var n=r(49164);function o(t,e){const r=(0,n.a)(t,e?.in);r.setDate(1);r.setHours(0,0,0,0);return r}var a=null&&o},75583:(t,e,r)=>{r.d(e,{BB:()=>o.B});var n=r(38945);var o=r(44880);var a=r(18525);var i=r(61578);var u=r(90865);var s=r(22471)},76260:(t,e,r)=>{r.d(e,{A:()=>n});function n(t){return(e,r={})=>{const n=r.width;const i=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth];const u=e.match(i);if(!u){return null}const s=u[0];const c=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth];const l=Array.isArray(c)?a(c,(t=>t.test(s))):o(c,(t=>t.test(s)));let d;d=t.valueCallback?t.valueCallback(l):l;d=r.valueCallback?r.valueCallback(d):d;const f=e.slice(s.length);return{value:d,rest:f}}}function o(t,e){for(const r in t){if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r])){return r}}return undefined}function a(t,e){for(let r=0;r<t.length;r++){if(e(t[r])){return r}}return undefined}},76314:t=>{t.exports=function(t){var e=[];e.toString=function e(){return this.map((function(e){var r=t(e);if(e[2]){return"@media ".concat(e[2]," {").concat(r,"}")}return r})).join("")};e.i=function(t,r,n){if(typeof t==="string"){t=[[null,t,""]]}var o={};if(n){for(var a=0;a<this.length;a++){var i=this[a][0];if(i!=null){o[i]=true}}}for(var u=0;u<t.length;u++){var s=[].concat(t[u]);if(n&&o[s[0]]){continue}if(r){if(!s[2]){s[2]=r}else{s[2]="".concat(r," and ").concat(s[2])}}e.push(s)}};return e}},77307:(t,e,r)=>{r.d(e,{$:()=>o});var n=r(41594);function o(t){return n.createElement("span",{...t})}},77506:(t,e,r)=>{r.d(e,{w:()=>c});var n=r(52457);var o=r(97404);var a=r(17437);var i=r(41594);var u=r.n(i);function s(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var c=u().forwardRef((function(t,e){var r=t.className,n=t.variant;return(0,a.Y)("div",{className:r,ref:e,css:d({variant:n})})}));c.displayName="Separator";var l={horizontal:true?{name:"d6rgw1",styles:"height:1px;width:100%"}:0,vertical:true?{name:"cw4fps",styles:"height:100%;width:1px"}:0,base:(0,a.AH)("flex-shrink:0;background-color:",n.I6.stroke.divider,";"+(true?"":0),true?"":0)};var d=(0,o.s)({variants:{variant:{horizontal:l.horizontal,vertical:l.vertical}},defaultVariants:{variant:"horizontal"}},l.base)},77659:t=>{var e={};function r(t){if(typeof e[t]==="undefined"){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement){try{r=r.contentDocument.head}catch(t){r=null}}e[t]=r}return e[t]}function n(t,e){var n=r(t);if(!n){throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.")}n.appendChild(e)}t.exports=n},77846:(t,e,r)=>{r.d(e,{o:()=>n});function n(t){return(e,r)=>{const n=r?.context?String(r.context):"standalone";let o;if(n==="formatting"&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth;const n=r?.width?String(r.width):e;o=t.formattingValues[n]||t.formattingValues[e]}else{const e=t.defaultWidth;const n=r?.width?String(r.width):t.defaultWidth;o=t.values[n]||t.values[e]}const a=t.argumentCallback?t.argumentCallback(e):e;return o[a]}}},78127:(t,e,r)=>{r.d(e,{T:()=>a});var n=r(27256);var o=r(49164);function a(t,e){let r;let a=e?.in;t.forEach((t=>{if(!a&&typeof t==="object")a=n.w.bind(null,t);const e=(0,o.a)(t,a);if(!r||r<e||isNaN(+e))r=e}));return(0,n.w)(a,r||NaN)}var i=null&&a},78181:(t,e,r)=>{r.d(e,{l:()=>a});var n=r(41594);var o=r(51409);function a(t){const{components:e}=(0,o.w)();return n.createElement(e.Dropdown,{...t})}},78933:(t,e,r)=>{r.d(e,{C:()=>n});function n(t){const e={"data-mode":t.mode??undefined,"data-required":"required"in t?t.required:undefined,"data-multiple-months":t.numberOfMonths&&t.numberOfMonths>1||undefined,"data-week-numbers":t.showWeekNumber||undefined,"data-broadcast-calendar":t.broadcastCalendar||undefined};Object.entries(t).forEach((([t,r])=>{if(t.startsWith("data-")){e[t]=r}}));return e}},80007:(t,e,r)=>{r.d(e,{j:()=>o});var n=r(41594);function o(t){const{week:e,...r}=t;return n.createElement("tr",{...r})}},80436:(t,e,r)=>{r.d(e,{i:()=>a});var n=r(22596);var o=r(27256);function a(t,e){const{start:r,end:a}=(0,n.P)(e?.in,t);let i=+r>+a;const u=i?+r:+a;const s=i?a:r;s.setHours(0,0,0,0);s.setDate(1);let c=e?.step??1;if(!c)return[];if(c<0){c=-c;i=!i}const l=[];while(+s<=u){l.push((0,o.w)(r,s));s.setMonth(s.getMonth()+c)}return i?l.reverse():l}var i=null&&a},80517:(t,e,r)=>{r.d(e,{f:()=>a});var n=r(27256);var o=r(49164);function a(t,e,r){const a=(0,o.a)(t,r?.in);if(isNaN(e))return(0,n.w)(r?.in||t,NaN);if(!e)return a;a.setDate(a.getDate()+e);return a}var i=null&&a},80651:(t,e,r)=>{r.d(e,{o:()=>n});function n(t){return"Go to the Previous Month"}},80653:(t,e,r)=>{r.d(e,{M:()=>o});var n=r(32850);function o(t,e,r=0,o=0,a=false,i=n.VA){const{from:u,to:s}=e||{};const{isSameDay:c,isAfter:l,isBefore:d}=i;let f;if(!u&&!s){f={from:t,to:r>0?undefined:t}}else if(u&&!s){if(c(u,t)){if(a){f={from:u,to:undefined}}else{f=undefined}}else if(d(t,u)){f={from:t,to:u}}else{f={from:u,to:t}}}else if(u&&s){if(c(u,t)&&c(s,t)){if(a){f={from:u,to:s}}else{f=undefined}}else if(c(u,t)){f={from:u,to:r>0?undefined:t}}else if(c(s,t)){f={from:t,to:r>0?undefined:t}}else if(d(t,u)){f={from:t,to:s}}else if(l(t,u)){f={from:u,to:t}}else if(l(t,s)){f={from:u,to:t}}else{throw new Error("Invalid range")}}if(f?.from&&f?.to){const e=i.differenceInCalendarDays(f.to,f.from);if(o>0&&e>o){f={from:t,to:undefined}}else if(r>1&&e<r){f={from:t,to:undefined}}}return f}},80807:(t,e,r)=>{r.d(e,{Q:()=>n});function n(t,e,r,n){if(r.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:a=1}=r;const{startOfMonth:i,addMonths:u,differenceInCalendarMonths:s}=n;const c=o?a:1;const l=i(t);if(!e){return u(l,c)}const d=s(e,t);if(d<a){return undefined}return u(l,c)}},80953:(t,e,r)=>{r.d(e,{$2:()=>R,$n:()=>T,Cx:()=>D,Id:()=>K,Jc:()=>X,K3:()=>P,OJ:()=>$,R6:()=>k,V1:()=>C,VT:()=>H,V_:()=>q,gK:()=>V,me:()=>I,pR:()=>z,sC:()=>O});var n=r(97286);var o=r(97665);var a=r(94747);var i=r(12470);var u=r.n(i);var s=r(40874);var c=r(41502);var l=r(4862);var d=r(21508);var f=r(47849);function p(t){"@babel/helpers - typeof";return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function v(t,e){return b(t)||y(t,e)||m(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return g(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function b(t){if(Array.isArray(t))return t}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach((function(e){x(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function x(t,e,r){return(e=A(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A(t){var e=Y(t,"string");return"symbol"==p(e)?e:e+""}function Y(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var k=function t(e,r,n,o,a){var i,u,s;return _(_(_(_(_(_(_({},r&&{lesson_id:r}),{},{topic_id:n,title:e.title,description:e.description,thumbnail_id:(i=(u=e.thumbnail)===null||u===void 0?void 0:u.id)!==null&&i!==void 0?i:null},e.video?Object.fromEntries(Object.entries(e.video).map((function(t){var r;var n=v(t,2),o=n[0],a=n[1];return["video[".concat(o,"]"),o==="source"&&!a?"-1":o==="poster_url"&&!((r=e.video)!==null&&r!==void 0&&r.poster)?"":a]}))):{}),{},{"video[runtime][hours]":e.duration.hour||0,"video[runtime][minutes]":e.duration.minute||0,"video[runtime][seconds]":e.duration.second||0},(0,f.GR)(c.oW.TUTOR_COURSE_PREVIEW)&&{_is_preview:e.lesson_preview?1:0}),{},{tutor_attachments:(e.tutor_attachments||[]).map((function(t){return t.id}))},(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="unlock_by_date"&&{"content_drip_settings[unlock_date]":e.content_drip_settings.unlock_date||""}),(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="specific_days"&&{"content_drip_settings[after_xdays_of_enroll]":e.content_drip_settings.after_xdays_of_enroll||"0"}),(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="after_finishing_prerequisites"&&{"content_drip_settings[prerequisites]":(s=e.content_drip_settings.prerequisites)!==null&&s!==void 0&&s.length?e.content_drip_settings.prerequisites:""}),Object.fromEntries(a.map((function(t){return[t,e[t]||""]}))))};var O=function t(e,r,n,o,a){var i;return _(_(_(_(_({},r&&{assignment_id:r}),{},{topic_id:n,title:e.title,summary:e.summary,attachments:(e.attachments||[]).map((function(t){return t.id})),"assignment_option[time_duration][time]":e.time_duration.time,"assignment_option[time_duration][value]":e.time_duration.value,"assignment_option[deadline_from_start]":e.deadline_from_start?"1":"0","assignment_option[total_mark]":e.total_mark,"assignment_option[pass_mark]":e.pass_mark,"assignment_option[upload_files_limit]":e.upload_files_limit,"assignment_option[upload_file_size_limit]":e.upload_file_size_limit},(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="unlock_by_date"&&{"content_drip_settings[unlock_date]":e.content_drip_settings.unlock_date||""}),(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="specific_days"&&{"content_drip_settings[after_xdays_of_enroll]":e.content_drip_settings.after_xdays_of_enroll||"0"}),(0,f.GR)(c.oW.CONTENT_DRIP)&&o==="after_finishing_prerequisites"&&{"content_drip_settings[prerequisites]":(i=e.content_drip_settings.prerequisites)!==null&&i!==void 0&&i.length?e.content_drip_settings.prerequisites:""}),Object.fromEntries(a.map((function(t){return[t,e[t]||""]}))))};var S=function t(e){return l.b.get(d.A.GET_COURSE_CONTENTS,{params:{course_id:e}})};var I=function t(e){return(0,n.I)({queryKey:["Topic",e],queryFn:function t(){return S(e).then((function(t){return t.data.map((function(t){return _(_({},t),{},{contents:t.contents.map((function(t){return _(_({},t),{},{post_type:t.quiz_type?"tutor_h5p_quiz":t.post_type})}))})}))}))},enabled:!!e})};var E=function t(e){return l.b.post(d.A.SAVE_TOPIC,e)};var C=function t(){var e=(0,o.jE)();var r=(0,s.d)(),n=r.showToast;return(0,a.n)({mutationFn:E,onSuccess:function t(r){if(r.data){n({message:(0,i.__)("Topic saved successfully","tutor"),type:"success"});e.invalidateQueries({queryKey:["Topic"]})}},onError:function t(e){n({type:"danger",message:(0,f.EL)(e)})}})};var M=function t(e){return l.b.post(d.A.DELETE_TOPIC,{topic_id:e})};var T=function t(e){var r=(0,o.jE)();var n=(0,s.d)(),u=n.showToast;return(0,a.n)({mutationFn:M,onSuccess:function t(n,o){if(n.status_code===200){u({message:(0,i.__)(n.message,"tutor"),type:"success"});r.setQueryData(["Topic",e],(function(t){var e=JSON.parse(JSON.stringify(t));return e.filter((function(t){return String(t.id)!==String(o)}))}))}},onError:function t(e){u({type:"danger",message:(0,f.EL)(e)});r.invalidateQueries({queryKey:["Topic"]})}})};var j=function t(e,r){return l.b.get(d.A.GET_LESSON_DETAILS,{params:{topic_id:r,lesson_id:e}})};var D=function t(e,r){return(0,n.I)({queryKey:["Lesson",e,r],queryFn:function t(){return j(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var L=function t(e){return l.b.post(d.A.SAVE_LESSON,e)};var P=function t(e){var r=(0,o.jE)();var n=(0,s.d)(),u=n.showToast;return(0,a.n)({mutationFn:function t(e){return L(e)},onSuccess:function t(n,o){if(n.data){r.invalidateQueries({queryKey:["Topic",e]});r.invalidateQueries({queryKey:["Lesson",o.lesson_id,o.topic_id]});u({message:(0,i.__)("Lesson saved successfully","tutor"),type:"success"})}},onError:function t(e){u({type:"danger",message:(0,f.EL)(e)})}})};var N=function t(e){return l.b.post(d.A.DELETE_TOPIC_CONTENT,{lesson_id:e})};var H=function t(){var e=(0,o.jE)();var r=(0,s.d)(),n=r.showToast;return(0,a.n)({mutationFn:N,onSuccess:function t(r){if(r.status_code===200){n({message:(0,i.__)(r.message,"tutor"),type:"success"});e.invalidateQueries({queryKey:["Topic"]})}},onError:function t(e){n({type:"danger",message:(0,f.EL)(e)})}})};var W=function t(e){return l.b.post(d.A.UPDATE_COURSE_CONTENT_ORDER,e)};var K=function t(){var e=(0,s.d)(),r=e.showToast;return(0,a.n)({mutationFn:W,onError:function t(e){r({type:"danger",message:(0,f.EL)(e)})}})};var B=function t(e,r){return l.b.get(d.A.GET_ASSIGNMENT_DETAILS,{params:{topic_id:r,assignment_id:e}})};var z=function t(e,r){return(0,n.I)({queryKey:["Assignment",e,r],queryFn:function t(){return B(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var U=function t(e){return l.b.post(d.A.SAVE_ASSIGNMENT,e)};var R=function t(e){var r=(0,o.jE)();var n=(0,s.d)(),u=n.showToast;return(0,a.n)({mutationFn:function t(e){return U(e)},onSuccess:function t(n,o){if(n.status_code===200||n.status_code===201){r.invalidateQueries({queryKey:["Topic",Number(e)]});r.invalidateQueries({queryKey:["Assignment",o.assignment_id,o.topic_id]});u({message:(0,i.__)(n.message,"tutor"),type:"success"})}},onError:function t(e){u({type:"danger",message:(0,f.EL)(e)})}})};var F=function t(e){return l.b.post(d.A.DUPLICATE_CONTENT,e)};var q=function t(e){var r=(0,o.jE)();var n=(0,s.d)(),u=n.showToast;return(0,a.n)({mutationFn:F,onSuccess:function t(n,o){if(n.status_code===200||n.status_code===201){u({message:(0,i.__)(n.message,"tutor"),type:"success"});if(["lesson","assignment","quiz","topic"].includes(o.content_type)){r.invalidateQueries({queryKey:["Topic"]});return}if(["question"].includes(o.content_type)){r.invalidateQueries({queryKey:["Quiz",e]});return}}},onError:function t(n,o){u({message:(0,f.EL)(n),type:"danger"});if(["answer"].includes(o.content_type)){r.invalidateQueries({queryKey:["Quiz",e]})}}})};var G=function t(e,r){return l.b.get(d.A.GET_ZOOM_MEETING_DETAILS,{params:{meeting_id:e,topic_id:r}})};var V=function t(e,r){return(0,n.I)({queryKey:["ZoomMeeting",e],queryFn:function t(){return G(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var Q=function t(e,r){return l.b.get(d.A.GET_GOOGLE_MEET_DETAILS,{params:{meeting_id:e,topic_id:r}})};var $=function t(e,r){return(0,n.I)({queryKey:["GoogleMeet",e],queryFn:function t(){return Q(e,r).then((function(t){return t.data}))},enabled:!!e&&!!r})};var Z=function t(e){return l.b.post(d.A.GET_H5P_LESSON_CONTENT,{search_filter:e})};var X=function t(e,r){return(0,n.I)({queryKey:["H5PLessonContents",e],queryFn:function t(){return Z(e).then((function(t){return t.data}))},enabled:r==="lesson"})}},80985:(t,e,r)=>{r.d(e,{x:()=>o});var n=r(41594);function o(t){const{day:e,modifiers:r,...o}=t;const a=n.useRef(null);n.useEffect((()=>{if(r.focused)a.current?.focus()}),[r.focused]);return n.createElement("button",{ref:a,...o})}},81107:(t,e,r)=>{r.d(e,{k:()=>d});var n=r(77846);const o={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]};const a={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]};const i={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]};const u={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]};const s={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}};const c={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}};const l=(t,e)=>{const r=Number(t);const n=r%100;if(n>20||n<10){switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}}return r+"th"};const d={ordinalNumber:l,era:(0,n.o)({values:o,defaultWidth:"wide"}),quarter:(0,n.o)({values:a,defaultWidth:"wide",argumentCallback:t=>t-1}),month:(0,n.o)({values:i,defaultWidth:"wide"}),day:(0,n.o)({values:u,defaultWidth:"wide"}),dayPeriod:(0,n.o)({values:s,defaultWidth:"wide",formattingValues:c,defaultFormattingWidth:"wide"})}},81730:(t,e,r)=>{r.d(e,{A:()=>U});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(53429);var u=r(41594);var s=r.n(u);var c=r(49785);var l=r(38919);var d=r(4704);var f=r(49804);var p=r(85271);var v=r(97011);var h=r(55435);var m=r(46201);var g=r(19502);var y=r(48465);var b=r(41502);var w=r(52457);var _=r(62246);var x=r(82179);var A=r(55077);var Y=r(33191);var k=r(80953);var O=r(21708);var S=r(45538);var I=r(94083);var E=r(34419);var C=r(56499);function M(t){"@babel/helpers - typeof";return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function T(){return T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},T.apply(null,arguments)}function j(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */j=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(T([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==M(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(M(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,C.prototype={constructor:C,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:T(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function D(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?D(Object(r),!0).forEach((function(e){P(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function P(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t){var e=H(t,"string");return"symbol"==M(e)?e:e+""}function H(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function W(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function K(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){W(a,n,o,i,u,"next",t)}function u(t){W(a,n,o,i,u,"throw",t)}i(void 0)}))}}var B=(0,O.p9)();var z=function t(e){var r,a,s;var w=e.onCancel,_=e.data,O=e.topicId,I=e.meetingId;var M=(0,A.$)({defaultValue:true}),D=M.ref,P=M.isScrolling;var N=(0,k.OJ)(I?I:"",O?O:"");var H=_!==null&&_!==void 0?_:N.data;var W=(0,x.p)({defaultValues:{meeting_name:(r=H===null||H===void 0?void 0:H.post_title)!==null&&r!==void 0?r:"",meeting_summary:(a=H===null||H===void 0?void 0:H.post_content)!==null&&a!==void 0?a:"",meeting_start_date:H!==null&&H!==void 0&&H.meeting_data.start_datetime?(0,i["default"])(new Date(H.meeting_data.start_datetime),b.Bd.yearMonthDay):"",meeting_start_time:H!==null&&H!==void 0&&H.meeting_data.start_datetime?(0,i["default"])(new Date(H.meeting_data.start_datetime),b.Bd.hoursMinutes):"",meeting_end_date:H!==null&&H!==void 0&&H.meeting_data.end_datetime?(0,i["default"])(new Date(H.meeting_data.end_datetime),b.Bd.yearMonthDay):"",meeting_end_time:H!==null&&H!==void 0&&H.meeting_data.end_datetime?(0,i["default"])(new Date(H.meeting_data.end_datetime),b.Bd.hoursMinutes):"",meeting_timezone:(s=H===null||H===void 0?void 0:H.meeting_data.timezone)!==null&&s!==void 0?s:"",meeting_enrolledAsAttendee:(H===null||H===void 0?void 0:H.meeting_data.attendees)==="Yes"},shouldFocusError:true,mode:"onChange"});var z=(0,Y.Cz)();var U=y.P.timezones;var F=Object.keys(U).map((function(t){return{label:U[t],value:t}}));var q=function(){var t=K(j().mark((function t(e){var r;return j().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(B){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return z.mutateAsync(L(L(L({},H&&{"post-id":Number(H.ID),"event-id":H.meeting_data.id}),O&&{topic_id:O}),{},{course_id:B,meeting_title:e.meeting_name,meeting_summary:e.meeting_summary,meeting_start_date:e.meeting_start_date,meeting_start_time:e.meeting_start_time,meeting_end_date:e.meeting_end_date,meeting_end_time:e.meeting_end_time,meeting_attendees_enroll_students:e.meeting_enrolledAsAttendee?"Yes":"No",meeting_timezone:e.meeting_timezone,attendees:e.meeting_enrolledAsAttendee?"Yes":"No"}));case 4:r=n.sent;if(r.status_code===200||r.status_code===201){w();W.reset()}case 6:case"end":return n.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){if((0,E.O9)(H)){W.reset({meeting_name:H.post_title,meeting_summary:H.post_content,meeting_start_date:H.meeting_data.start_datetime?(0,i["default"])(new Date(H.meeting_data.start_datetime),b.Bd.yearMonthDay):"",meeting_start_time:H.meeting_data.start_datetime?(0,i["default"])(new Date(H.meeting_data.start_datetime),b.Bd.hoursMinutes):"",meeting_end_date:H.meeting_data.end_datetime?(0,i["default"])(new Date(H.meeting_data.end_datetime),b.Bd.yearMonthDay):"",meeting_end_time:H.meeting_data.end_datetime?(0,i["default"])(new Date(H.meeting_data.end_datetime),b.Bd.hoursMinutes):"",meeting_timezone:H.meeting_data.timezone,meeting_enrolledAsAttendee:H.meeting_data.attendees==="Yes"})}var t=setTimeout((function(){W.setFocus("meeting_name")}),250);return function(){clearTimeout(t)}}),[H]);return(0,n.Y)("div",{css:R.container},(0,n.Y)("div",{css:R.formWrapper,ref:D},(0,n.Y)(S.A,{when:!N.isLoading,fallback:(0,n.Y)(d.p8,null)},(0,n.Y)(c.xI,{name:"meeting_name",control:W.control,rules:{required:(0,o.__)("Name is required","tutor")},render:function t(e){return(0,n.Y)(v.A,T({},e,{label:(0,o.__)("Meeting Name","tutor"),placeholder:(0,o.__)("Enter meeting name","tutor")}))}}),(0,n.Y)(c.xI,{name:"meeting_summary",control:W.control,rules:{required:(0,o.__)("Summary is required","tutor")},render:function t(e){return(0,n.Y)(m.A,T({},e,{label:(0,o.__)("Meeting Summary","tutor"),placeholder:(0,o.__)("Enter meeting summary","tutor"),rows:3,enableResize:true}))}}),(0,n.Y)("div",{css:R.meetingDateTimeWrapper},(0,n.Y)("div",{css:R.dateLabel},(0,o.__)("Meeting Start Date","tutor")),(0,n.Y)("div",{css:R.meetingDateTime},(0,n.Y)(c.xI,{name:"meeting_start_date",control:W.control,rules:{required:(0,o.__)("Start date is required","tutor"),validate:C.Kh},render:function t(e){return(0,n.Y)(p.A,T({},e,{placeholder:(0,o.__)("Start date","tutor"),disabledBefore:(new Date).toISOString()}))}}),(0,n.Y)(c.xI,{name:"meeting_start_time",control:W.control,rules:{required:(0,o.__)("Start time is required","tutor"),validate:C.XA},render:function t(e){return(0,n.Y)(g.A,T({},e,{placeholder:(0,o.__)("Start time","tutor")}))}}))),(0,n.Y)("div",{css:R.meetingDateTimeWrapper},(0,n.Y)("div",{css:R.dateLabel},(0,o.__)("Meeting End Date","tutor")),(0,n.Y)("div",{css:R.meetingDateTime},(0,n.Y)(c.xI,{name:"meeting_end_date",control:W.control,rules:{required:(0,o.__)("End date is required","tutor"),validate:{invalidDate:C.Kh,checkEndDate:function t(e){var r=W.watch("meeting_start_date");var n=e;if(r&&n){return new Date(r)>new Date(n)?(0,o.__)("End date should be greater than start date","tutor"):undefined}return undefined}},deps:["meeting_start_date"]},render:function t(e){return(0,n.Y)(p.A,T({},e,{placeholder:(0,o.__)("End date","tutor"),disabledBefore:W.watch("meeting_start_date")||(new Date).toISOString()}))}}),(0,n.Y)(c.xI,{name:"meeting_end_time",control:W.control,rules:{required:(0,o.__)("End time is required","tutor"),validate:{invalidTime:C.XA,checkEndTime:function t(e){var r=W.watch("meeting_start_date");var n=W.watch("meeting_start_time");var a=W.watch("meeting_end_date");var i=e;if(r&&a&&n&&i){return new Date("".concat(r," ").concat(n))>new Date("".concat(a," ").concat(i))?(0,o.__)("End time should be greater than start time","tutor"):undefined}return undefined}},deps:["meeting_start_time","meeting_start_date","meeting_end_date"]},render:function t(e){return(0,n.Y)(g.A,T({},e,{placeholder:(0,o.__)("End time","tutor")}))}}))),(0,n.Y)(c.xI,{name:"meeting_timezone",control:W.control,rules:{required:(0,o.__)("Timezone is required","tutor")},render:function t(e){return(0,n.Y)(h.A,T({},e,{label:(0,o.__)("Timezone","tutor"),placeholder:(0,o.__)("Select timezone","tutor"),options:F,selectOnFocus:true,isSearchable:true}))}}),(0,n.Y)(c.xI,{name:"meeting_enrolledAsAttendee",control:W.control,render:function t(e){return(0,n.Y)(f.A,T({},e,{label:(0,o.__)("Add enrolled students as attendees","tutor")}))}}))),(0,n.Y)("div",{css:R.buttonWrapper({isScrolling:P})},(0,n.Y)(l.A,{variant:"text",size:"small",onClick:w},(0,o.__)("Cancel","tutor")),(0,n.Y)(l.A,{"data-cy":"save-google-meeting",loading:z.isPending,variant:"primary",size:"small",onClick:W.handleSubmit(q)},H||I?(0,o.__)("Update Meeting","tutor"):(0,o.__)("Create Meeting","tutor"))))};const U=z;var R={container:(0,n.AH)(I.x.display.flex("column")," background:",w.I6.background.white,";padding-block:",w.YK[12],";border-radius:",w.Vq.card,";box-shadow:",w.r7.popover,";",_.I.caption("regular"),";*>label{font-size:",w.J[15],";color:",w.I6.text.title,";}"+(true?"":0),true?"":0),formWrapper:(0,n.AH)(I.x.display.flex("column"),";",I.x.overflowYAuto,";padding-inline:",w.YK[12],";padding-bottom:",w.YK[8],";gap:",w.YK[12],";height:400px;"+(true?"":0),true?"":0),dateLabel:(0,n.AH)(_.I.caption("medium")," color:",w.I6.text.title,";"+(true?"":0),true?"":0),meetingDateTimeWrapper:(0,n.AH)(I.x.display.flex("column")," gap:",w.YK[6],";"+(true?"":0),true?"":0),meetingDateTime:(0,n.AH)(I.x.display.flex()," justify-content:space-between;align-items:flex-start;gap:",w.YK[6],";"+(true?"":0),true?"":0),buttonWrapper:function t(e){var r=e.isScrolling,o=r===void 0?false:r;return(0,n.AH)(I.x.display.flex()," padding-top:",w.YK[8],";padding-inline:",w.YK[12],";justify-content:flex-end;gap:",w.YK[8],";z-index:",w.fE.positive,";",o&&(0,n.AH)("box-shadow:",w.r7.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},81896:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/83571e85f649c56b82349466a5b4c844-neon.png"},82104:(t,e,r)=>{r.d(e,{n:()=>n});function n(t){if(t<10){return`0${t.toLocaleString()}`}return`${t.toLocaleString()}`}},82909:(t,e,r)=>{r.d(e,{s:()=>u});var n=r(31240);const o={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"};const a={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"};const i={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"};const u={date:(0,n.k)({formats:o,defaultWidth:"full"}),time:(0,n.k)({formats:a,defaultWidth:"full"}),dateTime:(0,n.k)({formats:i,defaultWidth:"full"})}},83180:(t,e,r)=>{r.d(e,{c:()=>o});var n=r(41594);function o(t){const{size:e=24,orientation:r="left",className:o}=t;return n.createElement("svg",{className:o,width:e,height:e,viewBox:"0 0 24 24"},r==="up"&&n.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),r==="down"&&n.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),r==="left"&&n.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),r==="right"&&n.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}},83788:(t,e,r)=>{r.d(e,{A:()=>m});var n=r(17437);var o=r(41594);var a=r.n(o);var i=r(52457);var u=r(62246);var s=r(15888);var c=r(94083);var l=r(6502);var d=["css"];function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},f.apply(null,arguments)}function p(t,e){if(null==t)return{};var r,n,o=v(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function v(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}var h=function t(e){var r=e.label,a=e.content,i=e.contentPosition,u=i===void 0?"left":i,s=e.showVerticalBar,c=s===void 0?true:s,v=e.size,h=v===void 0?"regular":v,m=e.type,y=m===void 0?"text":m,b=e.field,w=e.fieldState,_=e.disabled,x=e.readOnly,A=e.loading,Y=e.placeholder,k=e.helpText,O=e.onChange,S=e.onKeyDown,I=e.isHidden,E=e.wrapperCss,C=e.contentCss,M=e.removeBorder,T=M===void 0?false:M,j=e.selectOnFocus,D=j===void 0?false:j;var L=(0,o.useRef)(null);return(0,n.Y)(l.A,{label:r,field:b,fieldState:w,disabled:_,readOnly:x,loading:A,placeholder:Y,helpText:k,isHidden:I,removeBorder:T},(function(t){var e;var r=t.css,o=p(t,d);return(0,n.Y)("div",{css:[g.inputWrapper(!!w.error,T),E,true?"":0,true?"":0]},u==="left"&&(0,n.Y)("div",{css:[g.inputLeftContent(c,h),C,true?"":0,true?"":0]},a),(0,n.Y)("input",f({},b,o,{type:"text",value:(e=b.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=y==="number"?e.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):e.target.value;b.onChange(y==="number"?Number(r):r);if(O){O(r)}},onKeyDown:function t(e){return S===null||S===void 0?void 0:S(e.key)},css:[r,g.input(u,c,h),true?"":0,true?"":0],autoComplete:"off",ref:function t(e){b.ref(e);L.current=e},onFocus:function t(){if(!D||!L.current){return}L.current.select()},"data-input":true})),u==="right"&&(0,n.Y)("div",{css:[g.inputRightContent(c,h),C,true?"":0,true?"":0]},a))}))};const m=(0,s.M)(h);var g={inputWrapper:function t(e,r){return(0,n.AH)("display:flex;align-items:center;",!r&&(0,n.AH)("border:1px solid ",i.I6.stroke["default"],";border-radius:",i.Vq[6],";box-shadow:",i.r7.input,";background-color:",i.I6.background.white,";"+(true?"":0),true?"":0)," ",e&&(0,n.AH)("border-color:",i.I6.stroke.danger,";background-color:",i.I6.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",c.x.inputFocus,";",e&&(0,n.AH)("border-color:",i.I6.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function t(e,r,o){return(0,n.AH)("&[data-input]{",u.I.body(),";border:none;box-shadow:none;background-color:transparent;padding-",e,":0;",r&&(0,n.AH)("padding-",e,":",i.YK[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.AH)("font-size:",i.J[24],";font-weight:",i.Wy.medium,";height:34px;",r&&(0,n.AH)("padding-",e,":",i.YK[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},inputLeftContent:function t(e,r){return(0,n.AH)(u.I.small()," ",c.x.flexCenter()," height:40px;min-width:48px;color:",i.I6.icon.subdued,";padding-inline:",i.YK[12],";",r==="large"&&(0,n.AH)(u.I.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.AH)("border-right:1px solid ",i.I6.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function t(e,r){return(0,n.AH)(u.I.small()," ",c.x.flexCenter()," height:40px;min-width:48px;color:",i.I6.icon.subdued,";padding-inline:",i.YK[12],";",r==="large"&&(0,n.AH)(u.I.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.AH)("border-left:1px solid ",i.I6.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}}},84608:(t,e,r)=>{r.r(e);r.d(e,{formatCaption:()=>n.w,formatDay:()=>o.i,formatMonthCaption:()=>n.G,formatMonthDropdown:()=>a.Z,formatWeekNumber:()=>i.n,formatWeekNumberHeader:()=>u.U,formatWeekdayName:()=>s.Z,formatYearCaption:()=>c.D,formatYearDropdown:()=>c.e});var n=r(13907);var o=r(13663);var a=r(36306);var i=r(82104);var u=r(61757);var s=r(52756);var c=r(72881)},85072:t=>{var e=[];function r(t){var r=-1;for(var n=0;n<e.length;n++){if(e[n].identifier===t){r=n;break}}return r}function n(t,n){var a={};var i=[];for(var u=0;u<t.length;u++){var s=t[u];var c=n.base?s[0]+n.base:s[0];var l=a[c]||0;var d="".concat(c," ").concat(l);a[c]=l+1;var f=r(d);var p={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(f!==-1){e[f].references++;e[f].updater(p)}else{var v=o(p,n);n.byIndex=u;e.splice(u,0,{identifier:d,updater:v,references:1})}i.push(d)}return i}function o(t,e){var r=e.domAPI(e);r.update(t);var n=function e(n){if(n){if(n.css===t.css&&n.media===t.media&&n.sourceMap===t.sourceMap&&n.supports===t.supports&&n.layer===t.layer){return}r.update(t=n)}else{r.remove()}};return n}t.exports=function(t,o){o=o||{};t=t||[];var a=n(t,o);return function t(i){i=i||[];for(var u=0;u<a.length;u++){var s=a[u];var c=r(s);e[c].references--}var l=n(i,o);for(var d=0;d<a.length;d++){var f=a[d];var p=r(f);if(e[p].references===0){e[p].updater();e.splice(p,1)}}a=l}}},85271:(t,e,r)=>{r.d(e,{A:()=>P});var n=r(17437);var o=r(86828);var a=r(53429);var i=r(41594);var u=r.n(i);var s=r(5170);var c=r(38919);var l=r(942);var d=r(41502);var f=r(52457);var p=r(30015);var v=r(94083);var h=r(99972);var m=r(62246);var g=r(6502);function y(t){"@babel/helpers - typeof";return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}var b=["css"];function w(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=x(t,"string");return"symbol"==y(e)?e:e+""}function x(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function A(){return A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},A.apply(null,arguments)}function Y(t,e){if(null==t)return{};var r,n,o=k(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&{}.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function k(t,e){if(null==t)return{};var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}function O(t,e){return M(t)||C(t,e)||I(t,e)||S()}function S(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(t,e){if(t){if("string"==typeof t)return E(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(t,e):void 0}}function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function C(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function M(t){if(Array.isArray(t))return t}function T(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var j=function t(){if(!wp.date){return}var e=wp.date.format;return{formatMonthDropdown:function t(r){return e("F",r)},formatMonthCaption:function t(r){return e("F",r)},formatCaption:function t(r){return e("F",r)},formatWeekdayName:function t(r){return e("D",r)}}};var D=function t(e){if(!e)return undefined;return(0,o["default"])(new Date(e))?new Date(e.length===10?e+"T00:00:00":e):undefined};var L=function t(e){var r=e.label,o=e.field,u=e.fieldState,f=e.disabled,v=e.disabledBefore,h=e.disabledAfter,m=e.loading,y=e.placeholder,_=e.helpText,x=e.isClearable,k=x===void 0?true:x,S=e.onChange,I=e.dateFormat,E=I===void 0?d.Bd.yearMonthDay:I;var C=(0,i.useRef)(null);var M=(0,i.useState)(false),T=O(M,2),L=T[0],P=T[1];var H=D(o.value);var W=H?(0,a["default"])(H,E):"";var K=(0,p.t)({isOpen:L,isDropdown:true}),B=K.triggerRef,z=K.position,U=K.popoverRef;var R=function t(){var e;P(false);(e=C.current)===null||e===void 0||e.focus()};var F=D(v);var q=D(h);return(0,n.Y)(g.A,{label:r,field:o,fieldState:u,disabled:f,loading:m,placeholder:y,helpText:_},(function(t){var e;var r=t.css,i=Y(t,b);return(0,n.Y)("div",null,(0,n.Y)("div",{css:N.wrapper,ref:B},(0,n.Y)("input",A({},i,{css:[r,N.input,true?"":0,true?"":0],ref:function t(e){o.ref(e);C.current=e},type:"text",value:W,onClick:function t(e){e.stopPropagation();P((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();P((function(t){return!t}))}},autoComplete:"off","data-input":true})),(0,n.Y)(l.A,{name:"calendarLine",width:30,height:32,style:N.icon}),k&&o.value&&(0,n.Y)(c.A,{variant:"text",buttonCss:N.clearButton,onClick:function t(){o.onChange("")}},(0,n.Y)(l.A,{name:"times",width:12,height:12}))),(0,n.Y)(p.Z,{isOpen:L,onClickOutside:R,onEscape:R},(0,n.Y)("div",{css:[N.pickerWrapper,w(w({},d.V8?"right":"left",z.left),"top",z.top),true?"":0,true?"":0],ref:U},(0,n.Y)(s.h,{dir:d.V8?"rtl":"ltr",animate:true,mode:"single",formatters:j(),disabled:[!!F&&{before:F},!!q&&{after:q}],selected:H,onSelect:function t(e){if(e){var r=(0,a["default"])(e,d.Bd.yearMonthDay);o.onChange(r);R();if(S){S(r)}}},showOutsideDays:true,captionLayout:"dropdown",autoFocus:true,defaultMonth:H||new Date,startMonth:F||new Date((new Date).getFullYear()-10,0),endMonth:q||new Date((new Date).getFullYear()+10,11),weekStartsOn:(e=wp.date)===null||e===void 0?void 0:e.getSettings().l10n.startOfWeek}))))}))};const P=L;var N={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.AH)("&[data-input]{padding-left:",f.YK[40],";}"+(true?"":0),true?"":0),icon:(0,n.AH)("position:absolute;top:50%;left:",f.YK[8],";transform:translateY(-50%);color:",f.I6.icon["default"],";"+(true?"":0),true?"":0),pickerWrapper:(0,n.AH)(m.I.body("regular"),";position:absolute;background-color:",f.I6.background.white,";box-shadow:",f.r7.popover,";border-radius:",f.Vq[6],";.rdp-root{--rdp-day-height:40px;--rdp-day-width:40px;--rdp-day_button-height:40px;--rdp-day_button-width:40px;--rdp-nav-height:40px;--rdp-today-color:",f.I6.text.title,";--rdp-caption-font-size:",f.J[18],";--rdp-accent-color:",f.I6.action.primary["default"],";--rdp-background-color:",f.I6.background.hover,";--rdp-accent-color-dark:",f.I6.action.primary.active,";--rdp-background-color-dark:",f.I6.action.primary.hover,";--rdp-selected-color:",f.I6.text.white,";--rdp-day_button-border-radius:",f.Vq.circle,";--rdp-outside-opacity:0.5;--rdp-disabled-opacity:0.25;}.rdp-months{margin:",f.YK[16],";}.rdp-month_grid{margin:0px;}.rdp-day{padding:0px;}.rdp-nav{--rdp-accent-color:",f.I6.text.primary,";button{border-radius:",f.Vq.circle,";&:hover,&:focus,&:active{background-color:",f.I6.background.hover,";color:",f.I6.text.primary,";}&:focus-visible:not(:disabled){--rdp-accent-color:",f.I6.text.white,";background-color:",f.I6.background.brand,";}}}.rdp-dropdown_root{.rdp-caption_label{padding:",f.YK[8],";}}.rdp-today{.rdp-day_button{font-weight:",f.Wy.bold,";}}.rdp-selected{color:var(--rdp-selected-color);background-color:var(--rdp-accent-color);border-radius:",f.Vq.circle,";font-weight:",f.Wy.regular,";.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-accent-color);color:",f.I6.text.primary,";}&:focus-visible{outline:2px solid var(--rdp-accent-color);outline-offset:2px;}&:not(.rdp-outside){color:var(--rdp-selected-color);}}}.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-background-color);color:",f.I6.text.primary,";}&:focus-visible:not([disabled]){color:var(--rdp-selected-color);opacity:1;background-color:var(--rdp-accent-color);}}"+(true?"":0),true?"":0),clearButton:(0,n.AH)("position:absolute;top:50%;right:",f.YK[4],";transform:translateY(-50%);width:32px;height:32px;",v.x.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",f.Vq[2],";:hover{background-color:",f.I6.background.hover,";}"+(true?"":0),true?"":0)}},85377:(t,e,r)=>{r.d(e,{p:()=>i});var n=r(27256);var o=r(99719);var a=r(49164);function i(t,e){const r=(0,a.a)(t,e?.in);const i=r.getFullYear();const u=(0,n.w)(r,0);u.setFullYear(i+1,0,4);u.setHours(0,0,0,0);const s=(0,o.b)(u);const c=(0,n.w)(r,0);c.setFullYear(i,0,4);c.setHours(0,0,0,0);const l=(0,o.b)(c);if(r.getTime()>=s.getTime()){return i+1}else if(r.getTime()>=l.getTime()){return i}else{return i-1}}var u=null&&i},86607:(t,e,r)=>{r.d(e,{c:()=>n});function n(t,e,r){const n=t.today();const o=r?t.startOfBroadcastWeek(n,t):e?t.startOfISOWeek(n):t.startOfWeek(n);const a=[];for(let e=0;e<7;e++){const r=t.addDays(o,e);a.push(r)}return a}},87006:(t,e,r)=>{r.d(e,{u:()=>a});var n=r(41594);var o=r(51409);function a(t){const{components:e}=(0,o.w)();return n.createElement(e.Button,{...t})}},87096:(t,e,r)=>{r.d(e,{P:()=>o});var n=r(32917);function o(t){return{...n,...t}}},87177:(t,e,r)=>{r.d(e,{A:()=>x});var n=r(17437);var o=r(12470);var a=r.n(o);var i=r(41594);var u=r.n(i);var s=r(48465);var c=r(41502);var l=r(52457);var d=r(94083);var f=r(47849);function p(t,e){return y(t)||g(t,e)||h(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function y(t){if(Array.isArray(t))return t}var b=!!s.P.tutor_pro_url;if(!window.wp.editor.getDefaultSettings){window.wp.editor.getDefaultSettings=function(){return{}}}function w(t,e,r,n,a,i,u,c,l,d,f,p,v){var h=p||(n?"bold italic underline | image | ".concat(b?"codesample":""):"formatselect bold italic underline | bullist numlist | blockquote | alignleft aligncenter alignright | link unlink | wp_more ".concat(b?" codesample":""," | wp_adv"));var m=v||"strikethrough hr | forecolor pastetext removeformat | charmap | outdent indent | undo redo | wp_help | fullscreen | tutor_button | undoRedoDropdown";h=f?h:h.replaceAll(" | "," ");return{tinymce:{wpautop:true,menubar:false,autoresize_min_height:l||200,autoresize_max_height:d||500,wp_autoresize_on:true,browser_spellcheck:!c,convert_urls:false,end_container_on_empty_block:true,entities:"38,amp,60,lt,62,gt",entity_encoding:"raw",fix_list_elements:true,indent:false,relative_urls:0,remove_script_host:0,plugins:"charmap,colorpicker,hr,lists,image,media,paste,tabfocus,textcolor,fullscreen,wordpress,wpautoresize,wpeditimage,wpemoji,wpgallery,wplink,wpdialogs,wptextpattern,wpview".concat(b?",codesample":""),skin:"light",skin_url:"".concat(s.P.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light"),submit_patch:true,link_context_toolbar:false,theme:"modern",toolbar:!c,toolbar1:h,toolbar2:n?false:m,content_css:"".concat(s.P.site_url,"/wp-includes/css/dashicons.min.css,").concat(s.P.site_url,"/wp-includes/js/tinymce/skins/wordpress/wp-content.css,").concat(s.P.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light/content.min.css"),statusbar:!c,branding:false,setup:function a(i){i.on("init",(function(){if(t&&!c){i.getBody().focus()}if(c){i.setMode("readonly");var e=i.contentDocument.querySelector(".mce-content-body");e.style.backgroundColor="transparent";setTimeout((function(){var t=e.scrollHeight;if(t){i.iframeElement.style.height="".concat(t,"px")}}),500)}}));if(!n){i.addButton("tutor_button",{text:(0,o.__)("Tutor ShortCode","tutor"),icon:false,type:"menubutton",menu:[{text:(0,o.__)("Student Registration Form","tutor"),onclick:function t(){i.insertContent("[tutor_student_registration_form]")}},{text:(0,o.__)("Instructor Registration Form","tutor"),onclick:function t(){i.insertContent("[tutor_instructor_registration_form]")}},{text:(0,o.__)("Courses","tutor"),onclick:function t(){i.windowManager.open({title:(0,o.__)("Courses Shortcode","tutor"),body:[{type:"textbox",name:"id",label:(0,o.__)("Course id, separate by (,) comma","tutor"),value:""},{type:"textbox",name:"exclude_ids",label:(0,o.__)("Exclude Course IDS","tutor"),value:""},{type:"textbox",name:"category",label:(0,o.__)("Category IDS","tutor"),value:""},{type:"listbox",name:"orderby",label:(0,o.__)("Order By","tutor"),onselect:function t(){},values:[{text:"ID",value:"ID"},{text:"title",value:"title"},{text:"rand",value:"rand"},{text:"date",value:"date"},{text:"menu_order",value:"menu_order"},{text:"post__in",value:"post__in"}]},{type:"listbox",name:"order",label:(0,o.__)("Order","tutor"),onselect:function t(){},values:[{text:"DESC",value:"DESC"},{text:"ASC",value:"ASC"}]},{type:"textbox",name:"count",label:(0,o.__)("Count","tutor"),value:"6"}],onsubmit:function t(e){i.insertContent('[tutor_course id="'.concat(e.data.id,'" exclude_ids="').concat(e.data.exclude_ids,'" category="').concat(e.data.category,'" orderby="').concat(e.data.orderby,'" order="').concat(e.data.order,'" count="').concat(e.data.count,'"]'))}})}}]})}i.on("change keyup paste",(function(){e(i.getContent())}));i.on("focus",(function(){r(true)}));i.on("blur",(function(){return r(false)}));i.on("FullscreenStateChanged",(function(t){var e=document.getElementById("tutor-course-builder");var r=document.getElementById("tutor-course-bundle-builder-root");var n=e||r;if(n){if(t.state){n.style.position="relative";n.style.zIndex="100000"}else{n.removeAttribute("style")}}u===null||u===void 0||u(t.state)}))},wp_keep_scroll_position:false,wpeditimage_html5_captions:true},mediaButtons:!a&&!n&&!c,drag_drop_upload:true,quicktags:i||n||c?false:{buttons:["strong","em","block","del","ins","img","ul","ol","li","code","more","close"]}}}var _=function t(e){var r=e.value,o=r===void 0?"":r,a=e.onChange,u=e.isMinimal,s=e.hideMediaButtons,l=e.hideQuickTags,d=e.autoFocus,v=d===void 0?false:d,h=e.onFullScreenChange,m=e.readonly,g=m===void 0?false:m,y=e.min_height,b=e.max_height,_=e.toolbar1,x=e.toolbar2;var Y=(0,i.useRef)(null);var k=(0,i.useRef)((0,f.Ak)()),O=k.current;var S=(0,i.useState)(v),I=p(S,2),E=I[0],C=I[1];var M=function t(e){var r=e.target;a(r.value)};var T=(0,i.useCallback)((function(t){var e=window,r=e.tinymce;if(!r||E){return}var n=window.tinymce.get(O);if(n){if(t!==n.getContent()){n.setContent(t)}}}),[O,E]);(0,i.useEffect)((function(){T(o)}),[o]);(0,i.useEffect)((function(){if(typeof window.wp!=="undefined"&&window.wp.editor){window.wp.editor.remove(O);window.wp.editor.initialize(O,w(E,a,C,u,s,l,h,g,y,b,c.vN.isAboveMobile,_,x));var t=Y.current;t===null||t===void 0||t.addEventListener("change",M);t===null||t===void 0||t.addEventListener("input",M);return function(){window.wp.editor.remove(O);t===null||t===void 0||t.removeEventListener("change",M);t===null||t===void 0||t.removeEventListener("input",M)}}}),[g]);return(0,n.Y)("div",{css:A.wrapper({hideQuickTags:l,isMinimal:u,isFocused:E,isReadOnly:g})},(0,n.Y)("textarea",{"data-cy":"tutor-tinymce",ref:Y,id:O,defaultValue:o}))};const x=_;var A={wrapper:function t(e){var r=e.hideQuickTags,o=e.isMinimal,a=e.isFocused,i=e.isReadOnly;return(0,n.AH)("flex:1;.wp-editor-tools{z-index:auto;}.wp-editor-container{border-top-left-radius:",l.Vq[6],";border-bottom-left-radius:",l.Vq[6],";border-bottom-right-radius:",l.Vq[6],";",a&&!i&&(0,n.AH)(d.x.inputFocus,";"+(true?"":0),true?"":0)," :focus-within{",!i&&d.x.inputFocus,";}}.wp-switch-editor{height:auto;border:1px solid #dcdcde;border-radius:0px;border-top-left-radius:",l.Vq[4],";border-top-right-radius:",l.Vq[4],";top:2px;padding:3px 8px 4px;font-size:13px;color:#646970;&:focus,&:active,&:hover{background:#f0f0f1;color:#646970;}}.mce-btn button{&:focus,&:active,&:hover{background:none;color:#50575e;}}.mce-toolbar-grp,.quicktags-toolbar{border-top-left-radius:",l.Vq[6],";",(r||o)&&(0,n.AH)("border-top-right-radius:",l.Vq[6],";"+(true?"":0),true?"":0),";}.mce-top-part::before{display:none;}.mce-statusbar{border-bottom-left-radius:",l.Vq[6],";border-bottom-right-radius:",l.Vq[6],";}.mce-tinymce{box-shadow:none;background-color:transparent;}.mce-edit-area{background-color:unset;}",(r||o)&&(0,n.AH)(".mce-tinymce.mce-container{border:",!i?"1px solid ".concat(l.I6.stroke["default"]):"none",";border-radius:",l.Vq[6],";",a&&!i&&(0,n.AH)(d.x.inputFocus,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)," textarea{visibility:visible!important;width:100%;resize:none;border:none;outline:none;padding:",l.YK[10],";}"+(true?"":0),true?"":0)}}},87331:(t,e,r)=>{r.d(e,{k:()=>i});var n=r(32850);var o=r(52044);var a=r(70684);function i(t,e,r=n.VA){const i=!Array.isArray(e)?[e]:e;const{isSameDay:u,differenceInCalendarDays:s,isAfter:c}=r;return i.some((e=>{if(typeof e==="boolean"){return e}if(r.isDate(e)){return u(t,e)}if((0,a.Hg)(e,r)){return e.includes(t)}if((0,a.oM)(e)){return(0,o.R)(e,t,false,r)}if((0,a.OE)(e)){if(!Array.isArray(e.dayOfWeek)){return e.dayOfWeek===t.getDay()}return e.dayOfWeek.includes(t.getDay())}if((0,a.m4)(e)){const r=s(e.before,t);const n=s(e.after,t);const o=r>0;const a=n<0;const i=c(e.before,e.after);if(i){return a&&o}else{return o||a}}if((0,a.RE)(e)){return s(t,e.after)>0}if((0,a.Ue)(e)){return s(e.before,t)>0}if(typeof e==="function"){return e(t)}return false}))}const u=null&&i},87496:(t,e,r)=>{r.d(e,{C:()=>i});var n=r(41594);var o=r(26261);var a=r(97665);"use client";function i(t,e){const r=(0,a.jE)(e);const i=r.getQueryCache();return n.useSyncExternalStore(n.useCallback((t=>i.subscribe(o.j.batchCalls(t))),[i]),(()=>r.isFetching(t)),(()=>r.isFetching(t)))}},87982:(t,e,r)=>{r.d(e,{Ss:()=>s,ef:()=>i,xM:()=>u});const n=/^D+$/;const o=/^Y+$/;const a=["D","DD","YY","YYYY"];function i(t){return n.test(t)}function u(t){return o.test(t)}function s(t,e,r){const n=c(t,e,r);console.warn(n);if(a.includes(t))throw new RangeError(n)}function c(t,e,r){const n=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}},89269:(t,e,r)=>{r.d(e,{_:()=>d});var n=r(20207);var o=r(11456);var a=r(85377);var i=r(64585);var u=r(91408);var s=r(66504);var c=r(71951);const l={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};const d={G:function(t,e,r){const n=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});case"GGGG":default:return r.era(n,{width:"wide"})}},y:function(t,e,r){if(e==="yo"){const e=t.getFullYear();const n=e>0?e:1-e;return r.ordinalNumber(n,{unit:"year"})}return c.C.y(t,e)},Y:function(t,e,r,n){const o=(0,u.h)(t,n);const a=o>0?o:1-o;if(e==="YY"){const t=a%100;return(0,s.F)(t,2)}if(e==="Yo"){return r.ordinalNumber(a,{unit:"year"})}return(0,s.F)(a,e.length)},R:function(t,e){const r=(0,a.p)(t);return(0,s.F)(r,e.length)},u:function(t,e){const r=t.getFullYear();return(0,s.F)(r,e.length)},Q:function(t,e,r){const n=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(n);case"QQ":return(0,s.F)(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(t,e,r){const n=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(n);case"qq":return(0,s.F)(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(t,e,r){const n=t.getMonth();switch(e){case"M":case"MM":return c.C.M(t,e);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(t,e,r){const n=t.getMonth();switch(e){case"L":return String(n+1);case"LL":return(0,s.F)(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(t,e,r,n){const o=(0,i.N)(t,n);if(e==="wo"){return r.ordinalNumber(o,{unit:"week"})}return(0,s.F)(o,e.length)},I:function(t,e,r){const n=(0,o.s)(t);if(e==="Io"){return r.ordinalNumber(n,{unit:"week"})}return(0,s.F)(n,e.length)},d:function(t,e,r){if(e==="do"){return r.ordinalNumber(t.getDate(),{unit:"date"})}return c.C.d(t,e)},D:function(t,e,r){const o=(0,n.F)(t);if(e==="Do"){return r.ordinalNumber(o,{unit:"dayOfYear"})}return(0,s.F)(o,e.length)},E:function(t,e,r){const n=t.getDay();switch(e){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});case"EEEE":default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(t,e,r,n){const o=t.getDay();const a=(o-n.weekStartsOn+8)%7||7;switch(e){case"e":return String(a);case"ee":return(0,s.F)(a,2);case"eo":return r.ordinalNumber(a,{unit:"day"});case"eee":return r.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(o,{width:"short",context:"formatting"});case"eeee":default:return r.day(o,{width:"wide",context:"formatting"})}},c:function(t,e,r,n){const o=t.getDay();const a=(o-n.weekStartsOn+8)%7||7;switch(e){case"c":return String(a);case"cc":return(0,s.F)(a,e.length);case"co":return r.ordinalNumber(a,{unit:"day"});case"ccc":return r.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(o,{width:"narrow",context:"standalone"});case"cccccc":return r.day(o,{width:"short",context:"standalone"});case"cccc":default:return r.day(o,{width:"wide",context:"standalone"})}},i:function(t,e,r){const n=t.getDay();const o=n===0?7:n;switch(e){case"i":return String(o);case"ii":return(0,s.F)(o,e.length);case"io":return r.ordinalNumber(o,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});case"iiii":default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(t,e,r){const n=t.getHours();const o=n/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(t,e,r){const n=t.getHours();let o;if(n===12){o=l.noon}else if(n===0){o=l.midnight}else{o=n/12>=1?"pm":"am"}switch(e){case"b":case"bb":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(t,e,r){const n=t.getHours();let o;if(n>=17){o=l.evening}else if(n>=12){o=l.afternoon}else if(n>=4){o=l.morning}else{o=l.night}switch(e){case"B":case"BB":case"BBB":return r.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(t,e,r){if(e==="ho"){let e=t.getHours()%12;if(e===0)e=12;return r.ordinalNumber(e,{unit:"hour"})}return c.C.h(t,e)},H:function(t,e,r){if(e==="Ho"){return r.ordinalNumber(t.getHours(),{unit:"hour"})}return c.C.H(t,e)},K:function(t,e,r){const n=t.getHours()%12;if(e==="Ko"){return r.ordinalNumber(n,{unit:"hour"})}return(0,s.F)(n,e.length)},k:function(t,e,r){let n=t.getHours();if(n===0)n=24;if(e==="ko"){return r.ordinalNumber(n,{unit:"hour"})}return(0,s.F)(n,e.length)},m:function(t,e,r){if(e==="mo"){return r.ordinalNumber(t.getMinutes(),{unit:"minute"})}return c.C.m(t,e)},s:function(t,e,r){if(e==="so"){return r.ordinalNumber(t.getSeconds(),{unit:"second"})}return c.C.s(t,e)},S:function(t,e){return c.C.S(t,e)},X:function(t,e,r){const n=t.getTimezoneOffset();if(n===0){return"Z"}switch(e){case"X":return p(n);case"XXXX":case"XX":return v(n);case"XXXXX":case"XXX":default:return v(n,":")}},x:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"x":return p(n);case"xxxx":case"xx":return v(n);case"xxxxx":case"xxx":default:return v(n,":")}},O:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+f(n,":");case"OOOO":default:return"GMT"+v(n,":")}},z:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+f(n,":");case"zzzz":default:return"GMT"+v(n,":")}},t:function(t,e,r){const n=Math.trunc(+t/1e3);return(0,s.F)(n,e.length)},T:function(t,e,r){return(0,s.F)(+t,e.length)}};function f(t,e=""){const r=t>0?"-":"+";const n=Math.abs(t);const o=Math.trunc(n/60);const a=n%60;if(a===0){return r+String(o)}return r+String(o)+e+(0,s.F)(a,2)}function p(t,e){if(t%60===0){const e=t>0?"-":"+";return e+(0,s.F)(Math.abs(t)/60,2)}return v(t,e)}function v(t,e=""){const r=t>0?"-":"+";const n=Math.abs(t);const o=(0,s.F)(Math.trunc(n/60),2);const a=(0,s.F)(n%60,2);return r+o+e+a}},89441:(t,e,r)=>{r.d(e,{q:()=>o});let n={};function o(){return n}function a(t){n=t}},90208:(t,e,r)=>{r.d(e,{i:()=>a});var n=r(27256);var o=r(49164);function a(t,e,r){const a=(0,o.a)(t,r?.in);if(isNaN(+a))return(0,n.w)(r?.in||t,NaN);a.setFullYear(e);return a}var i=null&&a},90642:(t,e,r)=>{r.d(e,{w:()=>i});var n=r(27256);var o=r(85377);var a=r(99719);function i(t,e){const r=(0,o.p)(t,e);const i=(0,n.w)(e?.in||t,0);i.setFullYear(r,0,4);i.setHours(0,0,0,0);return(0,a.b)(i)}var u=null&&i},90865:(t,e,r)=>{r.d(e,{Y:()=>a});const n={};const o={};function a(t,e){try{const r=n[t]||=new Intl.DateTimeFormat("en-GB",{timeZone:t,hour:"numeric",timeZoneName:"longOffset"}).format;const a=r(e).split("GMT")[1]||"";if(a in o)return o[a];return u(a,a.split(":"))}catch{if(t in o)return o[t];const e=t?.match(i);if(e)return u(t,e.slice(1));return NaN}}const i=/([+-]\d\d):?(\d\d)?/;function u(t,e){const r=+e[0];const n=+(e[1]||0);return o[t]=r>0?r*60+n:r*60-n}},91327:(t,e,r)=>{r.d(e,{$:()=>o});var n=r(41594);function o(t){return n.createElement("button",{...t})}},91408:(t,e,r)=>{r.d(e,{h:()=>u});var n=r(89441);var o=r(27256);var a=r(73524);var i=r(49164);function u(t,e){const r=(0,i.a)(t,e?.in);const u=r.getFullYear();const s=(0,n.q)();const c=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1;const l=(0,o.w)(e?.in||t,0);l.setFullYear(u+1,0,c);l.setHours(0,0,0,0);const d=(0,a.k)(l,e);const f=(0,o.w)(e?.in||t,0);f.setFullYear(u,0,c);f.setHours(0,0,0,0);const p=(0,a.k)(f,e);if(+r>=+d){return u+1}else if(+r>=+p){return u}else{return u-1}}var s=null&&u},91725:(t,e,r)=>{r.d(e,{O:()=>i});var n=r(61797);var o=r(87331);var a=r(69030);function i(t,e,r,u,s,c,l,d=0){if(d>365){return undefined}const f=(0,a.l)(t,e,r.date,u,s,c,l);const p=Boolean(c.disabled&&(0,o.k)(f,c.disabled,l));const v=Boolean(c.hidden&&(0,o.k)(f,c.hidden,l));const h=f;const m=new n.P(f,h,l);if(!p&&!v){return m}return i(t,e,m,u,s,c,l,d+1)}},92833:(t,e,r)=>{r.d(e,{z:()=>o});var n=r(41594);function o(t){return n.createElement("div",{...t})}},92887:(t,e,r)=>{r.d(e,{u:()=>nt});var n=r(92590);var o=r(942);var a=r(72574);var i=r(46201);var u=r(52457);var s=r(62246);var c=r(98880);var l=r(45538);var d=r(85589);var f=r(61025);var p=r(6867);var v=r(96574);var h=r(96970);var m=r(28890);var g=r(50558);var y=r(81896);var b=r(93362);var w=r(98104);var _=r(29214);var x=r(61978);var A=r(40716);var Y=r(40874);var k=r(94083);var O=r(34419);var S=r(17437);var I=r(12470);var E=r.n(I);var C=r(41594);var M=r.n(C);var T=r(49785);var j=r(11260);var D=r(10752);var L=r(52854);function P(t){"@babel/helpers - typeof";return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(t)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach((function(e){W(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function W(t,e,r){return(e=K(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function K(t){var e=B(t,"string");return"symbol"==P(e)?e:e+""}function B(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},z.apply(null,arguments)}function U(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */U=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:O(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function g(){}function y(){}function b(){}var w={};c(w,i,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(M([])));x&&x!==r&&n.call(x,i)&&(w=x);var A=b.prototype=g.prototype=Object.create(w);function Y(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==P(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function t(n,o){function i(){return new e((function(t,e){r(n,o,t,e)}))}return a=a?a.then(i,i):i()}})}function O(e,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=S(u,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(P(e)+" is not iterable")}return y.prototype=b,o(A,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=c(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,c(t,s,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},Y(k.prototype),c(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},Y(A),c(A,s,"Generator"),c(A,i,(function(){return this})),c(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,C.prototype={constructor:C,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function a(e,n){return s.type="throw",s.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var u=this.tryEntries[i],s=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=e,u.arg=r,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:M(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function R(t){return G(t)||q(t)||X(t)||F()}function F(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function q(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function G(t){if(Array.isArray(t))return J(t)}function V(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function Q(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){V(a,n,o,i,u,"next",t)}function u(t){V(a,n,o,i,u,"throw",t)}i(void 0)}))}}function $(t,e){return et(t)||tt(t,e)||X(t,e)||Z()}function Z(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function X(t,e){if(t){if("string"==typeof t)return J(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(t,e):void 0}}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function et(t){if(Array.isArray(t))return t}var rt=[{label:(0,I.__)("None","tutor"),value:"none",image:b.A},{label:(0,I.__)("Photo","tutor"),value:"photo",image:_.A},{label:(0,I.__)("Neon","tutor"),value:"neon",image:y.A},{label:(0,I.__)("3D","tutor"),value:"3d",image:f.A},{label:(0,I.__)("Painting","tutor"),value:"painting",image:w.A},{label:(0,I.__)("Sketch","tutor"),value:"sketch",image:A.A},{label:(0,I.__)("Concept","tutor"),value:"concept_art",image:v.A},{label:(0,I.__)("Illustration","tutor"),value:"illustration",image:g.A},{label:(0,I.__)("Dreamy","tutor"),value:"dreamy",image:h.A},{label:(0,I.__)("Filmic","tutor"),value:"filmic",image:m.A},{label:(0,I.__)("Retro","tutor"),value:"retrowave",image:x.A},{label:(0,I.__)("Black & White","tutor"),value:"black-and-white",image:p.A}];var nt=function t(){var e=(0,T.mN)({defaultValues:{style:"none",prompt:""}});var r=(0,j.A6)(),u=r.images,s=r.setImages;var f=(0,d.R8)();var p=(0,Y.d)(),v=p.showToast;var h=(0,C.useState)(u.every((function(t){return t===null}))),m=$(h,2),g=m[0],y=m[1];var b=(0,C.useState)([false,false,false,false]),w=$(b,2),_=w[0],x=w[1];var A=e.watch("style");var k=e.watch("prompt");var E=!A||!k;var M=u.some(O.O9);(0,C.useEffect)((function(){if(f.isError){v({type:"danger",message:f.error.response.data.message})}}),[f.isError]);(0,C.useEffect)((function(){e.setFocus("prompt")}),[]);return(0,S.Y)("form",{css:L.C.wrapper,onSubmit:e.handleSubmit(function(){var t=Q(U().mark((function t(e){return U().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:x([true,true,true,true]);y(false);r.prev=2;r.next=5;return Promise.all(Array.from({length:4}).map((function(t,r){return f.mutateAsync(e).then((function(t){s((function(e){var n,o;var a=R(e);a[r]=(n=(o=t.data.data)===null||o===void 0||(o=o[0])===null||o===void 0?void 0:o.b64_json)!==null&&n!==void 0?n:null;return a}));x((function(t){var e=R(t);e[r]=false;return e}))}))["catch"]((function(t){x((function(t){var e=R(t);e[r]=false;return e}));throw t}))})));case 5:r.next=11;break;case 7:r.prev=7;r.t0=r["catch"](2);x([false,false,false,false]);y(true);case 11:case"end":return r.stop()}}),t,null,[[2,7]])})));return function(e){return t.apply(this,arguments)}}())},(0,S.Y)("div",{css:L.C.left},(0,S.Y)(l.A,{when:!g,fallback:(0,S.Y)(o.A,{name:"magicAiPlaceholder",width:72,height:72})},(0,S.Y)("div",{css:ot.images},(0,S.Y)(c.A,{each:u},(function(t,e){return(0,S.Y)(D.z,{key:e,src:t,loading:_[e],index:e})}))))),(0,S.Y)("div",{css:L.C.right},(0,S.Y)("div",{css:ot.fields},(0,S.Y)("div",{css:ot.promptWrapper},(0,S.Y)(T.xI,{control:e.control,name:"prompt",render:function t(e){return(0,S.Y)(i.A,z({},e,{label:(0,I.__)("Visualize Your Course","tutor"),placeholder:(0,I.__)("Describe the image you want for your course thumbnail","tutor"),rows:4,isMagicAi:true,disabled:f.isPending,enableResize:false}))}}),(0,S.Y)("button",{type:"button",css:ot.inspireButton,onClick:function t(){var r=j.i3.length;var n=Math.floor(Math.random()*r);e.reset(H(H({},e.getValues()),{},{prompt:j.i3[n]}))},disabled:f.isPending},(0,S.Y)(o.A,{name:"bulbLine"}),(0,I.__)("Inspire Me","tutor"))),(0,S.Y)(T.xI,{control:e.control,name:"style",render:function t(e){return(0,S.Y)(a.A,z({},e,{label:(0,I.__)("Styles","tutor"),options:rt,disabled:f.isPending}))}})),(0,S.Y)("div",{css:L.C.rightFooter},(0,S.Y)(n.A,{type:"submit",disabled:f.isPending||E},(0,S.Y)(o.A,{name:M?"reload":"magicAi",width:24,height:24}),M?(0,I.__)("Generate Again","tutor"):(0,I.__)("Generate Now","tutor")))))};var ot={images:(0,S.AH)("display:grid;grid-template-columns:repeat(2, minmax(150px, 1fr));grid-template-rows:repeat(2, minmax(150px, 1fr));gap:",u.YK[12],";align-self:start;padding:",u.YK[24],";width:100%;height:100%;>div{aspect-ratio:1/1;}"+(true?"":0),true?"":0),fields:(0,S.AH)("display:flex;flex-direction:column;gap:",u.YK[12],";"+(true?"":0),true?"":0),promptWrapper:(0,S.AH)("position:relative;textarea{padding-bottom:",u.YK[40],"!important;}"+(true?"":0),true?"":0),inspireButton:(0,S.AH)(k.x.resetButton,";",s.I.small(),";position:absolute;height:28px;bottom:",u.YK[12],";left:",u.YK[12],";border:1px solid ",u.I6.stroke.brand,";border-radius:",u.Vq[4],";display:flex;align-items:center;gap:",u.YK[4],";color:",u.I6.text.brand,";padding-inline:",u.YK[12],";background-color:",u.I6.background.white,";&:hover{background-color:",u.I6.background.brand,";color:",u.I6.text.white,";}&:focus-visible{outline:2px solid ",u.I6.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",u.I6.background.disable,";color:",u.I6.text.disable,";}"+(true?"":0),true?"":0)}},93035:(t,e,r)=>{r.d(e,{f:()=>a});var n=r(21721);var o=r(49164);function a(t){return!(!(0,n.$)(t)&&typeof t!=="number"||isNaN(+(0,o.a)(t)))}var i=null&&a},93116:(t,e,r)=>{r.r(e);r.d(e,{labelCaption:()=>n.t,labelDay:()=>a.Z,labelDayButton:()=>a.n,labelGrid:()=>n.b,labelGridcell:()=>o.P,labelMonthDropdown:()=>u.a,labelNav:()=>i.y,labelNext:()=>s.s,labelPrevious:()=>c.o,labelWeekNumber:()=>d.k,labelWeekNumberHeader:()=>f.N,labelWeekday:()=>l.n,labelYearDropdown:()=>p.n});var n=r(67918);var o=r(23462);var a=r(59830);var i=r(69545);var u=r(35227);var s=r(35179);var c=r(80651);var l=r(14618);var d=r(94923);var f=r(56690);var p=r(6998)},93362:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/9dcf3f4907036dd08b31bf2a7181bed0-none.jpg"},93397:(t,e,r)=>{r.d(e,{g:()=>o});var n=r(32850);function o(t,e,r=n.VA){const o=!Array.isArray(e)?[e]:e;let a=t.from;const i=r.differenceInCalendarDays(t.to,t.from);const u=Math.min(i,6);for(let t=0;t<=u;t++){if(o.includes(a.getDay())){return true}a=r.addDays(a,1)}return false}},93987:(t,e,r)=>{r.d(e,{E:()=>n});function n(t,e,r,n){if(r.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:a}=r;const{startOfMonth:i,addMonths:u,differenceInCalendarMonths:s}=n;const c=o?a??1:1;const l=i(t);if(!e){return u(l,-c)}const d=s(l,e);if(d<=0){return undefined}return u(l,-c)}},94528:(t,e,r)=>{r.d(e,{Q:()=>o});var n=r(49164);function o(t,e){const r=(0,n.a)(t,e?.in);const o=r.getFullYear();r.setFullYear(o+1,0,0);r.setHours(23,59,59,999);return r}var a=null&&o},94632:(t,e,r)=>{r.d(e,{i:()=>o});var n=r(41594);function o(t){return n.createElement("div",{...t})}},94923:(t,e,r)=>{r.d(e,{k:()=>n});function n(t,e){return`Week ${t}`}},95387:(t,e,r)=>{r.d(e,{o:()=>o});const n={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};const o=(t,e,r,o)=>n[t]},96574:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/9613f2a35fc147cbde38998fc279f6e9-concept.png"},96755:(t,e,r)=>{r.d(e,{A:()=>k});var n=r(12470);var o=r.n(n);var a=r(41594);var i=r.n(a);var u=r(40874);function s(t){"@babel/helpers - typeof";return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t){return f(t)||d(t)||h(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function f(t){if(Array.isArray(t))return m(t)}function p(t,e){return y(t)||g(t,e)||h(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r["return"]&&(i=r["return"](),Object(i)!==i))return}finally{if(c)throw o}}return u}}function y(t){if(Array.isArray(t))return t}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach((function(e){_(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=A(t,"string");return"symbol"==s(e)?e:e+""}function A(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var Y=function t(e){var r=e.options,o=r===void 0?{}:r,i=e.onChange,s=e.initialFiles;var l=(0,u.d)(),d=l.showToast;var f=(0,a.useMemo)((function(){return s?Array.isArray(s)?s:[s]:[]}),[s]);var v=(0,a.useMemo)((function(){return w(w(w({},o),o.type?{library:{type:o.type}}:{}),{},{multiple:o.multiple?o.multiple===true?"add":o.multiple:false})}),[o]);var h=(0,a.useState)(f),m=p(h,2),g=m[0],y=m[1];(0,a.useEffect)((function(){if(f&&!g.length){y(f)}}),[g,f]);var b=(0,a.useCallback)((function(){var t;if(!((t=window.wp)!==null&&t!==void 0&&t.media)){console.error("WordPress media library is not available");return}var e=window.wp.media(v);e.on("close",(function(){if(e.$el){e.$el.parent().parent().remove()}}));e.on("open",(function(){var t=e.state().get("selection");e.$el.attr("data-focus-trap","true");t.reset();g.forEach((function(e){var r=window.wp.media.attachment(e.id);if(r){r.fetch();t.add(r)}}))}));e.on("select",(function(){var t=e.state().get("selection").toJSON();var r=new Set(t.map((function(t){return t.id})));var o=g.filter((function(t){return r.has(t.id)}));var a=t.reduce((function(t,e){if(o.some((function(t){return t.id===e.id}))){return t}if(v.maxFileSize&&e.filesizeInBytes>v.maxFileSize){d({message:(0,n.sprintf)((0,n.__)("%s size exceeds the maximum allowed size","tutor"),e.title),type:"danger"});return t}var r={id:e.id,title:e.title,url:e.url,name:e.title,size:e.filesizeHumanReadable,size_bytes:e.filesizeInBytes,ext:e.filename.split(".").pop()||""};t.push(r);return t}),[]);var u=v.multiple?[].concat(c(o),c(a)):a.slice(0,1);if(v.maxFiles&&u.length>v.maxFiles){d({message:(0,n.sprintf)((0,n.__)("Cannot select more than %d files","tutor"),v.maxFiles),type:"warning"});return}y(u);i===null||i===void 0||i(v.multiple?u:u[0]||null);e.close()}));e.open()}),[v,i,g,d]);var _=(0,a.useCallback)((function(){y([]);i===null||i===void 0||i(v.multiple?[]:null)}),[v.multiple,i]);return{openMediaLibrary:b,existingFiles:g,resetFiles:_}};const k=Y},96970:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/ff5a8a3d6c18c02f00d659da3824176b-dreamy.png"},97328:(t,e,r)=>{r.d(e,{S:()=>o});var n=r(41594);function o(t){return n.createElement("thead",{"aria-hidden":true},n.createElement("tr",{...t}))}},97766:(t,e,r)=>{r.d(e,{UI:()=>n,X5:()=>i,pL:()=>o,wc:()=>a});var n;(function(t){t["Root"]="root";t["Chevron"]="chevron";t["Day"]="day";t["DayButton"]="day_button";t["CaptionLabel"]="caption_label";t["Dropdowns"]="dropdowns";t["Dropdown"]="dropdown";t["DropdownRoot"]="dropdown_root";t["Footer"]="footer";t["MonthGrid"]="month_grid";t["MonthCaption"]="month_caption";t["MonthsDropdown"]="months_dropdown";t["Month"]="month";t["Months"]="months";t["Nav"]="nav";t["NextMonthButton"]="button_next";t["PreviousMonthButton"]="button_previous";t["Week"]="week";t["Weeks"]="weeks";t["Weekday"]="weekday";t["Weekdays"]="weekdays";t["WeekNumber"]="week_number";t["WeekNumberHeader"]="week_number_header";t["YearsDropdown"]="years_dropdown"})(n||(n={}));var o;(function(t){t["disabled"]="disabled";t["hidden"]="hidden";t["outside"]="outside";t["focused"]="focused";t["today"]="today"})(o||(o={}));var a;(function(t){t["range_end"]="range_end";t["range_middle"]="range_middle";t["range_start"]="range_start";t["selected"]="selected"})(a||(a={}));var i;(function(t){t["weeks_before_enter"]="weeks_before_enter";t["weeks_before_exit"]="weeks_before_exit";t["weeks_after_enter"]="weeks_after_enter";t["weeks_after_exit"]="weeks_after_exit";t["caption_after_enter"]="caption_after_enter";t["caption_after_exit"]="caption_after_exit";t["caption_before_enter"]="caption_before_enter";t["caption_before_exit"]="caption_before_exit"})(i||(i={}))},97822:(t,e,r)=>{r.d(e,{Y:()=>o});var n=r(49164);function o(t,e){return+(0,n.a)(t)<+(0,n.a)(e)}var a=null&&o},97825:t=>{function e(t,e,r){var n="";if(r.supports){n+="@supports (".concat(r.supports,") {")}if(r.media){n+="@media ".concat(r.media," {")}var o=typeof r.layer!=="undefined";if(o){n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")}n+=r.css;if(o){n+="}"}if(r.media){n+="}"}if(r.supports){n+="}"}var a=r.sourceMap;if(a&&typeof btoa!=="undefined"){n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")}e.styleTagTransform(n,t,e.options)}function r(t){if(t.parentNode===null){return false}t.parentNode.removeChild(t)}function n(t){if(typeof document==="undefined"){return{update:function t(){},remove:function t(){}}}var n=t.insertStyleElement(t);return{update:function r(o){e(n,t,o)},remove:function t(){r(n)}}}t.exports=n},98104:(t,e,r)=>{r.d(e,{A:()=>n});const n=r.p+"images/fc8edfd709e8f6ed349b59a0f0a00647-painting.png"},98637:(t,e,r)=>{r.d(e,{J:()=>o});var n=r(80517);function o(t,e,r){return(0,n.f)(t,e*7,r)}var a=null&&o},99719:(t,e,r)=>{r.d(e,{b:()=>o});var n=r(73524);function o(t,e){return(0,n.k)(t,{...e,weekStartsOn:1})}var a=null&&o},99905:(t,e,r)=>{r.d(e,{O:()=>a});var n=r(21337);var o=r(73958);function a(t,e){const r=(0,o.l)(t,e);const a=(0,n.I)(t,e);const i=e.addDays(r,a*7-1);return i}},99972:(t,e,r)=>{var n=r(85072);var o=r.n(n);var a=r(97825);var i=r.n(a);var u=r(77659);var s=r.n(u);var c=r(55056);var l=r.n(c);var d=r(10540);var f=r.n(d);var p=r(41113);var v=r.n(p);var h=r(25631);var m={};m.styleTagTransform=v();m.setAttributes=l();m.insert=s().bind(null,"head");m.domAPI=i();m.insertStyleElement=f();var g=o()(h.A,m);var y=h.A&&h.A.locals?h.A.locals:undefined}}]);
<?php
/**
 * Plugin Name: Woo Product Manager
 * Plugin URI: https://example.com/woo-pruduct
 * Description: WooCommerce ürünlerini tarayarak Tutor LMS kurslarına bağlı olanları tespit eder ve sipariş durumuna göre buton görünürlüğünü kontrol eder.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * Text Domain: woo-pruduct
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Plugin sabitleri
define('WOO_PRUDUCT_VERSION', '1.0.0');
define('WOO_PRUDUCT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WOO_PRUDUCT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOO_PRUDUCT_PLUGIN_FILE', __FILE__);

/**
 * Ana eklenti sınıfı
 */
class WooPruduct {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Singleton pattern
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Plugin başlatma
     */
    public function init() {
        // Gerekli eklentilerin kontrolü
        if (!$this->check_dependencies()) {
            return;
        }
        
        // Hook'ları kaydet
        $this->register_hooks();
        
        // Admin paneli
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        }
        
        // AJAX işlemleri
        add_action('wp_ajax_woo_pruduct_scan', array($this, 'ajax_scan_products'));
        add_action('wp_ajax_woo_pruduct_toggle_button', array($this, 'ajax_toggle_button'));
        add_action('wp_ajax_woo_pruduct_clear_logs', array($this, 'ajax_clear_logs'));
    }
    
    /**
     * Gerekli eklentilerin kontrolü
     */
    private function check_dependencies() {
        $missing_plugins = array();
        
        // WooCommerce kontrolü
        if (!class_exists('WooCommerce')) {
            $missing_plugins[] = 'WooCommerce';
        }
        
        // Tutor LMS kontrolü
        if (!function_exists('tutor_utils')) {
            $missing_plugins[] = 'Tutor LMS';
        }
        
        if (!empty($missing_plugins)) {
            add_action('admin_notices', function() use ($missing_plugins) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(
                    __('Woo Product Manager eklentisi çalışmak için şu eklentilere ihtiyaç duyar: %s', 'woo-pruduct'),
                    implode(', ', $missing_plugins)
                );
                echo '</p></div>';
            });
            return false;
        }
        
        return true;
    }
    
    /**
     * Hook'ları kaydet
     */
    private function register_hooks() {
        // WooCommerce sipariş durumu değişikliklerini izle
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);
        
        // Ürün sayfasında buton kontrolü
        add_filter('woocommerce_is_purchasable', array($this, 'control_product_purchasability'), 10, 2);
        
        // Sepete ekle butonu kontrolü
        add_action('woocommerce_single_product_summary', array($this, 'control_add_to_cart_button'), 25);

        // Ürün sekmelerine "Kurs Bilgileri" sekmesi ekle
        add_filter('woocommerce_product_tabs', array($this, 'add_course_info_tab'), 25);
        add_action('woocommerce_product_tab_course_info', array($this, 'course_info_tab_content'));
    }
    
    /**
     * Sipariş durumu değişikliğini işle
     */
    public function handle_order_status_change($order_id, $old_status, $new_status) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        // Sipariş öğelerini kontrol et
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $course_id = $this->get_course_by_product($product_id);
            
            if ($course_id) {
                $this->log_order_status_change($order_id, $product_id, $course_id, $old_status, $new_status);
                
                // Buton durumunu güncelle
                $this->update_button_visibility($product_id, $course_id, $new_status);
            }
        }
    }
    
    /**
     * Ürünün satın alınabilirliğini kontrol et
     */
    public function control_product_purchasability($is_purchasable, $product) {
        if (!$is_purchasable) {
            return $is_purchasable;
        }
        
        $product_id = $product->get_id();
        $course_id = $this->get_course_by_product($product_id);
        
        if ($course_id) {
            $user_id = get_current_user_id();
            if ($user_id && $this->is_user_enrolled_in_course($user_id, $course_id)) {
                return false; // Kullanıcı zaten kayıtlı, satın alınamaz
            }
        }
        
        return $is_purchasable;
    }
    
    /**
     * Sepete ekle butonunu kontrol et
     */
    public function control_add_to_cart_button() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        $product_id = $product->get_id();
        $course_id = $this->get_course_by_product($product_id);
        
        if ($course_id) {
            $user_id = get_current_user_id();
            if ($user_id && $this->is_user_enrolled_in_course($user_id, $course_id)) {
                // Kullanıcı kayıtlı, özel mesaj göster
                remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
                add_action('woocommerce_single_product_summary', array($this, 'show_enrolled_message'), 30);
            }
        }
    }
    
    /**
     * Kayıtlı kullanıcı mesajını göster
     */
    public function show_enrolled_message() {
        global $product;
        $course_id = $this->get_course_by_product($product->get_id());
        $course_url = get_permalink($course_id);
        $enrollment_date = $this->get_user_enrollment_date(get_current_user_id(), $course_id);
        
        echo '<div class="woo-pruduct-enrolled-message">';
        echo '<p class="enrolled-status"><span class="dashicons dashicons-yes-alt"></span> ' . __('Bu kursa zaten kayıtlısınız!', 'woo-pruduct') . '</p>';
        if ($enrollment_date) {
            echo '<p class="enrollment-date">' . sprintf(__('Kayıt tarihi: %s', 'woo-pruduct'), $enrollment_date) . '</p>';
        }
        echo '<a href="' . esc_url($course_url) . '" class="button alt">' . __('Kursa Devam Et', 'woo-pruduct') . '</a>';
        echo '</div>';
        
        // CSS ekle
        wp_add_inline_style('woocommerce-general', '
            .woo-pruduct-enrolled-message {
                background: #f0f8ff;
                border: 2px solid #0073aa;
                border-radius: 5px;
                padding: 20px;
                margin: 20px 0;
                text-align: center;
            }
            .woo-pruduct-enrolled-message .enrolled-status {
                color: #0073aa;
                font-weight: bold;
                font-size: 16px;
                margin-bottom: 10px;
            }
            .woo-pruduct-enrolled-message .dashicons {
                color: #46b450;
            }
            .woo-pruduct-enrolled-message .enrollment-date {
                color: #666;
                font-size: 14px;
                margin-bottom: 15px;
            }
        ');
    }
    
    /**
     * Ürüne bağlı kursu bul
     */
    private function get_course_by_product($product_id) {
        global $wpdb;
        
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} 
             WHERE meta_key = '_tutor_course_product_id' 
             AND meta_value = %d",
            $product_id
        ));
        
        return $course_id ? intval($course_id) : false;
    }
    
    /**
     * Kullanıcının kursa kayıtlı olup olmadığını kontrol et
     */
    private function is_user_enrolled_in_course($user_id, $course_id) {
        if (function_exists('tutor_utils')) {
            return tutor_utils()->is_enrolled($course_id, $user_id);
        }
        
        // Fallback: Manuel kontrol
        global $wpdb;
        $enrolled = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_type = 'tutor_enrolled' 
             AND post_parent = %d 
             AND post_author = %d 
             AND post_status = 'completed'",
            $course_id,
            $user_id
        ));
        
        return !empty($enrolled);
    }
    
    /**
     * Kullanıcının kursa kayıt tarihini al
     */
    private function get_user_enrollment_date($user_id, $course_id) {
        global $wpdb;
        
        $enrollment = $wpdb->get_row($wpdb->prepare(
            "SELECT post_date FROM {$wpdb->posts} 
             WHERE post_type = 'tutor_enrolled' 
             AND post_parent = %d 
             AND post_author = %d 
             AND post_status = 'completed'",
            $course_id,
            $user_id
        ));
        
        if ($enrollment) {
            return date_i18n(get_option('date_format'), strtotime($enrollment->post_date));
        }
        
        return false;
    }
    
    /**
     * Buton görünürlüğünü güncelle
     */
    private function update_button_visibility($product_id, $course_id, $order_status) {
        // Bu fonksiyon gelecekte genişletilebilir
        // Şu an için sadece log tutuyor
        $this->log_button_update($product_id, $course_id, $order_status);
    }
    
    /**
     * Sipariş durumu değişikliğini logla
     */
    private function log_order_status_change($order_id, $product_id, $course_id, $old_status, $new_status) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'order_id' => $order_id,
            'product_id' => $product_id,
            'course_id' => $course_id,
            'old_status' => $old_status,
            'new_status' => $new_status
        );
        
        $logs = get_option('woo_pruduct_logs', array());
        $logs[] = $log_entry;
        
        // Son 1000 log kaydını tut
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        update_option('woo_pruduct_logs', $logs);
    }
    
    /**
     * Buton güncelleme logla
     */
    private function log_button_update($product_id, $course_id, $order_status) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'action' => 'button_update',
            'product_id' => $product_id,
            'course_id' => $course_id,
            'order_status' => $order_status
        );
        
        $logs = get_option('woo_pruduct_button_logs', array());
        $logs[] = $log_entry;
        
        // Son 500 log kaydını tut
        if (count($logs) > 500) {
            $logs = array_slice($logs, -500);
        }
        
        update_option('woo_pruduct_button_logs', $logs);
    }
    
    /**
     * Plugin aktivasyonu
     */
    public function activate() {
        // Varsayılan seçenekleri ayarla
        add_option('woo_pruduct_version', WOO_PRUDUCT_VERSION);
        add_option('woo_pruduct_logs', array());
        add_option('woo_pruduct_button_logs', array());
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Admin menüsü ekle
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Woo Product Manager', 'woo-pruduct'),
            __('Woo Product', 'woo-pruduct'),
            'manage_options',
            'woo-pruduct',
            array($this, 'admin_page'),
            'dashicons-cart',
            30
        );

        add_submenu_page(
            'woo-pruduct',
            __('Ürün Tarama', 'woo-pruduct'),
            __('Ürün Tarama', 'woo-pruduct'),
            'manage_options',
            'woo-pruduct-scan',
            array($this, 'scan_page')
        );

        add_submenu_page(
            'woo-pruduct',
            __('Loglar', 'woo-pruduct'),
            __('Loglar', 'woo-pruduct'),
            'manage_options',
            'woo-pruduct-logs',
            array($this, 'logs_page')
        );
    }

    /**
     * Admin scriptleri
     */
    public function admin_scripts($hook) {
        if (strpos($hook, 'woo-pruduct') === false) {
            return;
        }

        wp_enqueue_script(
            'woo-pruduct-admin',
            WOO_PRUDUCT_PLUGIN_URL . 'assets/admin.js',
            array('jquery'),
            WOO_PRUDUCT_VERSION,
            true
        );

        wp_localize_script('woo-pruduct-admin', 'wooPruductAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_pruduct_nonce'),
            'strings' => array(
                'scanning' => __('Taranıyor...', 'woo-pruduct'),
                'completed' => __('Tamamlandı', 'woo-pruduct'),
                'error' => __('Hata oluştu', 'woo-pruduct')
            )
        ));

        wp_enqueue_style(
            'woo-pruduct-admin',
            WOO_PRUDUCT_PLUGIN_URL . 'assets/admin.css',
            array(),
            WOO_PRUDUCT_VERSION
        );
    }

    /**
     * Ana admin sayfası
     */
    public function admin_page() {
        include WOO_PRUDUCT_PLUGIN_DIR . 'includes/admin-page.php';
    }

    /**
     * Tarama sayfası
     */
    public function scan_page() {
        include WOO_PRUDUCT_PLUGIN_DIR . 'includes/scan-page.php';
    }

    /**
     * Log sayfası
     */
    public function logs_page() {
        include WOO_PRUDUCT_PLUGIN_DIR . 'includes/logs-page.php';
    }

    /**
     * AJAX: Ürün tarama
     */
    public function ajax_scan_products() {
        check_ajax_referer('woo_pruduct_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok', 'woo-pruduct'));
        }

        $results = $this->scan_tutor_products();

        wp_send_json_success($results);
    }

    /**
     * AJAX: Buton durumu değiştir
     */
    public function ajax_toggle_button() {
        check_ajax_referer('woo_pruduct_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok', 'woo-pruduct'));
        }

        $product_id = intval($_POST['product_id']);
        $action = sanitize_text_field($_POST['action_type']);

        // Buton durumu değiştirme işlemi burada yapılacak
        $result = $this->toggle_product_button($product_id, $action);

        if ($result) {
            wp_send_json_success(__('İşlem başarılı', 'woo-pruduct'));
        } else {
            wp_send_json_error(__('İşlem başarısız', 'woo-pruduct'));
        }
    }

    /**
     * Tutor kurslarına bağlı ürünleri tara
     */
    private function scan_tutor_products() {
        global $wpdb;

        // Tutor kurslarına bağlı ürünleri bul
        $products = $wpdb->get_results("
            SELECT pm.post_id as course_id, pm.meta_value as product_id, p.post_title as course_title
            FROM {$wpdb->postmeta} pm
            LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_tutor_course_product_id'
            AND p.post_status = 'publish'
            AND p.post_type = 'courses'
        ");

        $results = array();

        foreach ($products as $product) {
            $wc_product = wc_get_product($product->product_id);
            if (!$wc_product) {
                continue;
            }

            // Bu ürün için siparişleri kontrol et
            $orders = $this->get_product_orders($product->product_id);

            $results[] = array(
                'course_id' => $product->course_id,
                'course_title' => $product->course_title,
                'product_id' => $product->product_id,
                'product_title' => $wc_product->get_name(),
                'product_price' => $wc_product->get_price(),
                'orders_count' => count($orders),
                'completed_orders' => $this->count_completed_orders($orders),
                'orders' => $orders
            );
        }

        return $results;
    }

    /**
     * Ürün siparişlerini al
     */
    private function get_product_orders($product_id) {
        global $wpdb;

        $orders = $wpdb->get_results($wpdb->prepare("
            SELECT DISTINCT p.ID as order_id, p.post_status as order_status, p.post_date
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
            WHERE p.post_type = 'shop_order'
            AND oim.meta_key = '_product_id'
            AND oim.meta_value = %d
            ORDER BY p.post_date DESC
        ", $product_id));

        return $orders;
    }

    /**
     * Tamamlanmış siparişleri say
     */
    private function count_completed_orders($orders) {
        $completed = 0;
        foreach ($orders as $order) {
            if ($order->order_status === 'wc-completed') {
                $completed++;
            }
        }
        return $completed;
    }

    /**
     * Ürün butonunu aç/kapat
     */
    private function toggle_product_button($product_id, $action) {
        // Bu fonksiyon gelecekte genişletilebilir
        // Şu an için sadece log tutuyor
        $this->log_button_toggle($product_id, $action);
        return true;
    }

    /**
     * Buton değiştirme işlemini logla
     */
    private function log_button_toggle($product_id, $action) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'action' => 'manual_toggle',
            'product_id' => $product_id,
            'toggle_action' => $action,
            'user_id' => get_current_user_id()
        );

        $logs = get_option('woo_pruduct_button_logs', array());
        $logs[] = $log_entry;

        update_option('woo_pruduct_button_logs', $logs);
    }

    /**
     * AJAX: Logları temizle
     */
    public function ajax_clear_logs() {
        check_ajax_referer('woo_pruduct_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Yetkiniz yok', 'woo-pruduct'));
        }

        // Logları temizle
        delete_option('woo_pruduct_logs');
        delete_option('woo_pruduct_button_logs');

        // Yeni boş log dizileri oluştur
        add_option('woo_pruduct_logs', array());
        add_option('woo_pruduct_button_logs', array());

        wp_send_json_success(__('Loglar başarıyla temizlendi', 'woo-pruduct'));
    }

    /**
     * WooCommerce ürün sekmelerine "Kurs Bilgileri" sekmesi ekle
     */
    public function add_course_info_tab($tabs) {
        global $product;

        if (!$product) {
            return $tabs;
        }

        $product_id = $product->get_id();
        $course_id = $this->get_course_by_product($product_id);

        // Sadece Tutor LMS kurslarına bağlı ürünlerde sekmeyi göster
        if ($course_id) {
            $tabs['course_info'] = array(
                'title'    => __('Kurs Bilgileri', 'woo-pruduct'),
                'priority' => 25, // Değerlendirmeler sekmesinden sonra
                'callback' => array($this, 'course_info_tab_content')
            );
        }

        return $tabs;
    }

    /**
     * "Kurs Bilgileri" sekmesi içeriği
     */
    public function course_info_tab_content() {
        global $product;

        if (!$product) {
            return;
        }

        $product_id = $product->get_id();
        $course_id = $this->get_course_by_product($product_id);

        if (!$course_id) {
            echo '<p>' . __('Bu ürün için kurs bilgisi bulunamadı.', 'woo-pruduct') . '</p>';
            return;
        }

        // Kurs bilgilerini al
        $course = get_post($course_id);
        if (!$course) {
            echo '<p>' . __('Kurs bilgileri yüklenemedi.', 'woo-pruduct') . '</p>';
            return;
        }

        // Kurs meta bilgilerini al
        $course_meta = $this->get_course_meta_info($course_id);
        $course_stats = $this->get_course_statistics($course_id);
        $course_curriculum = $this->get_course_curriculum($course_id);

        // Template'i yükle
        $this->render_course_info_template($course, $course_meta, $course_stats, $course_curriculum);
    }

    /**
     * Kurs meta bilgilerini al
     */
    private function get_course_meta_info($course_id) {
        global $wpdb;

        $meta = array();

        // Kurs seviyesi - Türkçe çeviri
        $level = get_post_meta($course_id, '_tutor_course_level', true);
        if ($level) {
            // İngilizce seviyeleri Türkçe'ye çevir
            $level_translations = array(
                'all_levels' => 'Tüm Seviyeler',
                'beginner' => 'Başlangıç',
                'intermediate' => 'Orta',
                'advanced' => 'İleri',
                'expert' => 'Uzman'
            );

            $meta['level'] = isset($level_translations[$level]) ? $level_translations[$level] : $level;
        }

        // Kurs süresi - Array hatasını düzelt
        $duration = get_post_meta($course_id, '_course_duration', true);
        if ($duration) {
            // Eğer array ise, uygun formatla
            if (is_array($duration)) {
                if (isset($duration['hours']) || isset($duration['minutes'])) {
                    $hours = isset($duration['hours']) ? intval($duration['hours']) : 0;
                    $minutes = isset($duration['minutes']) ? intval($duration['minutes']) : 0;

                    if ($hours > 0 && $minutes > 0) {
                        $meta['duration'] = sprintf('%d saat %d dakika', $hours, $minutes);
                    } elseif ($hours > 0) {
                        $meta['duration'] = sprintf('%d saat', $hours);
                    } elseif ($minutes > 0) {
                        $meta['duration'] = sprintf('%d dakika', $minutes);
                    }
                } else {
                    // Array ama farklı format
                    $meta['duration'] = implode(' ', array_filter($duration));
                }
            } else {
                // String ise direkt kullan
                $meta['duration'] = $duration;
            }
        }

        // Maksimum öğrenci sayısı
        $max_students = get_post_meta($course_id, '_tutor_course_max_students', true);
        if ($max_students && $max_students > 0) {
            $meta['max_students'] = $max_students;
        }

        // Kurs dili
        $language = get_post_meta($course_id, '_tutor_course_language', true);
        if ($language) {
            $meta['language'] = $language;
        }

        // Kayıtlı öğrenci sayısını buraya ekle
        $total_students = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->posts}
             WHERE post_type = 'tutor_enrolled'
             AND post_parent = %d
             AND post_status = 'completed'",
            $course_id
        ));
        $meta['total_students'] = intval($total_students);

        // Kurs son güncelleme tarihini ekle
        $course_post = get_post($course_id);
        if ($course_post && $course_post->post_modified) {
            $meta['last_updated'] = date_i18n(get_option('date_format'), strtotime($course_post->post_modified));
        }

        // Eğitmen bilgilerini al
        $instructor_info = $this->get_course_instructor_info($course_id);
        if ($instructor_info) {
            $meta['instructor'] = $instructor_info;
        }

        // Kurs gereksinimleri
        $requirements = get_post_meta($course_id, '_tutor_course_requirements', true);
        if ($requirements) {
            $meta['requirements'] = $requirements;
        }

        // Kurs hedef kitlesi
        $target_audience = get_post_meta($course_id, '_tutor_course_target_audience', true);
        if ($target_audience) {
            $meta['target_audience'] = $target_audience;
        }

        // Kurs faydaları
        $benefits = get_post_meta($course_id, '_tutor_course_benefits', true);
        if ($benefits) {
            $meta['benefits'] = $benefits;
        }

        return $meta;
    }

    /**
     * Kurs istatistiklerini al
     */
    private function get_course_statistics($course_id) {
        global $wpdb;

        $stats = array();

        // Toplam ders sayısı - Topic'ler üzerinden hesapla
        $total_lessons = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(lesson.ID)
             FROM {$wpdb->posts} lesson
             INNER JOIN {$wpdb->posts} topic ON lesson.post_parent = topic.ID
             WHERE lesson.post_type = 'lesson'
             AND topic.post_parent = %d
             AND lesson.post_status = 'publish'
             AND topic.post_status = 'publish'",
            $course_id
        ));
        $stats['total_lessons'] = intval($total_lessons);

        // Toplam quiz sayısı - Topic'ler üzerinden hesapla
        $total_quizzes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(quiz.ID)
             FROM {$wpdb->posts} quiz
             INNER JOIN {$wpdb->posts} topic ON quiz.post_parent = topic.ID
             WHERE quiz.post_type = 'tutor_quiz'
             AND topic.post_parent = %d
             AND quiz.post_status = 'publish'
             AND topic.post_status = 'publish'",
            $course_id
        ));
        $stats['total_quizzes'] = intval($total_quizzes);

        // Ortalama rating
        if (function_exists('tutor_utils')) {
            $rating = tutor_utils()->get_course_rating($course_id);
            $stats['average_rating'] = $rating->rating_avg ?? 0;
            $stats['rating_count'] = $rating->rating_count ?? 0;
        }

        // Kurs tamamlanma oranı (eğer mevcut ise)
        $completion_rate = get_post_meta($course_id, '_course_completion_rate', true);
        if ($completion_rate) {
            $stats['completion_rate'] = $completion_rate;
        }

        return $stats;
    }

    /**
     * Kurs müfredatını al
     */
    private function get_course_curriculum($course_id) {
        global $wpdb;

        $curriculum = array();

        // Kurs konularını al (topics)
        $topics = $wpdb->get_results($wpdb->prepare(
            "SELECT ID, post_title, menu_order
             FROM {$wpdb->posts}
             WHERE post_type = 'topics'
             AND post_parent = %d
             AND post_status = 'publish'
             ORDER BY menu_order ASC",
            $course_id
        ));

        foreach ($topics as $topic) {
            $topic_data = array(
                'id' => $topic->ID,
                'title' => $topic->post_title,
                'lessons' => array(),
                'quizzes' => array()
            );

            // Bu konuya ait dersleri al
            $lessons = $wpdb->get_results($wpdb->prepare(
                "SELECT ID, post_title
                 FROM {$wpdb->posts}
                 WHERE post_type = 'lesson'
                 AND post_parent = %d
                 AND post_status = 'publish'
                 ORDER BY menu_order ASC",
                $topic->ID
            ));

            foreach ($lessons as $lesson) {
                $topic_data['lessons'][] = array(
                    'id' => $lesson->ID,
                    'title' => $lesson->post_title
                );
            }

            // Bu konuya ait quizleri al
            $quizzes = $wpdb->get_results($wpdb->prepare(
                "SELECT ID, post_title
                 FROM {$wpdb->posts}
                 WHERE post_type = 'tutor_quiz'
                 AND post_parent = %d
                 AND post_status = 'publish'
                 ORDER BY menu_order ASC",
                $topic->ID
            ));

            foreach ($quizzes as $quiz) {
                $topic_data['quizzes'][] = array(
                    'id' => $quiz->ID,
                    'title' => $quiz->post_title
                );
            }

            $curriculum[] = $topic_data;
        }

        return $curriculum;
    }

    /**
     * Kurs eğitmen bilgilerini al
     */
    private function get_course_instructor_info($course_id) {
        // Kurs yazarını al (eğitmen)
        $course = get_post($course_id);
        if (!$course) {
            return false;
        }

        $instructor_id = $course->post_author;
        $instructor = get_userdata($instructor_id);

        if (!$instructor) {
            return false;
        }

        $instructor_info = array(
            'id' => $instructor_id,
            'name' => $instructor->display_name,
            'email' => $instructor->user_email,
            'profile_url' => get_author_posts_url($instructor_id),
            'avatar' => get_avatar_url($instructor_id, array('size' => 80)),
            'bio' => get_user_meta($instructor_id, 'description', true),
            'website' => get_user_meta($instructor_id, 'user_url', true),
            'social_links' => array()
        );

        // Tutor LMS eğitmen profil bilgileri
        if (function_exists('tutor_utils')) {
            $tutor_profile = tutor_utils()->get_tutor_user($instructor_id);
            if ($tutor_profile) {
                // Tutor LMS'den ek bilgiler al
                $instructor_info['tutor_profile'] = true;
                $instructor_info['job_title'] = get_user_meta($instructor_id, '_tutor_profile_job_title', true);
                $instructor_info['phone'] = get_user_meta($instructor_id, '_tutor_profile_phone', true);

                // Sosyal medya linkleri
                $social_links = array(
                    'facebook' => get_user_meta($instructor_id, '_tutor_profile_facebook', true),
                    'twitter' => get_user_meta($instructor_id, '_tutor_profile_twitter', true),
                    'linkedin' => get_user_meta($instructor_id, '_tutor_profile_linkedin', true),
                    'website' => get_user_meta($instructor_id, '_tutor_profile_website', true),
                    'github' => get_user_meta($instructor_id, '_tutor_profile_github', true)
                );

                $instructor_info['social_links'] = array_filter($social_links);
            }
        }

        // Eğitmenin toplam kurs sayısı
        global $wpdb;
        $total_courses = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->posts}
             WHERE post_type = 'courses'
             AND post_author = %d
             AND post_status = 'publish'",
            $instructor_id
        ));
        $instructor_info['total_courses'] = intval($total_courses);

        // Eğitmenin toplam öğrenci sayısı
        $total_students = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT te.post_author)
             FROM {$wpdb->posts} c
             INNER JOIN {$wpdb->posts} te ON c.ID = te.post_parent
             WHERE c.post_type = 'courses'
             AND c.post_author = %d
             AND c.post_status = 'publish'
             AND te.post_type = 'tutor_enrolled'
             AND te.post_status = 'completed'",
            $instructor_id
        ));
        $instructor_info['total_students'] = intval($total_students);

        // Eğitmenin ortalama puanı
        if (function_exists('tutor_utils')) {
            $instructor_rating = tutor_utils()->get_instructor_rating($instructor_id);
            $instructor_info['average_rating'] = $instructor_rating->rating_avg ?? 0;
            $instructor_info['rating_count'] = $instructor_rating->rating_count ?? 0;
        }

        return $instructor_info;
    }

    /**
     * Sosyal medya platformu için ikon al
     */
    private function get_social_icon($platform) {
        $icons = array(
            'facebook' => 'facebook-alt',
            'twitter' => 'twitter',
            'linkedin' => 'linkedin',
            'website' => 'admin-site-alt3',
            'github' => 'editor-code',
            'instagram' => 'camera',
            'youtube' => 'video-alt3'
        );

        return isset($icons[$platform]) ? $icons[$platform] : 'admin-links';
    }

    /**
     * Kurs bilgileri template'ini render et
     */
    private function render_course_info_template($course, $course_meta, $course_stats, $course_curriculum) {
        ?>
        <div class="woo-pruduct-course-info">
            <!-- Kurs Genel Bilgileri -->
            <div class="course-overview">
                <h3><?php _e('Kurs Hakkında', 'woo-pruduct'); ?></h3>
                <div class="course-description">
                    <?php echo wpautop($course->post_content); ?>
                </div>
            </div>

            <!-- Kurs Meta Bilgileri -->
            <?php if (!empty($course_meta)) : ?>
                <div class="course-meta-info">
                    <h3><?php _e('Kurs Detayları', 'woo-pruduct'); ?></h3>
                    <div class="meta-grid">
                        <?php if (isset($course_meta['level'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Seviye:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['level']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($course_meta['duration'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Süre:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['duration']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($course_meta['language'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Dil:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['language']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($course_meta['total_students'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Kayıtlı Öğrenci:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['total_students']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($course_meta['last_updated'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Son Güncelleme:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['last_updated']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($course_meta['max_students'])) : ?>
                            <div class="meta-item">
                                <span class="meta-label"><?php _e('Maksimum Öğrenci:', 'woo-pruduct'); ?></span>
                                <span class="meta-value"><?php echo esc_html($course_meta['max_students']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Eğitmen Bilgileri -->
            <?php if (isset($course_meta['instructor']) && !empty($course_meta['instructor'])) :
                $instructor = $course_meta['instructor']; ?>
                <div class="course-instructor-info">
                    <h3><?php _e('Eğitmen', 'woo-pruduct'); ?></h3>
                    <div class="instructor-card">
                        <div class="instructor-avatar">
                            <img src="<?php echo esc_url($instructor['avatar']); ?>"
                                 alt="<?php echo esc_attr($instructor['name']); ?>"
                                 class="instructor-photo">
                        </div>

                        <div class="instructor-details">
                            <div class="instructor-header">
                                <h4 class="instructor-name">
                                    <a href="<?php echo esc_url($instructor['profile_url']); ?>" target="_blank">
                                        <?php echo esc_html($instructor['name']); ?>
                                    </a>
                                </h4>
                                <?php if (!empty($instructor['job_title'])) : ?>
                                    <p class="instructor-title"><?php echo esc_html($instructor['job_title']); ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="instructor-stats">
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo esc_html($instructor['total_courses']); ?></span>
                                    <span class="stat-label"><?php _e('Kurs', 'woo-pruduct'); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?php echo esc_html($instructor['total_students']); ?></span>
                                    <span class="stat-label"><?php _e('Öğrenci', 'woo-pruduct'); ?></span>
                                </div>
                                <?php if (isset($instructor['average_rating']) && $instructor['average_rating'] > 0) : ?>
                                    <div class="stat-item">
                                        <span class="stat-number"><?php echo number_format($instructor['average_rating'], 1); ?></span>
                                        <span class="stat-label"><?php _e('Puan', 'woo-pruduct'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if (!empty($instructor['bio'])) : ?>
                                <div class="instructor-bio">
                                    <p><?php echo wp_trim_words(esc_html($instructor['bio']), 30, '...'); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($instructor['social_links'])) : ?>
                                <div class="instructor-social">
                                    <?php foreach ($instructor['social_links'] as $platform => $url) : ?>
                                        <?php if (!empty($url)) : ?>
                                            <a href="<?php echo esc_url($url); ?>" target="_blank" class="social-link social-<?php echo esc_attr($platform); ?>">
                                                <span class="dashicons dashicons-<?php echo $this->get_social_icon($platform); ?>"></span>
                                            </a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>



            <!-- Kurs Müfredatı -->
            <?php if (!empty($course_curriculum)) : ?>
                <div class="course-curriculum">
                    <h3>
                        <?php _e('Kurs Müfredatı', 'woo-pruduct'); ?>
                        <span class="curriculum-summary">
                            (<?php echo esc_html($course_stats['total_lessons']); ?> <?php _e('ders', 'woo-pruduct'); ?>,
                             <?php echo esc_html($course_stats['total_quizzes']); ?> <?php _e('sınav', 'woo-pruduct'); ?>)
                        </span>
                    </h3>
                    <div class="curriculum-accordion">
                        <?php foreach ($course_curriculum as $index => $topic) : ?>
                            <div class="curriculum-topic">
                                <div class="topic-header" data-topic-id="<?php echo esc_attr($index); ?>">
                                    <h4 class="topic-title">
                                        <?php echo esc_html($topic['title']); ?>
                                        <span class="topic-count">
                                            (<?php echo count($topic['lessons']) + count($topic['quizzes']); ?> <?php _e('öğe', 'woo-pruduct'); ?>)
                                        </span>
                                    </h4>
                                    <span class="accordion-toggle">
                                        <span class="dashicons dashicons-arrow-down-alt2"></span>
                                    </span>
                                </div>

                                <div class="topic-content" id="topic-content-<?php echo esc_attr($index); ?>">
                                    <?php if (!empty($topic['lessons'])) : ?>
                                        <ul class="topic-lessons">
                                            <?php foreach ($topic['lessons'] as $lesson) : ?>
                                                <li class="lesson-item">
                                                    <span class="dashicons dashicons-video-alt3"></span>
                                                    <span class="item-title"><?php echo esc_html($lesson['title']); ?></span>
                                                    <span class="item-type"><?php _e('Ders', 'woo-pruduct'); ?></span>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>

                                    <?php if (!empty($topic['quizzes'])) : ?>
                                        <ul class="topic-quizzes">
                                            <?php foreach ($topic['quizzes'] as $quiz) : ?>
                                                <li class="quiz-item">
                                                    <span class="dashicons dashicons-clipboard"></span>
                                                    <span class="item-title"><?php echo esc_html($quiz['title']); ?></span>
                                                    <span class="item-type"><?php _e('Quiz', 'woo-pruduct'); ?></span>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Kurs Gereksinimleri -->
            <?php if (isset($course_meta['requirements']) && !empty($course_meta['requirements'])) : ?>
                <div class="course-requirements">
                    <h3><?php _e('Kurs Gereksinimleri', 'woo-pruduct'); ?></h3>
                    <div class="requirements-content">
                        <?php echo wpautop($course_meta['requirements']); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Hedef Kitle -->
            <?php if (isset($course_meta['target_audience']) && !empty($course_meta['target_audience'])) : ?>
                <div class="course-target-audience">
                    <h3><?php _e('Bu Kurs Kimlere Uygun?', 'woo-pruduct'); ?></h3>
                    <div class="target-audience-content">
                        <?php echo wpautop($course_meta['target_audience']); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Kurs Faydaları -->
            <?php if (isset($course_meta['benefits']) && !empty($course_meta['benefits'])) : ?>
                <div class="course-benefits">
                    <h3><?php _e('Bu Kurstan Neler Öğreneceksiniz?', 'woo-pruduct'); ?></h3>
                    <div class="benefits-content">
                        <?php echo wpautop($course_meta['benefits']); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .woo-pruduct-course-info {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6;
        }

        .woo-pruduct-course-info h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 25px;
        }

        .curriculum-summary {
            font-size: 14px;
            color: #666;
            font-weight: normal;
            margin-left: 10px;
        }

        /* Eğitmen Bilgileri */
        .instructor-card {
            display: flex;
            gap: 25px;
            background: #fff;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .instructor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .instructor-avatar {
            flex-shrink: 0;
            position: relative;
        }

        .instructor-photo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .instructor-details {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .instructor-header {
            margin-bottom: 10px;
        }

        .instructor-name {
            margin: 0 0 8px 0;
            font-size: 22px;
            font-weight: 700;
        }

        .instructor-name a {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .instructor-name a:hover {
            color: #667eea;
        }

        .instructor-title {
            margin: 0;
            color: #7f8c8d;
            font-size: 15px;
            font-weight: 500;
        }

        .instructor-stats {
            display: inline-flex;
            gap: 12px;
            align-items: flex-end;
        }

        .instructor-stats .stat-item {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 8px 12px;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            white-space: nowrap;
        }

        .instructor-stats .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .instructor-stats .stat-item:nth-child(1) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .instructor-stats .stat-item:nth-child(2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .instructor-stats .stat-item:nth-child(3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .instructor-stats .stat-item:nth-child(1) .stat-number,
        .instructor-stats .stat-item:nth-child(1) .stat-label,
        .instructor-stats .stat-item:nth-child(2) .stat-number,
        .instructor-stats .stat-item:nth-child(2) .stat-label,
        .instructor-stats .stat-item:nth-child(3) .stat-number,
        .instructor-stats .stat-item:nth-child(3) .stat-label {
            color: #fff;
        }

        .instructor-stats .stat-number {
            font-size: 16px;
            font-weight: 700;
            margin-right: 2px;
        }

        .instructor-stats .stat-label {
            font-size: 12px;
            font-weight: 500;
        }

        .instructor-bio {
            margin-bottom: 15px;
        }

        .instructor-bio p {
            margin: 0;
            color: #555;
            line-height: 1.5;
        }

        .instructor-social {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #f0f0f0;
            border-radius: 50%;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: #0073aa;
            color: #fff;
            text-decoration: none;
        }

        .social-link .dashicons {
            font-size: 16px;
        }



        .course-overview,
        .course-meta-info,
        .course-instructor-info,
        .course-curriculum,
        .course-requirements,
        .course-target-audience,
        .course-benefits {
            margin-bottom: 35px;
            padding: 30px;
            background: #f9f9f9;
            border-radius: 16px;
            border: 1px solid #e8e8e8;
            position: relative;
        }

        .meta-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            position: relative;
            padding: 20px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            overflow: hidden;
        }

        .meta-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .meta-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .meta-item:nth-child(1)::before { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); }
        .meta-item:nth-child(2)::before { background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%); }
        .meta-item:nth-child(3)::before { background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%); }
        .meta-item:nth-child(4)::before { background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%); }
        .meta-item:nth-child(5)::before { background: linear-gradient(90deg, #fa709a 0%, #fee140 100%); }
        .meta-item:nth-child(6)::before { background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%); }

        .meta-label {
            display: block;
            font-weight: 600;
            color: #555;
            font-size: 14px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .meta-value {
            display: block;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 700;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .meta-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .meta-item {
                padding: 16px;
            }

            .woo-pruduct-course-info h3 {
                font-size: 18px;
            }

            .course-overview,
            .course-meta-info,
            .course-instructor-info,
            .course-curriculum,
            .course-requirements,
            .course-target-audience,
            .course-benefits {
                padding: 20px;
                margin-bottom: 25px;
            }
        }

        @media (max-width: 480px) {
            .meta-grid {
                gap: 12px;
            }

            .meta-item {
                padding: 14px;
            }

            .meta-label {
                font-size: 12px;
            }

            .meta-value {
                font-size: 14px;
            }
        }



        .curriculum-accordion {
            border: 1px solid #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            background: #fff;
        }

        .curriculum-topic {
            border-bottom: 1px solid #ddd;
        }

        .curriculum-topic:last-child {
            border-bottom: none;
        }

        .topic-header {
            background: #fff;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
            user-select: none;
            border-bottom: 1px solid #e9ecef;
        }

        .topic-header:hover {
            background: #f8f9fa;
        }

        .topic-header.active {
            background: #f8f9fa;
            color: #333;
        }

        .topic-title {
            margin: 0;
            font-size: 16px;
            display: flex;
            align-items: center;
            flex: 1;
        }



        .topic-count {
            margin-left: 10px;
            font-size: 14px;
            opacity: 0.8;
            font-weight: normal;
        }

        .accordion-toggle {
            transition: transform 0.3s ease;
        }

        .accordion-toggle .dashicons {
            font-size: 20px;
        }

        .topic-header.active .accordion-toggle {
            transform: rotate(180deg);
        }

        .topic-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #fff;
        }

        .topic-content.active {
            max-height: 1000px; /* Yeterince büyük bir değer */
        }

        .topic-lessons,
        .topic-quizzes {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .lesson-item,
        .quiz-item {
            padding: 15px 25px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.2s ease;
        }

        .lesson-item:hover,
        .quiz-item:hover {
            background: #f8f9fa;
        }

        .lesson-item:last-child,
        .quiz-item:last-child {
            border-bottom: none;
        }

        .lesson-item .dashicons,
        .quiz-item .dashicons {
            margin-right: 12px;
            color: #0073aa;
            font-size: 18px;
            flex-shrink: 0;
        }

        .item-title {
            flex: 1;
            font-weight: 500;
        }

        .item-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 10px;
        }

        .quiz-item .dashicons {
            color: #0073aa;
        }

        .quiz-item .item-type {
            background: #e3f2fd;
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .meta-grid {
                grid-template-columns: 1fr;
            }

            .instructor-card {
                flex-direction: column;
                text-align: center;
                gap: 20px;
                padding: 25px;
            }

            .instructor-photo {
                width: 80px;
                height: 80px;
                margin: 0 auto;
            }

            .instructor-stats {
                justify-content: center;
                gap: 8px;
                margin-top: 15px;
            }

            .instructor-stats .stat-item {
                padding: 6px 10px;
            }

            .instructor-stats .stat-number {
                font-size: 14px;
            }

            .instructor-stats .stat-label {
                font-size: 11px;
            }

            .instructor-social {
                justify-content: center;
            }
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Accordion işlevselliği
            $('.topic-header').on('click', function() {
                const $header = $(this);
                const $content = $header.next('.topic-content');
                const $accordion = $header.closest('.curriculum-accordion');

                // Diğer açık olan konuları kapat
                $accordion.find('.topic-header.active').not($header).each(function() {
                    $(this).removeClass('active');
                    $(this).next('.topic-content').removeClass('active');
                });

                // Mevcut konuyu aç/kapat
                $header.toggleClass('active');
                $content.toggleClass('active');

                // Smooth scroll etkisi için
                if ($header.hasClass('active')) {
                    setTimeout(function() {
                        $('html, body').animate({
                            scrollTop: $header.offset().top - 100
                        }, 300);
                    }, 150);
                }
            });

            // İlk konuyu varsayılan olarak aç
            $('.curriculum-accordion .topic-header:first').addClass('active');
            $('.curriculum-accordion .topic-content:first').addClass('active');

            // Klavye erişilebilirliği
            $('.topic-header').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });

            // ARIA attributes ekle
            $('.topic-header').each(function(index) {
                const $header = $(this);
                const $content = $header.next('.topic-content');
                const headerId = 'topic-header-' + index;
                const contentId = 'topic-content-' + index;

                $header.attr({
                    'id': headerId,
                    'role': 'button',
                    'tabindex': '0',
                    'aria-expanded': $header.hasClass('active') ? 'true' : 'false',
                    'aria-controls': contentId
                });

                $content.attr({
                    'id': contentId,
                    'role': 'region',
                    'aria-labelledby': headerId
                });
            });

            // Accordion durumu değiştiğinde ARIA güncelle
            $('.topic-header').on('click', function() {
                setTimeout(function() {
                    $('.topic-header').each(function() {
                        const isActive = $(this).hasClass('active');
                        $(this).attr('aria-expanded', isActive ? 'true' : 'false');
                    });
                }, 50);
            });
        });
        </script>
        <?php
    }

    /**
     * Plugin deaktivasyonu
     */
    public function deactivate() {
        // Geçici verileri temizle
        delete_transient('woo_pruduct_scan_results');

        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Eklentiyi başlat
WooPruduct::get_instance();

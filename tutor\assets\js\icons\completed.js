"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[496],{52199:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM8.03154 12.4023C8.05241 12.4527 8.08255 12.4988 8.12039 12.5382L10.955 15.3623C10.9924 15.4059 11.0387 15.441 11.0909 15.4651C11.2025 15.5112 11.3278 15.5112 11.4393 15.4651C11.4915 15.441 11.5378 15.4059 11.5752 15.3623L16.5946 10.3691C16.6324 10.3297 16.6626 10.2837 16.6834 10.2332C16.7251 10.1208 16.7251 9.99718 16.6834 9.88476C16.663 9.8341 16.6328 9.78793 16.5946 9.74887L15.9639 9.13561C15.927 9.09321 15.8815 9.05917 15.8305 9.03574C15.7794 9.0123 15.7239 9.00002 15.6677 8.99971C15.6079 8.99898 15.5485 9.01085 15.4935 9.03456C15.4405 9.05894 15.3926 9.09324 15.3524 9.13561L11.2686 13.2194L9.36609 11.3134C9.3248 11.2708 9.27571 11.2365 9.22148 11.2123C9.16662 11.1882 9.10717 11.1763 9.04726 11.1775C8.99197 11.1777 8.93735 11.1896 8.88697 11.2123C8.83465 11.2354 8.78819 11.2699 8.75108 11.3134L8.12039 11.9179C8.08218 11.957 8.05199 12.0032 8.03154 12.0538C7.98991 12.1663 7.98991 12.2899 8.03154 12.4023Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
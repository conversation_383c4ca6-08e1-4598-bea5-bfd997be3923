"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[5777],{17276:(a,l,t)=>{t.r(l);t.d(l,{default:()=>c});const c={icon:'<g clip-path="url(#a)"><path d="M8.111 3c.075 0 .149.014.219.04a.59.59 0 0 1 .204.115c.**************.143.205l1.231 2.5 2.785.41c.*************.225.08a.853.853 0 0 1 .175.148.691.691 0 0 1 .098.205c.*************.031.208a.484.484 0 0 1-.049.219.713.713 0 0 1-.13.18l-2.027 1.975.479 2.784a.55.55 0 0 1 0 .224.66.66 0 0 1-.************* 0 0 1-.15.165.613.613 0 0 1-.429.11.526.526 0 0 1-.204-.075L8.11 11.4l-2.506 1.298a.525.525 0 0 1-.205.075.614.614 0 0 1-.43-.11.53.53 0 0 1-.148-.165.552.552 0 0 1-.086-.43l.478-2.783L3.18 7.32a.744.744 0 0 1-.125-.19A.625.625 0 0 1 3 6.91c0-.073.01-.145.03-.215a.638.638 0 0 1 .274-.354.546.546 0 0 1 .226-.073l2.784-.408 1.247-2.514a.55.55 0 0 1 .346-.306A.628.628 0 0 1 8.11 3Z" fill="currentColor"/></g><defs><clipPath id="a"><path fill="#fff" transform="translate(2.667 2.667)" d="M0 0h10.667v10.667H0z"/></clipPath></defs>',viewBox:"0 0 16 16"}}}]);
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2346],{64917:(a,l,Z)=>{Z.r(l);Z.d(l,{default:()=>h});const h={icon:'<g clip-path="url(#a)"><path d="M14.4 18v.6a.6.6 0 0 0 .6-.6h-.6Zm-4.8 0H9a.6.6 0 0 0 .6.6V18Zm.6 2.4a.6.6 0 0 0 .6.6h2.4a.6.6 0 1 0 0-1.2h-2.4a.6.6 0 0 0-.6.6Zm-2.904-9.72-.012.084 *************-.084-1.189-.168ZM12 6.6a4.75 4.75 0 0 0-4.704 4.08l1.19.168A3.55 3.55 0 0 1 12 7.8V6.6Zm4.704 4.08A4.75 4.75 0 0 0 12 6.6v1.2a3.552 3.552 0 0 1 3.516 3.048l1.188-.168Zm.012.084-.012-.084-************.084 1.19-.168Zm-.948 3.587a4.745 4.745 0 0 0 .948-3.587l-1.188.17a3.542 3.542 0 0 1-.708 2.682l.948.735ZM13.8 16.08V18H15v-1.919l-1.2-.001Zm.6 1.32H9.6v1.2h4.8v-1.2Zm-4.2.6v-1.919H9V18h1.2Zm-2.916-7.236a4.741 4.741 0 0 0 .948 3.587l.95-.735a3.544 3.544 0 0 1-.71-2.684l-1.188-.168ZM10.2 16.08c0-.988-.526-1.828-1.02-2.465l-.948.736c.46.594.768 1.16.768 1.729h1.2Zm4.62-2.465c-.496.639-1.02 1.477-1.02 2.465H15c0-.569.308-1.135.768-1.73l-.948-.735ZM12 3a.6.6 0 0 0-.6.6v1.2a.6.6 0 1 0 1.2 0V3.6A.6.6 0 0 0 12 3Zm-9 9a.6.6 0 0 0 .6.6h1.2a.6.6 0 1 0 0-1.2H3.6a.6.6 0 0 0-.6.6Zm15.6 0a.6.6 0 0 0 .6.6h1.2a.6.6 0 1 0 0-1.2h-1.2a.6.6 0 0 0-.6.6Zm-12-4.2a.6.6 0 0 0 0-.85L5.65 6a.6.6 0 0 0-.85.85l.95.95a.6.6 0 0 0 .85 0Zm10.8 0a.6.6 0 0 0 .85 0l.95-.95a.6.6 0 0 0-.85-.85l-.95.95a.6.6 0 0 0 0 .85Z" fill="currentColor"/></g><defs><clipPath id="a"><path fill="currentColor" transform="translate(3 3)" d="M0 0h18v18H0z"/></clipPath></defs>',viewBox:"0 0 24 24"}}}]);
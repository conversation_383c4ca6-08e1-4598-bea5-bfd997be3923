"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[1993],{82708:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M5.33333 3.55551C5.08787 3.55551 4.88889 3.7545 4.88889 3.99996V12C4.88889 12.2454 5.08787 12.4444 5.33333 12.4444H10.6667C10.9121 12.4444 11.1111 12.2454 11.1111 12V6.66663H9.33333C8.59695 6.66663 8 6.06967 8 5.33329V3.55551H5.33333ZM8.88889 4.18405L10.4826 5.77774H9.33333C9.08787 5.77774 8.88889 5.57875 8.88889 5.33329V4.18405ZM4 3.99996C4 3.26358 4.59695 2.66663 5.33333 2.66663H8.44444C8.56232 2.66663 8.67536 2.71345 8.75871 2.7968L11.8698 5.90791C11.9532 5.99126 12 6.10431 12 6.22218V12C12 12.7363 11.403 13.3333 10.6667 13.3333H5.33333C4.59695 13.3333 4 12.7363 4 12V3.99996ZM6.22222 8.88885C6.22222 8.64339 6.42121 8.4444 6.66667 8.4444H8C8.24546 8.4444 8.44444 8.64339 8.44444 8.88885C8.44444 9.13431 8.24546 9.33329 8 9.33329H6.66667C6.42121 9.33329 6.22222 9.13431 6.22222 8.88885ZM6.22222 10.6666C6.22222 10.4212 6.42121 10.2222 6.66667 10.2222H9.33333C9.57879 10.2222 9.77778 10.4212 9.77778 10.6666C9.77778 10.9121 9.57879 11.1111 9.33333 11.1111H6.66667C6.42121 11.1111 6.22222 10.9121 6.22222 10.6666Z" fill="currentColor"/>',viewBox:"0 0 16 16"}}}]);
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[3870],{24685:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M14 11C12.3431 11 11 12.3431 11 14V34C11 35.6569 12.3431 37 14 37H34C35.6569 37 37 35.6569 37 34V14C37 12.3431 35.6569 11 34 11H14ZM13 14C13 13.4477 13.4477 13 14 13H34C34.5523 13 35 13.4477 35 14V34C35 34.5523 34.5523 35 34 35H14C13.4477 35 13 34.5523 13 34V14ZM29.8137 20.5812C30.1347 20.1318 30.0307 19.5073 29.5812 19.1863C29.1318 18.8653 28.5073 18.9693 28.1863 19.4188L22.6224 27.2082L19.7071 24.2929C19.3166 23.9024 18.6834 23.9024 18.2929 24.2929C17.9024 24.6834 17.9024 25.3166 18.2929 25.7071L22.0429 29.4571C22.2507 29.665 22.5395 29.7708 22.8325 29.7466C23.1254 29.7223 23.3929 29.5704 23.5637 29.3312L29.8137 20.5812Z" fill="currentColor"/>',viewBox:"0 0 48 48"}}}]);
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[7667],{7443:(e,r,t)=>{t.d(r,{d:()=>d});var i=t(41594);var n=t.n(i);function a(e,r){return u(e)||s(e,r)||l(e,r)||o()}function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,r){if(e){if("string"==typeof e)return c(e,r);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(e,r):void 0}}function c(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,i=Array(r);t<r;t++)i[t]=e[t];return i}function s(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,n,a,o,l=[],c=!0,s=!1;try{if(a=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;c=!1}else for(;!(c=(i=a.call(t)).done)&&(l.push(i.value),l.length!==r);c=!0);}catch(e){s=!0,n=e}finally{try{if(!c&&null!=t["return"]&&(o=t["return"](),Object(o)!==o))return}finally{if(s)throw n}}return l}}function u(e){if(Array.isArray(e))return e}var d=function e(r){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:300;var n=(0,i.useState)(r),o=a(n,2),l=o[0],c=o[1];(0,i.useEffect)((function(){var e=setTimeout((function(){c(r)}),t);return function(){clearTimeout(e)}}),[r,t]);return l}},82856:(e,r,t)=>{t.r(r);t.d(r,{default:()=>b});var i=t(942);var n=t(17437);var a=1116;function o(e){var r=e.children;return(0,n.Y)("div",{css:c.wrapper},r)}const l=o;var c={wrapper:(0,n.AH)("max-width:",a,"px;margin:0 auto;height:100%;width:100%;"+(true?"":0),true?"":0)};var s=t(97011);var u=t(52457);var d=t(62246);var p=t(98880);var g=t(7443);var h=t(82179);var v=["chevronUp","penToSquare","chevronDown","eye","spinner","sortMinor","times","timesAlt","arrowLeftAlt","storeImage","storeEye","storeEyeSlash","alert","coupon","seo","circledPlus","chevronLeft","chevronRight","listOption","colorOption","duplicate","preview","threeDots","threeDotsVertical","plusSquare","plusSquareBrand","minusSquare","markCircle","lock","lockStroke","drop","marksTotal","crossCircle","sortBy","anglesRight","settingsGeneral","settingsProduct","settingsShipping","settingsPrivacy","settingsEmail","settingsIntegration","settingsAdvance","settingsTax","boxPrice","airDelivery","weightBox","discountType","freeShippingType","saleType","buyGetType","calendar","pauseCircle","questionCircle","barLegend","tickMark","tickMarkGreen","profile","giftCard","inactive","active","completed","visited","save","cross","info","infoFill","settings","contentDrip","addImage","calendarLine","clock","back","edit","search","delete","plus","crown","dragVertical","copyPaste","download","downloadColorize","lesson","quiz","assignment","bars","googleMeet","zoom","googleMeetColorize","zoomColorize","upload","note","attach","report","linkExternal","quizTrueFalse","quizMultiChoice","quizEssay","quizFillInTheBlanks","quizShortAnswer","quizImageMatching","quizImageAnswer","quizOrdering","checkMark","lineCross","bulb","check","checkFilled","checkFilledWhite","arrowsIn","plusMinus","textFieldExpand","checkSquare","checkSquareFilled","removeImage","imagePreview","imagePreviewLine","arrowsOut","dot","monitorPlay","video","videoCamera","arrowLeft","tagOutline","timesThin","landscape","landscapeFilled","portrait","portraitFilled","outlineNone","doc","currency","iso","dwg","mp3","mp4","csv","rtf","svg","css","xls","pdf","xml","png","compress","zip","ppt","jpg","exe","psd","javascript","ai","jsonFile","avi","fla","dbf","html","txt","file","spreadsheet","text","document","videoFile","audio","image","archive","buddyPress","dollar-recurring","threeDotsVerticalDouble","magicAi","magicAiColorize","magicAiPlaceholder","seeds","styleNone","bulbLine","imagePlus","magicWand","eraser","magicEraser","reload","undo","redo","magicVariation","copy","droip","star","book","tryAgain","user","refresh","warning","droipColorized","elementorColorized","gutenbergColorized","materialCheck","export","import","importColorized","crownRounded","crownRoundedSmall","quizH5p","interactiveQuiz","change","handCoin","update","pen","rotate","receiptPercent","vimeo","youtube","shortcode","coding","diviColorized","addons","priceTag","crownOutlined","certificate","settingsError","uploadFile","attachmentLine","primeCheckCircle"];var f=t(41594);var m=t(49785);function y(){return y=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},y.apply(null,arguments)}var w=function e(){var r=(0,h.p)({defaultValues:{search:""}});var t=(0,g.d)(r.watch("search"));var a=(0,f.useMemo)((function(){if(!t){return v}return v.filter((function(e){return new RegExp(t,"i").test(e)}))}),[t]);return(0,n.Y)(l,null,(0,n.Y)("div",{css:k.container},(0,n.Y)(m.xI,{control:r.control,name:"search",render:function e(r){return(0,n.Y)(s.A,y({},r,{placeholder:"Search icons..."}))}}),(0,n.Y)("div",{css:k.wrapper},(0,n.Y)(p.A,{each:a},(function(e,r){return(0,n.Y)("div",null,(0,n.Y)(i.A,{key:r,name:e,width:60,height:60}),(0,n.Y)("span",null,e))})))))};const b=w;var k={container:(0,n.AH)("display:flex;flex-direction:column;gap:",u.YK[32],";margin-block:60px;"+(true?"":0),true?"":0),wrapper:(0,n.AH)("display:grid;grid-template-columns:repeat(4, 1fr);gap:",u.YK[32],";&>div{display:flex;flex-direction:column;gap:",u.YK[8],";",d.I.caption(),";color:",u.I6.text.subdued,";align-items:center;svg{color:",u.I6.icon["default"],";}}"+(true?"":0),true?"":0)}}}]);
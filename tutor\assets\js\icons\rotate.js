"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[2172],{46479:(C,t,e)=>{e.r(t);e.d(t,{default:()=>u});const u={icon:'<path d="M21.9642 12.1013L18.9391 15.1703C18.7637 15.3456 18.5445 15.4333 18.3034 15.4333C18.0622 15.4333 17.843 15.3456 17.6677 15.1703L14.6425 12.1013C14.3137 11.7505 14.3137 11.2025 14.6425 10.8518C14.9933 10.523 15.5413 10.523 15.892 10.8518L17.58 12.5616C17.3608 9.38304 14.7521 6.8621 11.5736 6.8621C8.24152 6.8621 5.5452 9.62418 5.5452 13.0001C5.5452 16.3759 8.24152 19.138 11.5736 19.138C12.8011 19.138 13.9849 18.7653 14.9713 18.0639C15.3659 17.7789 15.914 17.8885 16.1989 18.2831C16.4839 18.6777 16.3743 19.2257 15.9797 19.5107C14.6864 20.4094 13.1519 20.8917 11.5736 20.8917C7.27698 20.8917 3.7915 17.3624 3.7915 13.0001C3.7915 8.63772 7.27698 5.1084 11.5736 5.1084C15.6071 5.1084 18.9391 8.24314 19.3118 12.2547L20.6928 10.8518C21.0435 10.501 21.5916 10.501 21.9423 10.8518C22.2931 11.2025 22.2931 11.7505 21.9642 12.1013Z" fill="currentColor"/>',viewBox:"0 0 26 26"}}}]);
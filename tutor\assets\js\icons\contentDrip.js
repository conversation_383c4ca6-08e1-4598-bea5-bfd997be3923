"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[3489],{95780:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M5.36625 11.3333H6C6.36819 11.3333 6.66667 11.6318 6.66667 12C6.66667 12.3682 6.36819 12.6667 6 12.6667H5.36625C5.67896 15.8163 8.18372 18.321 11.3333 18.6337V18C11.3333 17.6318 11.6318 17.3333 12 17.3333C12.3682 17.3333 12.6667 17.6318 12.6667 18V18.6337C15.8163 18.321 18.321 15.8163 18.6337 12.6667H18C17.6318 12.6667 17.3333 12.3682 17.3333 12C17.3333 11.6318 17.6318 11.3333 18 11.3333H18.6337C18.321 8.18372 15.8163 5.67896 12.6667 5.36625V6C12.6667 6.36819 12.3682 6.66667 12 6.66667C11.6318 6.66667 11.3333 6.36819 11.3333 6V5.36625C8.18372 5.67896 5.67896 8.18372 5.36625 11.3333ZM4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12ZM12 8C12.3682 8 12.6667 8.29848 12.6667 8.66667V11.6796L15.7498 14.1461C16.0373 14.3761 16.0839 14.7956 15.8539 15.0831C15.6239 15.3706 15.2044 15.4173 14.9169 15.1872L11.5835 12.5206C11.4254 12.3941 11.3333 12.2025 11.3333 12V8.66667C11.3333 8.29848 11.6318 8 12 8Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
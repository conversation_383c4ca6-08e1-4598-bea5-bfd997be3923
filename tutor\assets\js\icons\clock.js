"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[4457],{64008:(C,e,l)=>{l.r(e);l.d(e,{default:()=>t});const t={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M7.33301 15.9997C7.33301 11.2132 11.2132 7.33301 15.9997 7.33301C20.7861 7.33301 24.6663 11.2132 24.6663 15.9997C24.6663 20.7861 20.7861 24.6663 15.9997 24.6663C11.2132 24.6663 7.33301 20.7861 7.33301 15.9997ZM15.9997 5.33301C10.1086 5.33301 5.33301 10.1086 5.33301 15.9997C5.33301 21.8907 10.1086 26.6663 15.9997 26.6663C21.8907 26.6663 26.6663 21.8907 26.6663 15.9997C26.6663 10.1086 21.8907 5.33301 15.9997 5.33301ZM16.9997 10.1996C16.9997 9.64734 16.552 9.19963 15.9997 9.19963C15.4474 9.19963 14.9997 9.64734 14.9997 10.1996V15.9996C14.9997 16.3784 15.2137 16.7247 15.5525 16.8941L19.4191 18.8274C19.9131 19.0744 20.5138 18.8741 20.7608 18.3802C21.0078 17.8862 20.8075 17.2855 20.3136 17.0385L16.9997 15.3816V10.1996Z" fill="currentColor"/>',viewBox:"0 0 32 32"}}}]);
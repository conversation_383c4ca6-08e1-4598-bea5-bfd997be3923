"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[1130],{83145:(C,t,e)=>{e.r(t);e.d(t,{default:()=>u});const u={icon:'<path d="M40.0611 10.0613L30.6205 19.5H35.9999C36.3977 19.5 36.7792 19.658 37.0605 19.9394C37.3418 20.2207 37.4999 20.6022 37.4999 21C37.4999 21.3978 37.3418 21.7794 37.0605 22.0607C36.7792 22.342 36.3977 22.5 35.9999 22.5H26.9999C26.602 22.5 26.2205 22.342 25.9392 22.0607C25.6579 21.7794 25.4999 21.3978 25.4999 21V12C25.4999 11.6022 25.6579 11.2207 25.9392 10.9394C26.2205 10.658 26.602 10.5 26.9999 10.5C27.3977 10.5 27.7792 10.658 28.0605 10.9394C28.3418 11.2207 28.4999 11.6022 28.4999 12V17.3794L37.9386 7.93876C38.2201 7.6573 38.6018 7.49918 38.9999 7.49918C39.3979 7.49918 39.7796 7.6573 40.0611 7.93876C40.3426 8.22022 40.5007 8.60196 40.5007 9.00001C40.5007 9.39806 40.3426 9.7798 40.0611 10.0613ZM20.9999 25.5H11.9999C11.602 25.5 11.2205 25.658 10.9392 25.9393C10.6579 26.2207 10.4999 26.6022 10.4999 27C10.4999 27.3978 10.6579 27.7794 10.9392 28.0607C11.2205 28.342 11.602 28.5 11.9999 28.5H17.3792L7.93861 37.9388C7.65715 38.2202 7.49902 38.602 7.49902 39C7.49902 39.3981 7.65715 39.7798 7.93861 40.0613C8.22007 40.3427 8.60181 40.5008 8.99986 40.5008C9.3979 40.5008 9.77965 40.3427 10.0611 40.0613L19.4999 30.6206V36C19.4999 36.3978 19.6579 36.7794 19.9392 37.0607C20.2205 37.342 20.602 37.5 20.9999 37.5C21.3977 37.5 21.7792 37.342 22.0605 37.0607C22.3418 36.7794 22.4999 36.3978 22.4999 36V27C22.4999 26.6022 22.3418 26.2207 22.0605 25.9393C21.7792 25.658 21.3977 25.5 20.9999 25.5Z" fill="currentColor"/>',viewBox:"0 0 48 48"}}}]);
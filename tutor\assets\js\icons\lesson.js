"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[6041],{62820:(a,e,l)=>{l.r(e);l.d(e,{default:()=>c});const c={icon:'<path fill-rule="evenodd" clip-rule="evenodd" d="M7.265 4.988a.044.044 0 0 0-.003.018v13.988c0 .***************.018a.03.03 0 0 0 .006.01h10.57a.03.03 0 0 0 .007-.01.045.045 0 0 0 .003-.018V5.006a.045.045 0 0 0-.003-.018.029.029 0 0 0-.007-.01H7.271a.03.03 0 0 0-.006.01Zm-1.503.018c0-.824.654-1.527 1.505-1.527h10.578c.851 0 1.506.703 1.506 1.527v13.988c0 .824-.655 1.527-1.506 1.527H7.267c-.851 0-1.505-.703-1.505-1.527V5.006Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.578 3.479a.75.75 0 0 1 .75.75v15.543a.75.75 0 0 1-1.5 0V4.229a.75.75 0 0 1 .75-.75ZM4.25 8.114a.75.75 0 0 1 .75-.75h3.022a.75.75 0 1 1 0 1.5H5a.75.75 0 0 1-.75-.75ZM4.25 12a.75.75 0 0 1 .75-.75h3.022a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75ZM4.25 15.886a.75.75 0 0 1 .75-.75h3.022a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
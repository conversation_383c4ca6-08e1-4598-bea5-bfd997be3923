"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[5345],{81724:(C,e,l)=>{l.r(e);l.d(e,{default:()=>r});const r={icon:'<rect width="24" height="24" rx="3" fill="transparent"/><path fill-rule="evenodd" clip-rule="evenodd" d="M6.12933 6.99397C5.92754 6.99397 5.73402 7.07413 5.59133 7.21682C5.44864 7.3595 5.36848 7.55303 5.36848 7.75482V17.8705C5.36848 18.0723 5.44864 18.2658 5.59133 18.4085C5.73402 18.5512 5.92754 18.6313 6.12933 18.6313H16.245C16.4468 18.6313 16.6403 18.5512 16.783 18.4085C16.9257 18.2658 17.0058 18.0723 17.0058 17.8705V14.0121C17.0058 13.6342 17.3122 13.3278 17.6901 13.3278C18.068 13.3278 18.3743 13.6342 18.3743 14.0121V17.8705C18.3743 18.4352 18.15 18.9768 17.7506 19.3761C17.3513 19.7755 16.8097 19.9998 16.245 19.9998H6.12933C5.5646 19.9998 5.02299 19.7755 4.62367 19.3761C4.22434 18.9768 4 18.4352 4 17.8705V7.75482C4 7.19009 4.22434 6.64848 4.62367 6.24916C5.02299 5.84983 5.5646 5.62549 6.12933 5.62549H9.98773C10.3656 5.62549 10.672 5.93183 10.672 6.30973C10.672 6.68762 10.3656 6.99397 9.98773 6.99397H6.12933Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M15.9288 4.20041C16.196 3.9332 16.6293 3.9332 16.8965 4.20041L19.7996 7.1035C20.0668 7.37071 20.0668 7.80395 19.7996 8.07116L12.5419 15.3289C12.4135 15.4572 12.2395 15.5293 12.058 15.5293H9.15494C8.77705 15.5293 8.4707 15.2229 8.4707 14.845V11.942C8.4707 11.7605 8.54279 11.5864 8.67111 11.4581L15.9288 4.20041ZM9.83918 12.2254V14.1608H11.7746L18.3481 7.58733L16.4127 5.6519L9.83918 12.2254Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
<?php
/**
 * Handle native cart logics
 *
 * @package Tu<PERSON>\Ecommerce
 * <AUTHOR>
 * @link https://themeum.com
 * @since 3.5.0
 */

namespace Tu<PERSON>\Ecommerce\Cart;

use <PERSON><PERSON>\Ecommerce\Cart\Contracts\CartInterface;
use <PERSON><PERSON>\Ecommerce\CartController;
use Tutor\Models\CartModel;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
/**
 * Class for managing native cart functions
 *
 * @since 3.5.0
 */
class NativeCart extends BaseCart implements CartInterface {

	/**
	 * Cart model
	 *
	 * @var CartModel
	 */
	private $cart_model;

	/**
	 * Initialize vars
	 *
	 * @since 3.5.0
	 */
	public function __construct() {
		parent::__construct();
		$this->cart_model = new CartModel();
	}

	/**
	 * Add to cart
	 *
	 * @since 3.5.0
	 *
	 * @param int $item_id Item id to add to cart.
	 *
	 * @return bool
	 */
	public function add( int $item_id ): bool {
		if ( $this->is_item_exists( $item_id ) ) {
			$this->cart_error = __( 'Item already exists in cart', 'tutor' );
			return false;
		}

		return (bool) $this->cart_model->add_course_to_cart( $this->user_id, $item_id );
	}

	/**
	 * Remove an item from cart
	 *
	 * @since 3.5.0
	 *
	 * @param integer $item_id Item id to add to cart.
	 *
	 * @return boolean
	 */
	public function remove( int $item_id ): bool {
		// @TODO
		return false;
	}

	/**
	 * Clear the cart entirely
	 *
	 * @since 3.5.0
	 *
	 * @return boolean
	 */
	public function clear_cart(): bool {
		// @TODO
		return false;
	}


	/**
	 * Get cart items
	 *
	 * @since 3.5.0
	 *
	 * @return array Array of objects
	 */
	public function get_cart_items(): array {
		$items      = array();
		$cart_items = $this->cart_model->get_cart_items( $this->user_id );
		if ( is_array( $cart_items ) && ! empty( $cart_items['courses']['results'] ) ) {
			foreach ( $cart_items['courses']['results'] as $cart_item ) {
				$item = (object) array(
					'id'    => $cart_item->ID,
					'title' => $cart_item->post_title,
				);

				$items[] = $item;
			}
		}

		return $items;
	}

	/**
	 * Get cart page url to view the cart
	 *
	 * @since 3.5.0
	 *
	 * @return string
	 */
	public function get_cart_url(): string {
		return CartController::get_page_url();
	}

	/**
	 * Check if item exists in cart
	 *
	 * @since 3.5.0
	 *
	 * @param int $item_id Item id.
	 *
	 * @return bool
	 */
	public function is_item_exists( int $item_id ): bool {
		return $this->cart_model->is_course_in_user_cart( $this->user_id, $item_id );
	}
}

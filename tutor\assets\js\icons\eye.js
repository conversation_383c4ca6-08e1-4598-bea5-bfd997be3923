"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[4632],{2967:(C,e,l)=>{l.r(e);l.d(e,{default:()=>r});const r={icon:'<path d="M16.0006 18.6667C16.7048 18.6667 17.3801 18.3857 17.878 17.8856C18.3758 17.3855 18.6556 16.7073 18.6556 16C18.6556 15.2928 18.3758 14.6145 17.878 14.1144C17.3801 13.6143 16.7048 13.3333 16.0006 13.3333C15.2965 13.3333 14.6212 13.6143 14.1233 14.1144C13.6254 14.6145 13.3457 15.2928 13.3457 16C13.3457 16.7073 13.6254 17.3855 14.1233 17.8856C14.6212 18.3857 15.2965 18.6667 16.0006 18.6667Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M3.33398 16C5.02517 10.5907 10.0563 6.66667 16.0007 6.66667C21.945 6.66667 26.9761 10.5907 28.6673 16C26.9761 21.4093 21.945 25.3333 16.0007 25.3333C10.0563 25.3333 5.02517 21.4093 3.33398 16ZM21.3105 16C21.3105 17.4145 20.7511 18.771 19.7553 19.7712C18.7595 20.7714 17.4089 21.3333 16.0007 21.3333C14.5924 21.3333 13.2418 20.7714 12.246 19.7712C11.2502 18.771 10.6908 17.4145 10.6908 16C10.6908 14.5855 11.2502 13.229 12.246 12.2288C13.2418 11.2286 14.5924 10.6667 16.0007 10.6667C17.4089 10.6667 18.7595 11.2286 19.7553 12.2288C20.7511 13.229 21.3105 14.5855 21.3105 16Z" fill="currentColor"/>',viewBox:"0 0 32 32"}}}]);
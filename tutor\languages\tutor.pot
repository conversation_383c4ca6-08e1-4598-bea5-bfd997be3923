# Copyright (C) 2025 Themeum
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Tutor LMS 3.6.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/tutor\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-02T05:54:46+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: tutor\n"

#. Plugin Name of the plugin
#: classes/Admin.php:124
#: classes/Admin.php:125
#: classes/Admin.php:593
#: classes/Gutenberg.php:144
msgid "Tutor LMS"
msgstr ""

#. Plugin URI of the plugin
msgid "https://tutorlms.com"
msgstr ""

#. Description of the plugin
msgid "Tutor is a complete solution for creating a Learning Management System in WordPress way. It can help you to create small to large scale online education site very conveniently. Power features like report, certificate, course preview, private file sharing make Tutor a robust plugin for any educational institutes."
msgstr ""

#. Author of the plugin
msgid "Themeum"
msgstr ""

#. Author URI of the plugin
msgid "https://themeum.com"
msgstr ""

#: classes/Addons.php:142
msgid "PHP 7.2.5 or greater is required"
msgstr ""

#: classes/Addons.php:143
msgid "SSL certificate"
msgstr ""

#: classes/Addons.php:183
#: classes/Ajax.php:86
#: classes/Ajax.php:166
#: classes/Ajax.php:503
#: classes/Ajax.php:582
#: classes/Course.php:1928
#: classes/Course.php:2162
#: classes/Instructor.php:325
#: classes/Quiz.php:358
#: classes/Quiz.php:940
#: classes/Quiz.php:1296
#: classes/Quiz.php:1446
#: classes/Quiz.php:1534
#: classes/Quiz.php:1962
#: classes/Quiz.php:1999
#: classes/Q_And_A.php:205
msgid "Access Denied"
msgstr ""

#: classes/Addons.php:235
msgid "Course Bundle"
msgstr ""

#: classes/Addons.php:236
msgid "Group multiple courses to sell together."
msgstr ""

#: classes/Addons.php:239
msgid "Subscription"
msgstr ""

#: classes/Addons.php:240
msgid "Manage subscription"
msgstr ""

#: classes/Addons.php:243
msgid "Social Login"
msgstr ""

#: classes/Addons.php:244
msgid "Let users register & login through social networks."
msgstr ""

#: classes/Addons.php:247
#: assets/js/tutor-course-builder.js:11979
msgid "Content Drip"
msgstr ""

#: classes/Addons.php:248
msgid "Unlock lessons by schedule or when students meet a specific condition."
msgstr ""

#: classes/Addons.php:251
msgid "Tutor Multi Instructors"
msgstr ""

#: classes/Addons.php:252
msgid "Collaborate and add multiple instructors to a course."
msgstr ""

#: classes/Addons.php:255
msgid "Tutor Assignments"
msgstr ""

#: classes/Addons.php:256
msgid "Assess student learning with assignments."
msgstr ""

#: classes/Addons.php:259
msgid "Tutor Course Preview"
msgstr ""

#: classes/Addons.php:260
msgid "Offer free previews of specific lessons before enrollment."
msgstr ""

#: classes/Addons.php:263
msgid "Tutor Course Attachments"
msgstr ""

#: classes/Addons.php:264
msgid "Add unlimited attachments/ private files to any Tutor course"
msgstr ""

#: classes/Addons.php:267
msgid "Tutor Google Meet Integration"
msgstr ""

#: classes/Addons.php:268
msgid "Host live classes with Google Meet, directly from your lesson page."
msgstr ""

#: classes/Addons.php:271
msgid "Tutor Report"
msgstr ""

#: classes/Addons.php:272
msgid "Check your course performance through Tutor Report stats."
msgstr ""

#: classes/Addons.php:275
#: templates/dashboard/my-profile.php:32
#: views/pages/instructors.php:123
#: views/pages/instructors.php:170
#: views/pages/students.php:88
msgid "Email"
msgstr ""

#: classes/Addons.php:276
msgid "Send automated and customized emails for various Tutor events."
msgstr ""

#: classes/Addons.php:279
msgid "Calendar"
msgstr ""

#: classes/Addons.php:280
msgid "Enable to let students view all your course events in one place."
msgstr ""

#: classes/Addons.php:283
msgid "Notifications"
msgstr ""

#: classes/Addons.php:284
msgid "Keep students and instructors notified of course events on their dashboard."
msgstr ""

#: classes/Addons.php:287
msgid "Google Classroom Integration"
msgstr ""

#: classes/Addons.php:288
msgid "Enable to integrate Tutor LMS with Google Classroom."
msgstr ""

#: classes/Addons.php:291
msgid "Tutor Zoom Integration"
msgstr ""

#: classes/Addons.php:292
msgid "Connect Tutor LMS with Zoom to host live online classes."
msgstr ""

#: classes/Addons.php:295
msgid "Quiz Export/Import"
msgstr ""

#: classes/Addons.php:296
msgid "Save time by exporting/importing quiz data with easy options."
msgstr ""

#: classes/Addons.php:299
#: assets/js/tutor-course-builder.js:11988
msgid "Enrollment"
msgstr ""

#: classes/Addons.php:300
msgid "Enable to manually enroll students in your courses."
msgstr ""

#: classes/Addons.php:303
msgid "Tutor Certificate"
msgstr ""

#: classes/Addons.php:304
msgid "Enable to award certificates upon course completion."
msgstr ""

#: classes/Addons.php:307
msgid "Gradebook"
msgstr ""

#: classes/Addons.php:308
msgid "Track student progress with a centralized gradebook."
msgstr ""

#: classes/Addons.php:311
msgid "Tutor Prerequisites"
msgstr ""

#: classes/Addons.php:312
msgid "Set course prerequisites to guide learning paths effectively."
msgstr ""

#: classes/Addons.php:315
#: assets/js/tutor-course-builder.js:11996
msgid "BuddyPress"
msgstr ""

#: classes/Addons.php:316
msgid "Boost engagement with social features through BuddyPress for Tutor LMS."
msgstr ""

#: classes/Addons.php:319
msgid "WooCommerce Subscriptions"
msgstr ""

#: classes/Addons.php:320
msgid "Capture Residual Revenue with Recurring Payments."
msgstr ""

#: classes/Addons.php:323
msgid "Paid Memberships Pro"
msgstr ""

#: classes/Addons.php:324
msgid "Boost revenue by selling course memberships."
msgstr ""

#: classes/Addons.php:327
msgid "Restrict Content Pro"
msgstr ""

#: classes/Addons.php:328
msgid "Enable to manage content access through Restrict Content Pro. "
msgstr ""

#: classes/Addons.php:331
msgid "Weglot"
msgstr ""

#: classes/Addons.php:332
msgid "Translate & manage multilingual courses for global reach."
msgstr ""

#: classes/Addons.php:335
msgid "WPML"
msgstr ""

#: classes/Addons.php:336
msgid "Create multilingual courses, lessons, dashboard and more."
msgstr ""

#: classes/Addons.php:339
#: assets/js/tutor-course-builder.js:20128
msgid "H5P"
msgstr ""

#: classes/Addons.php:340
msgid "Integrate H5P to add interactivity and engagement to your courses."
msgstr ""

#. translators: %s: version name
#: classes/Admin.php:79
msgid "You're currently using Tutor LMS %s. To ensure stability, please do not use it on a live site."
msgstr ""

#: classes/Admin.php:82
#: assets/js/tutor-admin.js:2553
#: assets/js/tutor.js:2754
#: assets/js/tutor.js:2759
msgid "Warning!"
msgstr ""

#: classes/Admin.php:108
#: assets/js/tutor-course-builder.js:7394
#: assets/js/tutor-course-builder.js:19935
#: assets/js/tutor-course-builder.js:23802
#: assets/js/tutor-course-builder.js:24246
#: assets/js/tutor-course-builder.js:25516
#: assets/js/tutor-course-builder.js:25538
#: assets/js/tutor-course-builder.js:25559
#: assets/js/tutor-course-builder.js:29117
#: assets/js/tutor-course-builder.js:29369
#: assets/js/tutor-course-builder.js:29390
#: assets/js/tutor-course-builder.js:29541
#: assets/js/tutor-import-export.js:6989
#: assets/js/tutor-import-export.js:6998
msgid "Pro"
msgstr ""

#: classes/Admin.php:134
#: classes/Course_List.php:78
#: classes/Post_types.php:101
#: templates/archive-course-init.php:132
#: templates/dashboard/announcements.php:91
#: templates/dashboard/assignments.php:40
#: templates/dashboard/elements/filters.php:26
#: templates/instructor/cover.php:39
#: templates/instructor/default.php:33
#: templates/instructor/minimal-horizontal.php:31
#: templates/instructor/minimal.php:35
#: templates/instructor/portrait-horizontal.php:36
#: templates/public-profile.php:130
#: templates/public-profile.php:185
#: assets/js/tutor-coupon.js:3470
#: assets/js/tutor-coupon.js:3589
#: assets/js/tutor-coupon.js:5395
#: assets/js/tutor-course-builder.js:34622
#: assets/js/tutor-order-details.js:10333
#: assets/js/tutor-payment-settings.js:11037
msgid "Courses"
msgstr ""

#: classes/Admin.php:137
#: classes/WhatsNew.php:39
#: classes/WhatsNew.php:46
msgid "What's New"
msgstr ""

#: classes/Admin.php:143
#: assets/js/tutor-course-builder.js:33169
msgid "Course Builder"
msgstr ""

#: classes/Admin.php:149
#: views/pages/addons.php:28
#: views/template-import/templates.php:22
#: views/template-import/templates.php:39
msgid "Themes"
msgstr ""

#: classes/Admin.php:149
#: templates/shortcode/instructor-filter.php:18
#: assets/js/tutor-addon-list.js:6572
msgid "New"
msgstr ""

#: classes/Admin.php:151
#: views/pages/course-list.php:262
#: assets/js/tutor-course-builder.js:11262
msgid "Categories"
msgstr ""

#: classes/Admin.php:153
#: classes/Post_types.php:195
#: templates/single/course/tags.php:18
#: assets/js/tutor-course-builder.js:11271
msgid "Tags"
msgstr ""

#: classes/Admin.php:155
#: classes/Students_List.php:76
#: templates/public-profile.php:137
#: views/pages/students.php:84
msgid "Students"
msgstr ""

#: classes/Admin.php:158
#: assets/js/tutor-course-builder.js:11309
msgid "Instructors"
msgstr ""

#: classes/Admin.php:159
#: classes/Withdraw_Requests_List.php:54
msgid "Withdraw Requests"
msgstr ""

#: classes/Admin.php:162
#: classes/Announcements.php:65
#: classes/Options_V2.php:1477
#: classes/Utils.php:9154
#: classes/Utils.php:9440
#: views/fragments/announcement-list.php:253
msgid "Announcements"
msgstr ""

#: classes/Admin.php:164
#: classes/Options_V2.php:1366
#: classes/Utils.php:9149
#: templates/dashboard.php:54
#: assets/js/tutor-course-builder.js:12072
msgid "Q&A"
msgstr ""

#: classes/Admin.php:164
msgid "Q&A "
msgstr ""

#: classes/Admin.php:166
#: classes/Quiz_Attempts_List.php:85
#: classes/Utils.php:9450
#: templates/dashboard.php:54
#: templates/dashboard/quiz-attempts.php:31
msgid "Quiz Attempts"
msgstr ""

#: classes/Admin.php:168
#: assets/js/tutor-addon-list.js:5255
msgid "Addons"
msgstr ""

#: classes/Admin.php:172
#: views/options/tools.php:16
#: views/pages/tools.php:14
msgid "Tools"
msgstr ""

#: classes/Admin.php:174
#: classes/Admin.php:542
#: classes/Utils.php:3045
#: templates/dashboard/notifications/profile-completion.php:22
#: templates/dashboard/settings.php:13
#: templates/dashboard/settings/reset-password.php:14
#: templates/dashboard/settings/social-profile.php:15
#: templates/dashboard/settings/withdraw-settings.php:21
#: templates/ecommerce/billing.php:14
#: views/options/settings.php:21
#: assets/js/tutor-course-builder.js:25000
#: assets/js/tutor-import-export.js:7317
#: assets/js/tutor-import-export.js:8183
msgid "Settings"
msgstr ""

#: classes/Admin.php:179
#: classes/Admin.php:537
#: views/pages/feature-promotion.php:22
#: assets/js/tutor-addon-list.js:6676
msgid "Upgrade to Pro"
msgstr ""

#: classes/Admin.php:209
#: classes/Tutor_Setup.php:110
#: ecommerce/CheckoutController.php:176
#: ecommerce/Settings.php:425
#: ecommerce/Settings.php:455
#: ecommerce/Tax.php:77
#: assets/js/tutor-admin.js:1808
#: assets/js/tutor-admin.js:1856
#: assets/js/tutor-admin.js:1905
#: assets/js/tutor-admin.js:2167
#: assets/js/tutor-admin.js:2225
#: assets/js/tutor-admin.js:2285
#: assets/js/tutor-admin.js:3113
#: assets/js/tutor-admin.js:3180
#: assets/js/tutor-admin.js:3183
#: assets/js/tutor-front.js:148
#: assets/js/tutor-front.js:288
#: assets/js/tutor-front.js:314
#: assets/js/tutor-front.js:901
#: assets/js/tutor-front.js:2433
#: assets/js/tutor-front.js:2965
#: assets/js/tutor-front.js:3138
#: assets/js/tutor-front.js:3209
#: assets/js/tutor-front.js:3530
#: assets/js/tutor-setup.js:1344
#: assets/js/tutor-setup.js:1372
#: assets/js/tutor.js:2373
#: assets/js/tutor.js:2401
#: assets/js/tutor.js:3146
msgid "Success"
msgstr ""

#: classes/Admin.php:308
#: classes/Tools_V2.php:318
#: views/options/template/tutor_pages.php:15
msgid "Tutor Pages"
msgstr ""

#: classes/Admin.php:309
#: classes/Tools_V2.php:103
#: templates/dashboard/purchase_history.php:115
#: templates/dashboard/withdraw.php:233
#: views/options/template/status.php:13
#: views/options/template/tutor_pages.php:33
#: views/pages/course-list.php:91
#: views/pages/ecommerce/coupon-list.php:104
#: views/pages/ecommerce/order-list.php:105
#: views/pages/instructors.php:137
#: views/pages/instructors.php:193
#: views/pages/instructors.php:212
#: views/pages/tools/tutor_pages.php:20
#: views/pages/withdraw_requests.php:100
#: views/pages/withdraw_requests.php:250
#: views/qna/contexts.php:20
msgid "Status"
msgstr ""

#: classes/Admin.php:457
#: includes/tutor-template-functions.php:1614
#: templates/permission-denied.php:25
#: templates/single/lesson/required-enroll.php:15
msgid "Permission Denied"
msgstr ""

#: classes/Admin.php:563
msgid "Documentation"
msgstr ""

#: classes/Admin.php:568
msgid "Get Support"
msgstr ""

#. translators: %s: plugin name
#: classes/Admin.php:592
msgid "If you like %1$s please leave us a %2$s rating. A huge thanks in advance!"
msgstr ""

#: classes/Admin.php:712
#: classes/Admin.php:715
msgid "Edit with Course Builder"
msgstr ""

#: classes/Ajax.php:270
msgid "Rating placed successfully!"
msgstr ""

#: classes/Ajax.php:295
msgid "Permissioned Denied!"
msgstr ""

#: classes/Ajax.php:339
msgid "Course added to wish list"
msgstr ""

#: classes/Ajax.php:346
msgid "Course removed from wish list"
msgstr ""

#: classes/Ajax.php:409
msgid "Nonce verification failed"
msgstr ""

#: classes/Ajax.php:447
msgid "Username is required."
msgstr ""

#: classes/Ajax.php:527
msgid "Course name required"
msgstr ""

#: classes/Ajax.php:532
msgid "Announcement title required"
msgstr ""

#: classes/Ajax.php:536
#: classes/Ajax.php:541
msgid "Announcement summary required"
msgstr ""

#: classes/Ajax.php:549
msgid "All fields required!"
msgstr ""

#: classes/Ajax.php:563
msgid "Announcement created successfully"
msgstr ""

#: classes/Ajax.php:563
msgid "Announcement updated successfully"
msgstr ""

#: classes/Ajax.php:567
#: assets/js/tutor-admin.js:2337
#: assets/js/tutor-front.js:787
#: assets/js/tutor.js:258
#: assets/js/tutor.js:410
#: assets/js/tutor.js:414
#: assets/js/tutor.js:865
#: assets/js/tutor.js:875
#: assets/js/tutor.js:2587
msgid "Something Went Wrong!"
msgstr ""

#: classes/Ajax.php:587
msgid "Announcement deleted successfully"
msgstr ""

#: classes/Ajax.php:590
msgid "Announcement delete failed"
msgstr ""

#: classes/Ajax.php:605
msgid "Video ID is required"
msgstr ""

#: classes/Ajax.php:619
msgid "Fetched duration successfully"
msgstr ""

#: classes/Ajax.php:628
msgid "Failed to fetch duration"
msgstr ""

#: classes/Backend_Page_Trait.php:41
msgid "Bulk Action"
msgstr ""

#: classes/Backend_Page_Trait.php:54
#: templates/dashboard/dashboard.php:312
#: templates/loop/enrolled-course-progress.php:23
#: templates/single-content-loader.php:96
#: templates/single/course/course-entry-box.php:98
msgid "Complete"
msgstr ""

#: classes/Backend_Page_Trait.php:67
#: templates/dashboard/announcements/create.php:64
#: templates/dashboard/my-courses.php:51
#: templates/dashboard/my-courses.php:225
#: views/fragments/announcement-list.php:91
#: views/pages/course-list.php:179
#: views/pages/course-list.php:191
#: views/pages/ecommerce/order-list.php:56
#: assets/js/tutor-course-builder.js:30927
msgid "Publish"
msgstr ""

#: classes/Backend_Page_Trait.php:80
#: classes/Course_List.php:164
#: includes/translate-text.php:149
#: templates/dashboard/my-courses.php:59
#: views/pages/course-list.php:182
#: views/pages/ecommerce/order-list.php:59
msgid "Draft"
msgstr ""

#: classes/Backend_Page_Trait.php:93
#: includes/translate-text.php:101
#: includes/translate-text.php:109
#: includes/translate-text.php:113
#: includes/translate-text.php:117
#: templates/dashboard/purchase_history.php:221
msgid "On Hold"
msgstr ""

#: classes/Backend_Page_Trait.php:106
#: classes/Course_List.php:170
#: classes/Instructors_List.php:110
#: classes/Quiz_Attempts_List.php:239
#: classes/Utils.php:3189
#: classes/Withdraw_Requests_List.php:89
#: includes/translate-text.php:41
#: models/UserModel.php:95
#: templates/dashboard/dashboard.php:153
#: templates/dashboard/my-courses.php:55
#: templates/dashboard/purchase_history.php:233
#: templates/single/assignment/content.php:494
#: templates/single/course/reviews-loop.php:44
#: views/pages/course-list.php:180
#: views/pages/ecommerce/order-list.php:57
#: views/pages/instructors.php:54
#: views/quiz/attempt-details.php:294
#: views/quiz/attempt-details.php:726
#: views/quiz/attempt-table.php:156
msgid "Pending"
msgstr ""

#: classes/Backend_Page_Trait.php:119
#: includes/translate-text.php:77
#: templates/dashboard/purchase_history.php:217
#: assets/js/tutor-front.js:3608
msgid "Processing"
msgstr ""

#: classes/Backend_Page_Trait.php:132
#: views/pages/course-list.php:453
#: views/pages/ecommerce/coupon-list.php:181
msgid "Delete Permanently"
msgstr ""

#: classes/Backend_Page_Trait.php:145
#: templates/dashboard/announcements/create.php:65
#: templates/dashboard/announcements/details.php:41
#: templates/dashboard/announcements/update.php:64
#: templates/dashboard/my-courses.php:326
#: templates/dashboard/reviews/given-reviews.php:119
#: templates/dashboard/withdraw.php:191
#: templates/modal/confirm.php:55
#: views/elements/bulk-confirm-popup.php:33
#: views/elements/common-confirm-popup.php:65
#: views/fragments/announcement-list.php:88
#: views/fragments/announcement-list.php:164
#: views/fragments/announcement-list.php:217
#: views/modal/review.php:39
#: views/options/template/common/modal-confirm.php:38
#: views/pages/instructors.php:366
#: views/pages/tools/manage-tokens.php:130
#: views/pages/tools/manage-tokens.php:195
#: views/pages/withdraw_requests.php:372
#: views/pages/withdraw_requests.php:430
#: views/qna/qna-new.php:30
#: views/qna/qna-table.php:182
#: assets/js/tutor-addon-list.js:6317
#: assets/js/tutor-addon-list.js:6380
#: assets/js/tutor-coupon.js:4274
#: assets/js/tutor-coupon.js:7206
#: assets/js/tutor-coupon.js:11984
#: assets/js/tutor-course-builder.js:4690
#: assets/js/tutor-course-builder.js:5409
#: assets/js/tutor-course-builder.js:7431
#: assets/js/tutor-course-builder.js:8029
#: assets/js/tutor-course-builder.js:10156
#: assets/js/tutor-course-builder.js:10431
#: assets/js/tutor-course-builder.js:12227
#: assets/js/tutor-course-builder.js:13758
#: assets/js/tutor-course-builder.js:14800
#: assets/js/tutor-course-builder.js:17370
#: assets/js/tutor-course-builder.js:18334
#: assets/js/tutor-course-builder.js:19413
#: assets/js/tutor-course-builder.js:19665
#: assets/js/tutor-course-builder.js:20369
#: assets/js/tutor-course-builder.js:20569
#: assets/js/tutor-course-builder.js:21100
#: assets/js/tutor-course-builder.js:21539
#: assets/js/tutor-course-builder.js:22157
#: assets/js/tutor-course-builder.js:22792
#: assets/js/tutor-course-builder.js:25023
#: assets/js/tutor-course-builder.js:25100
#: assets/js/tutor-course-builder.js:26000
#: assets/js/tutor-course-builder.js:26024
#: assets/js/tutor-course-builder.js:26532
#: assets/js/tutor-course-builder.js:28782
#: assets/js/tutor-course-builder.js:29008
#: assets/js/tutor-course-builder.js:32867
#: assets/js/tutor-course-builder.js:32885
#: assets/js/tutor-course-builder.js:37988
#: assets/js/tutor-import-export.js:6716
#: assets/js/tutor-import-export.js:8873
#: assets/js/tutor-order-details.js:8818
#: assets/js/tutor-order-details.js:9400
#: assets/js/tutor-order-details.js:9506
#: assets/js/tutor-order-details.js:9830
#: assets/js/tutor-payment-settings.js:5805
#: assets/js/tutor-payment-settings.js:10661
#: assets/js/tutor-payment-settings.js:12533
#: assets/js/tutor-tax-settings.js:7621
#: assets/js/tutor-tax-settings.js:7970
#: assets/js/tutor-tax-settings.js:10135
#: assets/js/tutor-tax-settings.js:10636
msgid "Cancel"
msgstr ""

#: classes/Backend_Page_Trait.php:158
#: classes/Instructors_List.php:104
#: views/pages/withdraw_requests.php:261
msgid "Approve"
msgstr ""

#: classes/Backend_Page_Trait.php:171
#: classes/Instructors_List.php:116
msgid "Block"
msgstr ""

#: classes/Backend_Page_Trait.php:183
#: classes/Backend_Page_Trait.php:266
#: classes/Course_List.php:188
#: includes/translate-text.php:145
#: models/CouponModel.php:205
#: models/OrderModel.php:270
#: templates/dashboard/dashboard.php:154
#: views/pages/course-list.php:181
#: views/pages/ecommerce/order-list.php:58
#: assets/js/tutor-coupon.js:7614
#: assets/js/tutor-order-details.js:10624
msgid "Trash"
msgstr ""

#: classes/Backend_Page_Trait.php:196
#: views/pages/instructors.php:62
#: views/pages/withdraw_requests.php:264
msgid "Reject"
msgstr ""

#: classes/Backend_Page_Trait.php:210
#: includes/translate-text.php:157
#: models/CouponModel.php:203
#: assets/js/tutor-coupon.js:7608
msgid "Active"
msgstr ""

#: classes/Backend_Page_Trait.php:224
#: includes/translate-text.php:161
#: models/CouponModel.php:204
#: assets/js/tutor-coupon.js:7611
msgid "Inactive"
msgstr ""

#: classes/Backend_Page_Trait.php:238
#: assets/js/tutor-order-details.js:9983
msgid "Mark as paid"
msgstr ""

#: classes/Backend_Page_Trait.php:252
msgid "Mark as unpaid"
msgstr ""

#: classes/Course.php:330
msgid "Invalid password"
msgstr ""

#: classes/Course.php:442
msgid "Video source is required"
msgstr ""

#: classes/Course.php:444
msgid "Invalid video source"
msgstr ""

#: classes/Course.php:476
msgid "The enrollment start date cannot be earlier than the course start date"
msgstr ""

#: classes/Course.php:495
msgid "Invalid course categories"
msgstr ""

#: classes/Course.php:503
msgid "Invalid course tags"
msgstr ""

#: classes/Course.php:557
#: classes/Course.php:595
msgid "Invalid price type"
msgstr ""

#: classes/Course.php:570
#: classes/Course.php:610
msgid "Product already linked with course"
msgstr ""

#: classes/Course.php:614
msgid "Invalid product"
msgstr ""

#: classes/Course.php:622
msgid "Price is required"
msgstr ""

#: classes/Course.php:848
#: classes/Post_types.php:98
#: templates/dashboard.php:144
#: views/pages/course-list.php:51
msgid "New Course"
msgstr ""

#: classes/Course.php:871
msgid "Draft course created"
msgstr ""

#: classes/Course.php:936
msgid "Course list retrieved successfully!"
msgstr ""

#: classes/Course.php:986
#: classes/Course.php:1082
#: classes/Course.php:1224
#: classes/Course.php:1255
msgid "Invalid input"
msgstr ""

#: classes/Course.php:1011
msgid "Course created successfully"
msgstr ""

#: classes/Course.php:1130
msgid "Course updated successfully."
msgstr ""

#: classes/Course.php:1157
msgid "Builder unlinked successfully."
msgstr ""

#: classes/Course.php:1220
#: classes/Course.php:1251
msgid "Invalid course id"
msgstr ""

#: classes/Course.php:1230
msgid "Course contents fetched successfully"
msgstr ""

#: classes/Course.php:1338
msgid "Data retrieved successfully!"
msgstr ""

#: classes/Course.php:1373
msgid "You cannot edit this course because it is in the Trash. Please restore it and try again"
msgstr ""

#: classes/Course.php:1595
msgid "Products retrieved successfully!"
msgstr ""

#: classes/Course.php:1625
msgid "One product can not be added to multiple course!"
msgstr ""

#: classes/Course.php:1637
msgid "Product not found"
msgstr ""

#: classes/Course.php:1656
#: classes/Frontend.php:107
msgid "Access Denied!"
msgstr ""

#: classes/Course.php:1687
#: templates/loop/course-price-edd.php:80
#: templates/loop/course-price-tutor.php:84
#: templates/loop/course-price-woocommerce.php:77
#: templates/loop/course-price.php:79
msgid "Fully Booked"
msgstr ""

#: classes/Course.php:1911
msgid "Topic title is required!"
msgstr ""

#: classes/Course.php:1913
#: classes/Lesson.php:279
#: ecommerce/BillingController.php:168
msgid "Invalid inputs"
msgstr ""

#: classes/Course.php:1951
msgid "Topic updated successfully!"
msgstr ""

#: classes/Course.php:1956
msgid "Topic created successfully!"
msgstr ""

#: classes/Course.php:2002
msgid "Topic deleted successfully!"
msgstr ""

#: classes/Course.php:2024
msgid "Please Sign In first"
msgstr ""

#: classes/Course.php:2074
#: classes/Lesson.php:430
msgid "Please Sign-In"
msgstr ""

#: classes/Course.php:2171
msgid "Only main instructor can delete this course"
msgstr ""

#: classes/Course.php:2192
msgid "Course has been trashed successfully "
msgstr ""

#: classes/Course.php:2382
#: classes/Options_V2.php:1240
#: assets/js/tutor-course-builder.js:12045
msgid "Difficulty Level"
msgstr ""

#: classes/Course.php:2383
#: classes/Options_V2.php:1374
#: classes/Options_V2.php:1382
#: classes/Options_V2.php:1390
#: classes/Options_V2.php:1406
#: classes/Options_V2.php:1414
#: classes/Options_V2.php:1422
#: classes/Options_V2.php:1430
#: classes/Options_V2.php:1438
#: classes/Options_V2.php:1446
#: classes/Options_V2.php:1454
#: classes/Options_V2.php:1462
#: classes/Options_V2.php:1478
#: classes/Options_V2.php:1486
#: classes/User.php:458
msgid "Enable"
msgstr ""

#: classes/Course.php:2386
#: assets/js/tutor-course-builder.js:12047
msgid "Course difficulty level"
msgstr ""

#: classes/Course.php:2716
msgid "Complete all lessons to mark this course as complete"
msgstr ""

#: classes/Course.php:2781
msgid "quiz"
msgid_plural "quizzes"
msgstr[0] ""
msgstr[1] ""

#: classes/Course.php:2782
msgid "assignment"
msgid_plural "assignments"
msgstr[0] ""
msgstr[1] ""

#. translators: %1$s: number of quiz/assignment pass required; %2$s: quiz/assignment string
#: classes/Course.php:2786
#: classes/Course.php:2791
msgid "You have to pass %1$s %2$s to complete this course."
msgstr ""

#. translators: %1$s: number of quiz pass required; %2$s: quiz string; %3$s: number of assignment pass required; %4$s: assignment string
#: classes/Course.php:2796
msgid "You have to pass %1$s %2$s and %3$s %4$s to complete this course."
msgstr ""

#: classes/Course.php:2893
msgid "Invalid Course ID or Access Denied."
msgstr ""

#: classes/Course.php:2939
msgid "This course is password protected"
msgstr ""

#: classes/Course.php:2943
msgid "Enrollment successfully done!"
msgstr ""

#: classes/Course.php:2945
msgid "Enrollment failed, please try again!"
msgstr ""

#: classes/Course.php:2948
msgid "Invalid course ID"
msgstr ""

#: classes/Course_List.php:146
#: classes/Instructors_List.php:98
#: classes/Quiz_Attempts_List.php:221
#: classes/Withdraw_Requests_List.php:77
#: ecommerce/CouponController.php:453
#: ecommerce/OrderController.php:772
#: includes/translate-text.php:25
#: templates/dashboard/announcements.php:94
#: templates/dashboard/assignments.php:43
#: templates/dashboard/elements/filters.php:30
#: assets/js/tutor-course-builder.js:10844
msgid "All"
msgstr ""

#: classes/Course_List.php:152
msgid "Mine"
msgstr ""

#: classes/Course_List.php:158
#: templates/dashboard/dashboard.php:152
msgid "Published"
msgstr ""

#: classes/Course_List.php:176
msgid "Scheduled"
msgstr ""

#: classes/Course_List.php:182
#: classes/Options_V2.php:1301
#: classes/Options_V2.php:1326
#: includes/translate-text.php:153
#: views/pages/course-list.php:183
#: views/pages/ecommerce/order-list.php:60
#: assets/js/tutor-addon-list.js:231
#: assets/js/tutor-coupon.js:593
#: assets/js/tutor-course-builder.js:3580
#: assets/js/tutor-course-builder.js:11142
#: assets/js/tutor-import-export.js:234
#: assets/js/tutor-order-details.js:233
#: assets/js/tutor-payment-settings.js:234
#: assets/js/tutor-tax-settings.js:233
msgid "Private"
msgstr ""

#: classes/Course_List.php:306
#: ecommerce/OrderController.php:886
msgid "Please select appropriate action"
msgstr ""

#: classes/Course_List.php:317
msgid "Could not delete selected courses"
msgstr ""

#: classes/Course_List.php:333
msgid "Could not update course status"
msgstr ""

#: classes/Course_List.php:373
msgid "Course trashed successfully"
msgstr ""

#: classes/Course_List.php:419
msgid "Course has been deleted "
msgstr ""

#: classes/Course_List.php:421
msgid "Course delete failed "
msgstr ""

#: classes/Course_Widget.php:35
msgctxt "widget title"
msgid "Tutor Course"
msgstr ""

#: classes/Course_Widget.php:37
msgctxt "widget description"
msgid "Display courses wherever widget support is available."
msgstr ""

#: classes/Course_Widget.php:144
msgid "New title"
msgstr ""

#: classes/Course_Widget.php:154
#: views/pages/course-list.php:258
#: assets/js/tutor-coupon.js:5970
#: assets/js/tutor-coupon.js:7639
#: assets/js/tutor-course-builder.js:12332
#: assets/js/tutor-course-builder.js:13257
#: assets/js/tutor-course-builder.js:18356
#: assets/js/tutor-course-builder.js:19339
#: assets/js/tutor-import-export.js:8471
#: assets/js/tutor-order-details.js:7490
#: assets/js/tutor-payment-settings.js:5323
#: assets/js/tutor-payment-settings.js:9426
#: assets/js/tutor-payment-settings.js:12478
#: assets/js/tutor-tax-settings.js:8899
msgid "Title"
msgstr ""

#: classes/Course_Widget.php:161
#: views/options/template/tutor_pages.php:27
#: views/pages/ecommerce/order-list.php:88
#: views/pages/tools/tutor_pages.php:18
msgid "ID"
msgstr ""

#: classes/Course_Widget.php:165
msgid "Place single course id or comma (,) separated course ids"
msgstr ""

#: classes/Course_Widget.php:170
msgid "Exclude IDS:"
msgstr ""

#: classes/Course_Widget.php:173
msgid "Place comma (,) separated courses ids which you like to exclude from the query"
msgstr ""

#: classes/Course_Widget.php:179
#: classes/Options_V2.php:1238
#: templates/course-filter/filters.php:55
#: templates/shortcode/instructor-filter.php:57
#: views/elements/filters.php:120
#: views/pages/course-list.php:98
#: assets/js/tutor-coupon.js:3444
#: assets/js/tutor-coupon.js:3445
msgid "Category"
msgstr ""

#: classes/Course_Widget.php:183
msgid "Place comma (,) separated category ids"
msgstr ""

#: classes/Course_Widget.php:189
msgid "OrderBy"
msgstr ""

#: classes/Course_Widget.php:204
#: assets/js/tutor-course-builder.js:34668
#: assets/js/tutor-payment-settings.js:11083
msgid "Order"
msgstr ""

#: classes/Course_Widget.php:214
msgid "Count:"
msgstr ""

#: classes/Course_Widget.php:217
msgid "Total results you like to show"
msgstr ""

#: classes/FormHandler.php:62
msgid "Enter a username or email address."
msgstr ""

#: classes/FormHandler.php:84
#: classes/FormHandler.php:89
msgid "Invalid username or email."
msgstr ""

#: classes/FormHandler.php:101
msgid "Password reset is not allowed for this user"
msgstr ""

#: classes/FormHandler.php:128
msgid "Check your E-Mail"
msgstr ""

#: classes/FormHandler.php:129
msgid "We've sent an email to this account's email address. Click the link in the email to reset your password."
msgstr ""

#: classes/FormHandler.php:130
msgid " If you don't see the email, check other places it might be, like your junk, spam, social, promotion or others folders."
msgstr ""

#: classes/FormHandler.php:164
msgid "This key is invalid or has already been used. Please reset your password again if needed."
msgstr ""

#: classes/FormHandler.php:170
msgid "Please enter your password."
msgstr ""

#: classes/FormHandler.php:175
msgid "Passwords do not match."
msgstr ""

#. translators: %s: site name
#: classes/FormHandler.php:221
msgid "Password Reset Request for %s"
msgstr ""

#: classes/Instructor.php:92
#: classes/Instructor.php:247
#: classes/Student.php:65
msgid "First name field is required"
msgstr ""

#: classes/Instructor.php:93
#: classes/Instructor.php:248
#: classes/Student.php:66
msgid "Last name field is required"
msgstr ""

#: classes/Instructor.php:94
#: classes/Instructor.php:249
#: classes/Student.php:67
msgid "E-Mail field is required"
msgstr ""

#: classes/Instructor.php:95
#: classes/Instructor.php:250
#: classes/Student.php:68
msgid "User Name field is required"
msgstr ""

#: classes/Instructor.php:96
#: classes/Instructor.php:251
#: classes/Student.php:69
msgid "Password field is required"
msgstr ""

#: classes/Instructor.php:97
#: classes/Student.php:70
msgid "Password Confirmation field is required"
msgstr ""

#: classes/Instructor.php:119
#: classes/Instructor.php:264
#: classes/Student.php:90
msgid "Valid E-Mail is required"
msgstr ""

#: classes/Instructor.php:122
#: classes/Instructor.php:252
#: classes/Instructor.php:267
#: classes/Student.php:93
#: views/pages/instructors.php:341
msgid "Your passwords should match each other. Please recheck."
msgstr ""

#: classes/Instructor.php:214
msgid "Already applied for instructor"
msgstr ""

#: classes/Instructor.php:222
#: classes/Template.php:259
msgid "Permission denied"
msgstr ""

#: classes/Instructor.php:308
msgid "Instructor has been added successfully"
msgstr ""

#: classes/Instructors_List.php:74
#: classes/Options_V2.php:761
#: classes/Tutor_Setup.php:338
#: classes/Tutor_Setup.php:471
#: classes/Tutor_Setup.php:540
#: classes/Utils.php:9415
#: templates/dashboard/question-answer.php:63
msgid "Instructor"
msgstr ""

#: classes/Lesson.php:215
msgid "Lesson data fetched successfully"
msgstr ""

#: classes/Lesson.php:333
msgid "Lesson updated successfully"
msgstr ""

#: classes/Lesson.php:338
msgid "Lesson created successfully"
msgstr ""

#: classes/Lesson.php:366
#: classes/Options_V2.php:881
#: assets/js/tutor-course-builder.js:25321
#: assets/js/tutor-course-builder.js:25332
#: assets/js/tutor-course-builder.js:26184
msgid "Lesson"
msgstr ""

#: classes/Lesson.php:369
#: templates/dashboard/assignments.php:34
#: assets/js/tutor-course-builder.js:25431
#: assets/js/tutor-course-builder.js:25442
#: assets/js/tutor-course-builder.js:25454
#: assets/js/tutor-course-builder.js:26186
msgid "Assignment"
msgstr ""

#. translators: %s refers to the name of the content being deleted
#: classes/Lesson.php:376
msgid "%s deleted successfully"
msgstr ""

#: classes/Lesson.php:576
#: classes/Utils.php:1823
#: templates/single/lesson/comments-loop.php:30
#: templates/single/lesson/comments-loop.php:63
msgid " ago"
msgstr ""

#: classes/Options_V2.php:439
msgid "Invalid json file"
msgstr ""

#: classes/Options_V2.php:443
msgid "Data not found or invalid"
msgstr ""

#: classes/Options_V2.php:455
msgid "Settings not found"
msgstr ""

#: classes/Options_V2.php:466
msgid "Settings imported successfully!"
msgstr ""

#: classes/Options_V2.php:488
msgid "Total share percentage must be 100% or less"
msgstr ""

#: classes/Options_V2.php:565
#: assets/js/tutor-admin.js:2537
msgid "Settings Saved"
msgstr ""

#: classes/Options_V2.php:690
#: classes/TutorEDD.php:66
#: assets/js/tutor-course-builder.js:11971
msgid "General"
msgstr ""

#: classes/Options_V2.php:692
msgid "General Settings"
msgstr ""

#: classes/Options_V2.php:704
#: classes/Utils.php:7499
msgid "Dashboard Page"
msgstr ""

#: classes/Options_V2.php:707
msgid "This page will be used for student and instructor dashboard"
msgstr ""

#: classes/Options_V2.php:720
msgid "Terms and Conditions Page"
msgstr ""

#: classes/Options_V2.php:723
msgid "This page will be used as the Terms and Conditions page"
msgstr ""

#: classes/Options_V2.php:729
#: templates/ecommerce/checkout.php:154
msgid "Privacy Policy"
msgstr ""

#: classes/Options_V2.php:732
msgid "Choose the page for privacy policy."
msgstr ""

#: classes/Options_V2.php:738
msgid "Others"
msgstr ""

#: classes/Options_V2.php:745
msgid "Enable Marketplace"
msgstr ""

#: classes/Options_V2.php:748
msgid "Allow multiple instructors to sell their courses."
msgstr ""

#: classes/Options_V2.php:754
msgid "Pagination"
msgstr ""

#: classes/Options_V2.php:756
msgid "Set the number of rows to be displayed per page"
msgstr ""

#: classes/Options_V2.php:768
msgid "Become an Instructor Button"
msgstr ""

#: classes/Options_V2.php:771
msgid "Enable the option to display this button on the student dashboard."
msgstr ""

#: classes/Options_V2.php:776
msgid "Allow Instructors to Publish Courses"
msgstr ""

#: classes/Options_V2.php:779
msgid "Enable instructors to publish the course directly. If disabled, admins will be able to review course content before publishing."
msgstr ""

#: classes/Options_V2.php:784
msgid "Allow Instructors to Trash Courses"
msgstr ""

#: classes/Options_V2.php:787
msgid "Enable this setting to allow instructors to delete courses."
msgstr ""

#: classes/Options_V2.php:794
#: classes/Options_V2.php:1198
#: classes/Options_V2.php:1697
#: classes/Tutor_Setup.php:436
#: classes/Tutor_Setup.php:537
#: ecommerce/EmailController.php:458
#: templates/dashboard/announcements/details.php:31
#: templates/dashboard/assignments.php:108
#: templates/dashboard/assignments/review.php:49
#: templates/dashboard/assignments/submitted.php:43
#: templates/instructor/cover.php:39
#: templates/instructor/default.php:33
#: templates/instructor/minimal-horizontal.php:31
#: templates/instructor/minimal.php:35
#: templates/instructor/portrait-horizontal.php:36
#: templates/public-profile.php:130
#: views/elements/filters.php:99
#: views/elements/search-filter.php:40
#: views/fragments/announcement-list.php:144
#: views/fragments/announcement-list.php:301
#: views/qna/qna-table.php:102
#: views/quiz/contexts.php:18
#: views/quiz/header-context/backend-dashboard-students-attempts.php:25
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:19
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:21
#: assets/js/tutor-import-export.js:8430
msgid "Course"
msgid_plural "Courses"
msgstr[0] ""
msgstr[1] ""

#: classes/Options_V2.php:796
msgid "Course Settings"
msgstr ""

#: classes/Options_V2.php:808
msgid "Course Visibility"
msgstr ""

#: classes/Options_V2.php:811
msgid "Students must be logged in to view course"
msgstr ""

#: classes/Options_V2.php:816
msgid "Course Content Access"
msgstr ""

#: classes/Options_V2.php:819
msgid "Allow instructors and admins to view the course content without enrolling"
msgstr ""

#: classes/Options_V2.php:824
msgid "Content Summary"
msgstr ""

#: classes/Options_V2.php:826
msgid "Enabling this feature will show a course content summary on the Course Details page."
msgstr ""

#: classes/Options_V2.php:831
msgid "Spotlight Mode"
msgstr ""

#: classes/Options_V2.php:834
msgid "This will hide the header and the footer and enable spotlight (full screen) mode when students view lessons."
msgstr ""

#: classes/Options_V2.php:839
msgid "Auto Complete Course on All Lesson Completion"
msgstr ""

#: classes/Options_V2.php:842
msgid "If enabled, an Enrolled Course will be automatically completed if all its Lessons, Quizzes, and Assignments are already completed by the Student"
msgstr ""

#: classes/Options_V2.php:847
msgid "Course Completion Process"
msgstr ""

#: classes/Options_V2.php:852
msgid "Flexible"
msgstr ""

#: classes/Options_V2.php:853
msgid "Students can complete courses anytime in the Flexible mode"
msgstr ""

#: classes/Options_V2.php:856
msgid "Strict"
msgstr ""

#: classes/Options_V2.php:857
msgid "Students have to complete, pass all the lessons and quizzes (if any) to mark a course as complete."
msgstr ""

#: classes/Options_V2.php:860
msgid "Choose when a user can click on the <strong>“Complete Course”</strong> button"
msgstr ""

#: classes/Options_V2.php:865
msgid "Course Retake"
msgstr ""

#: classes/Options_V2.php:868
msgid "Enabling this feature will allow students to reset course progress and start over."
msgstr ""

#: classes/Options_V2.php:873
msgid "Publish Course Review on Admin's Approval"
msgstr ""

#: classes/Options_V2.php:876
msgid "Enable to publish/re-publish Course Review after the approval of Site Admin"
msgstr ""

#: classes/Options_V2.php:888
msgid "WP Editor for Lesson"
msgstr ""

#: classes/Options_V2.php:891
msgid "Enable classic editor to edit lesson."
msgstr ""

#: classes/Options_V2.php:896
msgid "Automatically Load Next Course Content."
msgstr ""

#: classes/Options_V2.php:899
msgid "Enable this feature to automatically load the next course content after the current one is finished."
msgstr ""

#: classes/Options_V2.php:904
msgid "Enable Lesson Comment"
msgstr ""

#: classes/Options_V2.php:907
msgid "Enable this feature to allow students to post comments on lessons."
msgstr ""

#: classes/Options_V2.php:912
#: templates/single/quiz/top.php:43
#: views/quiz/header-context/backend-dashboard-students-attempts.php:28
#: views/quiz/header-context/course-single-previous-attempts.php:23
#: assets/js/tutor-course-builder.js:25350
#: assets/js/tutor-course-builder.js:25362
#: assets/js/tutor-course-builder.js:26185
msgid "Quiz"
msgstr ""

#: classes/Options_V2.php:919
msgid "When time expires"
msgstr ""

#: classes/Options_V2.php:924
msgid "Auto Submit"
msgstr ""

#: classes/Options_V2.php:925
msgid "The current quiz answers are submitted automatically."
msgstr ""

#: classes/Options_V2.php:928
msgid "Auto Abandon"
msgstr ""

#: classes/Options_V2.php:929
msgid "Attempts must be submitted before time expires, otherwise they will not be counted."
msgstr ""

#: classes/Options_V2.php:932
msgid "Choose which action to follow when the quiz time expires."
msgstr ""

#: classes/Options_V2.php:937
msgid "Correct Answer Display Time (When Reveal Mode is enabled)"
msgstr ""

#: classes/Options_V2.php:939
msgid "Put the answer display time in seconds"
msgstr ""

#: classes/Options_V2.php:945
msgid "Default Quiz Attempt limit (When Retry Mode is enabled)"
msgstr ""

#: classes/Options_V2.php:947
msgid "The highest number of attempts allowed for students to participate a quiz. 0 means unlimited. This will work as the default Quiz Attempt limit in case of Quiz Retry Mode."
msgstr ""

#: classes/Options_V2.php:952
msgid "Show Quiz Previous Button"
msgstr ""

#: classes/Options_V2.php:954
msgid "Choose whether to show or hide the previous button for each question."
msgstr ""

#: classes/Options_V2.php:959
msgid "Final Grade Calculation"
msgstr ""

#: classes/Options_V2.php:960
msgid "When multiple attempts are allowed, select which method should be used to calculate a student's final grade for the quiz."
msgstr ""

#: classes/Options_V2.php:963
msgid "Highest Grade"
msgstr ""

#: classes/Options_V2.php:964
msgid "Average Grade"
msgstr ""

#: classes/Options_V2.php:965
msgid "First Attempt"
msgstr ""

#: classes/Options_V2.php:966
msgid "Last Attempt"
msgstr ""

#: classes/Options_V2.php:972
#: assets/js/tutor-course-builder.js:19771
msgid "Video"
msgstr ""

#: classes/Options_V2.php:980
#: classes/Options_V2.php:981
msgid "Preferred Video Source"
msgstr ""

#: classes/Options_V2.php:983
msgid "Select the video hosting platform(s) you want to enable."
msgstr ""

#: classes/Options_V2.php:988
msgid "Use Tutor Player for YouTube"
msgstr ""

#: classes/Options_V2.php:991
msgid "Enable this option to use Tutor LMS video player for YouTube."
msgstr ""

#: classes/Options_V2.php:996
msgid "Use Tutor Player for Vimeo"
msgstr ""

#: classes/Options_V2.php:999
msgid "Enable this option to use Tutor LMS video player for Vimeo."
msgstr ""

#: classes/Options_V2.php:1006
msgid "Monetization"
msgstr ""

#: classes/Options_V2.php:1008
msgid "Monetization Settings"
msgstr ""

#: classes/Options_V2.php:1013
#: classes/Options_V2.php:1784
#: assets/js/tutor-course-builder.js:12025
msgid "Options"
msgstr ""

#: classes/Options_V2.php:1020
msgid "Select eCommerce Engine"
msgstr ""

#: classes/Options_V2.php:1025
msgid "Disable Monetization"
msgstr ""

#: classes/Options_V2.php:1029
msgid "Select a monetization option to generate revenue by selling courses."
msgstr ""

#: classes/Options_V2.php:1034
#: classes/WooCommerce.php:576
msgid "WooCommerce"
msgstr ""

#: classes/Options_V2.php:1041
msgid "Automatically Complete WooCommerce Orders"
msgstr ""

#: classes/Options_V2.php:1044
msgid "If enabled, in the case of Courses, WooCommerce Orders will get the \"Completed\" status ."
msgstr ""

#: classes/Options_V2.php:1049
msgid "Auto Redirect to Courses"
msgstr ""

#: classes/Options_V2.php:1052
msgid "When a user's WooCommerce order is auto-completed, they will be redirected to enrolled courses"
msgstr ""

#: classes/Options_V2.php:1057
msgid "Enable Guest Mode"
msgstr ""

#: classes/Options_V2.php:1060
msgid "Allow customers to place orders without an account."
msgstr ""

#: classes/Options_V2.php:1065
#: classes/Tutor_Setup.php:475
msgid "Revenue Sharing"
msgstr ""

#: classes/Options_V2.php:1072
msgid "Enable Revenue Sharing"
msgstr ""

#: classes/Options_V2.php:1075
#: classes/Tutor_Setup.php:476
msgid "Allow revenue generated from selling courses to be shared with course creators."
msgstr ""

#: classes/Options_V2.php:1082
#: classes/Tutor_Setup.php:480
msgid "Sharing Percentage"
msgstr ""

#: classes/Options_V2.php:1089
msgid "Instructor Takes"
msgstr ""

#: classes/Options_V2.php:1095
msgid "Admin Takes"
msgstr ""

#: classes/Options_V2.php:1099
msgid "Set how the sales revenue will be shared among admins and instructors."
msgstr ""

#: classes/Options_V2.php:1104
msgid "Fees"
msgstr ""

#: classes/Options_V2.php:1111
msgid "Deduct Fees"
msgstr ""

#: classes/Options_V2.php:1114
msgid "Fees are charged from the entire sales amount. The remaining amount will be divided among admin and instructors."
msgstr ""

#: classes/Options_V2.php:1120
#: classes/Options_V2.php:1121
msgid "Fee Description"
msgstr ""

#: classes/Options_V2.php:1122
msgid "Set a description for the fee that you are deducting. Make sure to give a reasonable explanation to maintain transparency with your site’s instructors."
msgstr ""

#: classes/Options_V2.php:1130
msgid "Fee Amount & Type"
msgstr ""

#: classes/Options_V2.php:1131
msgid "Select the fee type and add fee amount/percentage"
msgstr ""

#: classes/Options_V2.php:1138
#: views/pages/ecommerce/coupon-list.php:143
#: assets/js/tutor-coupon.js:5237
msgid "Percent"
msgstr ""

#: classes/Options_V2.php:1139
msgid "Fixed"
msgstr ""

#: classes/Options_V2.php:1151
#: templates/dashboard/settings/nav-bar.php:30
msgid "Withdraw"
msgstr ""

#: classes/Options_V2.php:1158
msgid "Minimum Withdrawal Amount"
msgstr ""

#: classes/Options_V2.php:1160
msgid "Instructors should earn equal or above this amount to make a withdraw request."
msgstr ""

#: classes/Options_V2.php:1166
msgid "Minimum Days Before Balance is Available"
msgstr ""

#: classes/Options_V2.php:1169
msgid "Any income has to remain this many days in the platform before it is available for withdrawal."
msgstr ""

#: classes/Options_V2.php:1174
msgid "Enable Withdraw Method"
msgstr ""

#: classes/Options_V2.php:1177
msgid "Set how you would like to withdraw money from the website."
msgstr ""

#: classes/Options_V2.php:1182
msgid "Bank Instructions"
msgstr ""

#: classes/Options_V2.php:1183
msgid "Write the up to date bank informations of your instructor here."
msgstr ""

#: classes/Options_V2.php:1184
msgid "Write bank instructions for the instructors to conduct withdrawals."
msgstr ""

#: classes/Options_V2.php:1191
msgid "Design"
msgstr ""

#: classes/Options_V2.php:1193
msgid "Design Settings"
msgstr ""

#: classes/Options_V2.php:1205
msgid "Column Per Row"
msgstr ""

#: classes/Options_V2.php:1208
msgid "One"
msgstr ""

#: classes/Options_V2.php:1209
msgid "Two"
msgstr ""

#: classes/Options_V2.php:1210
msgid "Three"
msgstr ""

#: classes/Options_V2.php:1211
msgid "Four"
msgstr ""

#: classes/Options_V2.php:1213
msgid "Define how many columns you want to use to display courses."
msgstr ""

#: classes/Options_V2.php:1218
msgid "Course Filter"
msgstr ""

#: classes/Options_V2.php:1221
msgid "Show sorting and filtering options on course archive page"
msgstr ""

#: classes/Options_V2.php:1227
#: classes/Tutor_Setup.php:464
msgid "Courses Per Page"
msgstr ""

#: classes/Options_V2.php:1229
msgid "Set the number of courses to display per page on the Course List page."
msgstr ""

#: classes/Options_V2.php:1234
msgid "Preferred Course Filters"
msgstr ""

#: classes/Options_V2.php:1237
msgid "Keyword Search"
msgstr ""

#: classes/Options_V2.php:1239
#: templates/course-filter/filters.php:69
msgid "Tag"
msgstr ""

#: classes/Options_V2.php:1241
msgid "Price Type"
msgstr ""

#: classes/Options_V2.php:1243
msgid "Choose preferred filter options you'd like to show on the course archive page."
msgstr ""

#: classes/Options_V2.php:1248
msgid "Course Sorting"
msgstr ""

#: classes/Options_V2.php:1251
msgid "If enabled, the courses will be sortable by Course Name or Creation Date in either Ascending or Descending order"
msgstr ""

#: classes/Options_V2.php:1256
msgid "Layout"
msgstr ""

#: classes/Options_V2.php:1263
msgid "Instructor List Layout"
msgstr ""

#: classes/Options_V2.php:1264
msgid "Choose a layout for the list of instructors inside a course page. You can change this at any time."
msgstr ""

#: classes/Options_V2.php:1269
#: assets/js/tutor-course-builder.js:28443
msgid "Portrait"
msgstr ""

#: classes/Options_V2.php:1273
msgid "Cover"
msgstr ""

#: classes/Options_V2.php:1277
#: classes/Options_V2.php:1309
#: classes/Options_V2.php:1334
msgid "Minimal"
msgstr ""

#: classes/Options_V2.php:1283
msgid "Portrait Horizontal"
msgstr ""

#: classes/Options_V2.php:1287
msgid "Minimal Horizontal"
msgstr ""

#: classes/Options_V2.php:1296
msgid "Instructor Public Profile Layout"
msgstr ""

#: classes/Options_V2.php:1297
msgid "Choose a layout design for a instructor’s public profile"
msgstr ""

#: classes/Options_V2.php:1305
#: classes/Options_V2.php:1330
msgid "Modern"
msgstr ""

#: classes/Options_V2.php:1313
#: classes/Options_V2.php:1338
msgid "Classic"
msgstr ""

#: classes/Options_V2.php:1321
msgid "Student Public Profile Layout"
msgstr ""

#: classes/Options_V2.php:1322
msgid "Choose a layout design for a student’s public profile"
msgstr ""

#: classes/Options_V2.php:1346
msgid "Course Details"
msgstr ""

#: classes/Options_V2.php:1353
msgid "Page Features"
msgstr ""

#: classes/Options_V2.php:1354
msgid "You can keep the following features active or inactive as per the need of your business model"
msgstr ""

#: classes/Options_V2.php:1359
msgid "Instructor Info"
msgstr ""

#: classes/Options_V2.php:1361
msgid "Toggle to show instructor info"
msgstr ""

#: classes/Options_V2.php:1368
msgid "Enable to add a Q&A section"
msgstr ""

#: classes/Options_V2.php:1373
#: views/pages/course-list.php:271
#: assets/js/tutor-course-builder.js:11282
msgid "Author"
msgstr ""

#: classes/Options_V2.php:1376
msgid "Enable to show course author name"
msgstr ""

#: classes/Options_V2.php:1381
#: templates/course-filter/filters.php:83
#: templates/single/course/course-entry-box.php:54
msgid "Level"
msgstr ""

#: classes/Options_V2.php:1384
msgid "Enable to show course level"
msgstr ""

#: classes/Options_V2.php:1389
msgid "Social Share"
msgstr ""

#: classes/Options_V2.php:1392
msgid "Toggle to enable course social share"
msgstr ""

#: classes/Options_V2.php:1397
#: templates/single/course/course-entry-box.php:38
#: templates/single/course/course-entry-box.php:39
#: assets/js/tutor-course-builder.js:13660
msgid "Duration"
msgstr ""

#: classes/Options_V2.php:1398
#: classes/Options_V2.php:1470
msgid "Disable"
msgstr ""

#: classes/Options_V2.php:1400
msgid "Enable to show course duration"
msgstr ""

#: classes/Options_V2.php:1405
#: templates/single/course/course-entry-box.php:33
#: templates/single/course/course-entry-box.php:34
msgid "Total Enrolled"
msgstr ""

#: classes/Options_V2.php:1408
msgid "Enable to show total enrolled students"
msgstr ""

#: classes/Options_V2.php:1413
msgid "Update Date"
msgstr ""

#: classes/Options_V2.php:1416
msgid "Enable to show course update information"
msgstr ""

#: classes/Options_V2.php:1421
msgid "Progress Bar"
msgstr ""

#: classes/Options_V2.php:1424
msgid "Enable to show course progress for Students"
msgstr ""

#: classes/Options_V2.php:1429
msgid "Material"
msgstr ""

#: classes/Options_V2.php:1432
msgid "Enable to show course materials"
msgstr ""

#: classes/Options_V2.php:1437
msgid "About"
msgstr ""

#: classes/Options_V2.php:1440
msgid "Enable to show course about section"
msgstr ""

#: classes/Options_V2.php:1445
#: templates/single/assignment/content.php:225
#: templates/single/assignment/content.php:402
#: templates/single/assignment/content.php:585
#: views/pages/tools/manage-tokens.php:119
#: views/pages/tools/manage-tokens.php:185
#: assets/js/tutor-course-builder.js:12365
msgid "Description"
msgstr ""

#: classes/Options_V2.php:1448
msgid "Enable to show course description"
msgstr ""

#: classes/Options_V2.php:1453
msgid "Benefits"
msgstr ""

#: classes/Options_V2.php:1456
msgid "Enable to show course benefits section"
msgstr ""

#: classes/Options_V2.php:1461
#: templates/single/course/course-requirements.php:25
msgid "Requirements"
msgstr ""

#: classes/Options_V2.php:1464
msgid "Enable to show courses requirements section"
msgstr ""

#: classes/Options_V2.php:1469
#: assets/js/tutor-course-builder.js:29459
msgid "Target Audience"
msgstr ""

#: classes/Options_V2.php:1472
msgid "Enable to show course target audience section"
msgstr ""

#: classes/Options_V2.php:1480
msgid "Enable to show course announcements section"
msgstr ""

#: classes/Options_V2.php:1485
#: views/quiz/attempt-table.php:175
#: views/quiz/contexts.php:101
msgid "Review"
msgstr ""

#: classes/Options_V2.php:1488
msgid "Enable to show course review section"
msgstr ""

#: classes/Options_V2.php:1495
msgid "Colors"
msgstr ""

#: classes/Options_V2.php:1502
#: classes/Options_V2.php:1636
msgid "Preset Colors"
msgstr ""

#: classes/Options_V2.php:1503
msgid "These colors will be used throughout your website. Choose between these presets or create your own custom palette."
msgstr ""

#: classes/Options_V2.php:1509
#: classes/Quiz.php:217
#: assets/js/tutor-course-builder.js:24512
msgid "Default"
msgstr ""

#: classes/Options_V2.php:1540
#: assets/js/tutor-course-builder.js:28428
msgid "Landscape"
msgstr ""

#: classes/Options_V2.php:1571
msgid "Ocean"
msgstr ""

#: classes/Options_V2.php:1602
#: classes/Utils.php:9030
msgid "Custom"
msgstr ""

#: classes/Options_V2.php:1643
msgid "Primary Color"
msgstr ""

#: classes/Options_V2.php:1645
msgid "Choose a primary color"
msgstr ""

#: classes/Options_V2.php:1652
msgid "Primary Hover Color"
msgstr ""

#: classes/Options_V2.php:1654
msgid "Choose a primary hover color"
msgstr ""

#: classes/Options_V2.php:1661
msgid "Text Color"
msgstr ""

#: classes/Options_V2.php:1663
msgid "Choose a text color for your website"
msgstr ""

#: classes/Options_V2.php:1670
msgid "Gray"
msgstr ""

#: classes/Options_V2.php:1672
msgid "Choose a color for elements like table, card etc"
msgstr ""

#: classes/Options_V2.php:1679
msgid "Border"
msgstr ""

#: classes/Options_V2.php:1681
msgid "Choose a border color for your website"
msgstr ""

#: classes/Options_V2.php:1690
msgid "Advanced"
msgstr ""

#: classes/Options_V2.php:1692
#: ecommerce/Settings.php:186
#: ecommerce/Settings.php:213
#: assets/js/tutor-course-builder.js:24663
msgid "Advanced Settings"
msgstr ""

#: classes/Options_V2.php:1704
msgid "Gutenberg Editor"
msgstr ""

#: classes/Options_V2.php:1706
msgid "Enable this to create courses using the Gutenberg Editor."
msgstr ""

#: classes/Options_V2.php:1711
msgid "Hide Course Products on Shop Page"
msgstr ""

#: classes/Options_V2.php:1713
msgid "Enable to hide course products on shop page."
msgstr ""

#: classes/Options_V2.php:1718
msgid "Course Archive Page"
msgstr ""

#: classes/Options_V2.php:1721
msgid "This page will be used to list all the published courses."
msgstr ""

#: classes/Options_V2.php:1727
#: classes/Utils.php:7500
msgid "Instructor Registration Page"
msgstr ""

#: classes/Options_V2.php:1730
msgid "Choose the page for instructor registration."
msgstr ""

#: classes/Options_V2.php:1736
#: classes/Utils.php:7501
msgid "Student Registration Page"
msgstr ""

#: classes/Options_V2.php:1739
msgid "Choose the page for student registration."
msgstr ""

#: classes/Options_V2.php:1745
msgid "YouTube API Key"
msgstr ""

#: classes/Options_V2.php:1747
msgid "To host live videos on your platform using YouTube, enter your YouTube API key."
msgstr ""

#: classes/Options_V2.php:1751
msgid "Insert API key here"
msgstr ""

#: classes/Options_V2.php:1756
msgid "Base Permalink"
msgstr ""

#: classes/Options_V2.php:1763
msgid "Course Permalink"
msgstr ""

#: classes/Options_V2.php:1770
msgid "Lesson Permalink"
msgstr ""

#: classes/Options_V2.php:1777
msgid "Quiz Permalink"
msgstr ""

#: classes/Options_V2.php:1791
msgid "Profile Completion"
msgstr ""

#: classes/Options_V2.php:1794
msgid "Enabling this feature will show a notification bar to students and instructors to complete their profile information"
msgstr ""

#: classes/Options_V2.php:1799
msgid "Enable Tutor LMS Login"
msgstr ""

#: classes/Options_V2.php:1802
msgid "Enable to use the Tutor LMS native login system instead of the WordPress login page"
msgstr ""

#: classes/Options_V2.php:1807
msgid "Erase upon uninstallation"
msgstr ""

#: classes/Options_V2.php:1810
msgid "Delete all data during uninstallation"
msgstr ""

#: classes/Options_V2.php:1815
msgid "Maintenance Mode"
msgstr ""

#: classes/Options_V2.php:1818
msgid "Enabling maintenance mode will display a custom message on the frontend. During maintenance mode, visitors cannot access site content, but the wp-admin dashboard remains accessible."
msgstr ""

#: classes/Post_types.php:92
msgctxt "post type general name"
msgid "Courses"
msgstr ""

#: classes/Post_types.php:93
msgctxt "post type singular name"
msgid "Course"
msgstr ""

#: classes/Post_types.php:94
msgctxt "admin menu"
msgid "Courses"
msgstr ""

#: classes/Post_types.php:95
msgctxt "add new on admin bar"
msgid "Course"
msgstr ""

#: classes/Post_types.php:96
msgctxt "tutor course add"
msgid "Add New"
msgstr ""

#: classes/Post_types.php:97
msgid "Add New Course"
msgstr ""

#: classes/Post_types.php:99
msgid "Edit Course"
msgstr ""

#: classes/Post_types.php:100
#: includes/tutor-template-functions.php:1619
#: templates/single/lesson/required-enroll.php:21
msgid "View Course"
msgstr ""

#: classes/Post_types.php:102
msgid "Search Courses"
msgstr ""

#: classes/Post_types.php:103
msgid "Parent Courses:"
msgstr ""

#: classes/Post_types.php:104
msgid "No courses found."
msgstr ""

#: classes/Post_types.php:105
msgid "No courses found in Trash."
msgstr ""

#: classes/Post_types.php:110
#: classes/Post_types.php:242
#: classes/Post_types.php:309
#: classes/Post_types.php:349
#: classes/Post_types.php:388
#: classes/Post_types.php:503
msgid "Description."
msgstr ""

#: classes/Post_types.php:148
msgctxt "taxonomy general name"
msgid "Course Categories"
msgstr ""

#: classes/Post_types.php:149
msgctxt "taxonomy singular name"
msgid "Category"
msgstr ""

#: classes/Post_types.php:150
msgid "Search Categories"
msgstr ""

#: classes/Post_types.php:151
msgid "Popular Categories"
msgstr ""

#: classes/Post_types.php:152
#: views/pages/course-list.php:68
msgid "All Categories"
msgstr ""

#: classes/Post_types.php:155
msgid "Edit Category"
msgstr ""

#: classes/Post_types.php:156
msgid "Update Category"
msgstr ""

#: classes/Post_types.php:157
msgid "Add New Category"
msgstr ""

#: classes/Post_types.php:158
msgid "New Category Name"
msgstr ""

#: classes/Post_types.php:159
msgid "Separate categories with commas"
msgstr ""

#: classes/Post_types.php:160
msgid "Add or remove categories"
msgstr ""

#: classes/Post_types.php:161
msgid "Choose from the most used categories"
msgstr ""

#: classes/Post_types.php:162
#: assets/js/tutor-course-builder.js:7722
#: assets/js/tutor-course-builder.js:7957
msgid "No categories found."
msgstr ""

#: classes/Post_types.php:163
msgid "Course Categories"
msgstr ""

#: classes/Post_types.php:180
msgctxt "taxonomy general name"
msgid "Tags"
msgstr ""

#: classes/Post_types.php:181
msgctxt "taxonomy singular name"
msgid "Tag"
msgstr ""

#: classes/Post_types.php:182
msgid "Search Tags"
msgstr ""

#: classes/Post_types.php:183
msgid "Popular Tags"
msgstr ""

#: classes/Post_types.php:184
msgid "All Tags"
msgstr ""

#: classes/Post_types.php:187
msgid "Edit Tag"
msgstr ""

#: classes/Post_types.php:188
msgid "Update Tag"
msgstr ""

#: classes/Post_types.php:189
msgid "Add New Tag"
msgstr ""

#: classes/Post_types.php:190
msgid "New Tag Name"
msgstr ""

#: classes/Post_types.php:191
msgid "Separate Tags with commas"
msgstr ""

#: classes/Post_types.php:192
msgid "Add or remove Tags"
msgstr ""

#: classes/Post_types.php:193
msgid "Choose from the most used Tags"
msgstr ""

#: classes/Post_types.php:194
msgid "No Tags found."
msgstr ""

#: classes/Post_types.php:224
msgctxt "post type general name"
msgid "Lessons"
msgstr ""

#: classes/Post_types.php:225
msgctxt "post type singular name"
msgid "Lesson"
msgstr ""

#: classes/Post_types.php:226
msgctxt "admin menu"
msgid "Lessons"
msgstr ""

#: classes/Post_types.php:227
msgctxt "add new on admin bar"
msgid "Lesson"
msgstr ""

#: classes/Post_types.php:228
msgctxt "tutor lesson add"
msgid "Add New"
msgstr ""

#: classes/Post_types.php:229
msgid "Add New Lesson"
msgstr ""

#: classes/Post_types.php:230
msgid "New Lesson"
msgstr ""

#: classes/Post_types.php:231
msgid "Edit Lesson"
msgstr ""

#: classes/Post_types.php:232
msgid "View Lesson"
msgstr ""

#: classes/Post_types.php:233
msgid "Lessons"
msgstr ""

#: classes/Post_types.php:234
msgid "Search Lessons"
msgstr ""

#: classes/Post_types.php:235
msgid "Parent Lessons:"
msgstr ""

#: classes/Post_types.php:236
msgid "No lessons found."
msgstr ""

#: classes/Post_types.php:237
msgid "No lessons found in Trash."
msgstr ""

#: classes/Post_types.php:291
msgctxt "post type general name"
msgid "Quizzes"
msgstr ""

#: classes/Post_types.php:292
msgctxt "post type singular name"
msgid "Quiz"
msgstr ""

#: classes/Post_types.php:293
msgctxt "admin menu"
msgid "Quizzes"
msgstr ""

#: classes/Post_types.php:294
msgctxt "add new on admin bar"
msgid "Quiz"
msgstr ""

#: classes/Post_types.php:295
msgctxt "tutor quiz add"
msgid "Add New"
msgstr ""

#: classes/Post_types.php:296
msgid "Add New Quiz"
msgstr ""

#: classes/Post_types.php:297
msgid "New Quiz"
msgstr ""

#: classes/Post_types.php:298
msgid "Edit Quiz"
msgstr ""

#: classes/Post_types.php:299
msgid "View Quiz"
msgstr ""

#: classes/Post_types.php:300
msgid "Quizzes"
msgstr ""

#: classes/Post_types.php:301
msgid "Search Quizzes"
msgstr ""

#: classes/Post_types.php:302
msgid "Parent Quizzes:"
msgstr ""

#: classes/Post_types.php:303
msgid "No quizzes found."
msgstr ""

#: classes/Post_types.php:304
msgid "No quizzes found in Trash."
msgstr ""

#: classes/Post_types.php:370
msgctxt "post type general name"
msgid "Assignments"
msgstr ""

#: classes/Post_types.php:371
msgctxt "post type singular name"
msgid "Assignment"
msgstr ""

#: classes/Post_types.php:372
msgctxt "admin menu"
msgid "Assignments"
msgstr ""

#: classes/Post_types.php:373
msgctxt "add new on admin bar"
msgid "Assignment"
msgstr ""

#: classes/Post_types.php:374
msgctxt "tutor assignment add"
msgid "Add New"
msgstr ""

#: classes/Post_types.php:375
msgid "Add New Assignment"
msgstr ""

#: classes/Post_types.php:376
msgid "New Assignment"
msgstr ""

#: classes/Post_types.php:377
msgid "Edit Assignment"
msgstr ""

#: classes/Post_types.php:378
msgid "View Assignment"
msgstr ""

#: classes/Post_types.php:379
msgid "Assignments"
msgstr ""

#: classes/Post_types.php:380
msgid "Search Assignments"
msgstr ""

#: classes/Post_types.php:381
msgid "Parent Assignments:"
msgstr ""

#: classes/Post_types.php:382
msgid "No Assignments found."
msgstr ""

#: classes/Post_types.php:383
msgid "No Assignments found in Trash."
msgstr ""

#: classes/Post_types.php:435
msgid "Course restored to revision from "
msgstr ""

#: classes/Post_types.php:442
msgid "M j, Y @ G:i"
msgstr ""

#: classes/Post_types.php:443
msgid "Course scheduled for:"
msgstr ""

#: classes/Post_types.php:448
#: classes/Post_types.php:451
msgid "Course updated."
msgstr ""

#: classes/Post_types.php:449
msgid "Custom field updated."
msgstr ""

#: classes/Post_types.php:450
msgid "Custom field deleted."
msgstr ""

#: classes/Post_types.php:453
msgid "Course published."
msgstr ""

#: classes/Post_types.php:454
msgid "Course saved."
msgstr ""

#: classes/Post_types.php:455
msgid "Course submitted."
msgstr ""

#: classes/Post_types.php:457
msgid "Course draft updated."
msgstr ""

#: classes/Post_types.php:463
msgid "View course"
msgstr ""

#: classes/Post_types.php:469
msgid "Preview course"
msgstr ""

#: classes/Quiz.php:145
#: includes/translate-text.php:188
#: templates/single/quiz/top.php:73
#: assets/js/tutor-course-builder.js:24473
msgid "Seconds"
msgstr ""

#: classes/Quiz.php:146
#: includes/translate-text.php:182
#: templates/single/quiz/top.php:74
#: assets/js/tutor-course-builder.js:13675
#: assets/js/tutor-course-builder.js:24476
msgid "Minutes"
msgstr ""

#: classes/Quiz.php:147
#: includes/translate-text.php:176
#: templates/single/quiz/top.php:75
#: assets/js/tutor-course-builder.js:10582
#: assets/js/tutor-course-builder.js:13678
#: assets/js/tutor-course-builder.js:18197
#: assets/js/tutor-course-builder.js:24479
msgid "Hours"
msgstr ""

#: classes/Quiz.php:148
#: includes/translate-text.php:170
#: templates/single/quiz/top.php:76
#: assets/js/tutor-course-builder.js:10584
#: assets/js/tutor-course-builder.js:18194
#: assets/js/tutor-course-builder.js:24482
msgid "Days"
msgstr ""

#: classes/Quiz.php:149
#: templates/single/quiz/top.php:77
#: assets/js/tutor-course-builder.js:10586
#: assets/js/tutor-course-builder.js:18191
#: assets/js/tutor-course-builder.js:24485
msgid "Weeks"
msgstr ""

#: classes/Quiz.php:218
msgid "Answers shown after quiz is finished"
msgstr ""

#: classes/Quiz.php:222
#: assets/js/tutor-course-builder.js:24516
msgid "Reveal Mode"
msgstr ""

#: classes/Quiz.php:223
msgid "Show result after the attempt."
msgstr ""

#: classes/Quiz.php:227
msgid "Retry Mode"
msgstr ""

#: classes/Quiz.php:228
msgid "Reattempt quiz any number of times. Define Attempts Allowed below."
msgstr ""

#: classes/Quiz.php:244
msgid "Set question layout view"
msgstr ""

#: classes/Quiz.php:245
msgid "Single Question"
msgstr ""

#: classes/Quiz.php:246
msgid "Question Pagination"
msgstr ""

#: classes/Quiz.php:247
#: assets/js/tutor-course-builder.js:24694
msgid "Question below each other"
msgstr ""

#: classes/Quiz.php:262
#: assets/js/tutor-course-builder.js:24707
msgid "Random"
msgstr ""

#: classes/Quiz.php:263
#: assets/js/tutor-course-builder.js:24710
msgid "Sorting"
msgstr ""

#: classes/Quiz.php:264
#: assets/js/tutor-course-builder.js:24713
msgid "Ascending"
msgstr ""

#: classes/Quiz.php:265
#: assets/js/tutor-course-builder.js:24716
msgid "Descending"
msgstr ""

#: classes/Quiz.php:322
msgid "Invalid quiz info"
msgstr ""

#: classes/Quiz.php:918
msgid "Quiz has been timeout already"
msgstr ""

#: classes/Quiz.php:1105
msgid "Quiz updated successfully"
msgstr ""

#: classes/Quiz.php:1110
msgid "Quiz created successfully"
msgstr ""

#: classes/Quiz.php:1139
msgid "Quiz data fetched successfully"
msgstr ""

#: classes/Quiz.php:1171
msgid "Invalid quiz"
msgstr ""

#: classes/Quiz.php:1203
msgid "Quiz deleted successfully"
msgstr ""

#: classes/Quiz.php:1229
msgid "Quiz Permission Denied"
msgstr ""

#: classes/Quiz.php:1273
msgid "Question successfully deleted"
msgstr ""

#: classes/Quiz.php:1368
#: classes/Quiz.php:1764
#: assets/js/tutor-course-builder.js:24010
msgid "True"
msgstr ""

#: classes/Quiz.php:1375
#: classes/Quiz.php:1771
#: assets/js/tutor-course-builder.js:24021
msgid "False"
msgstr ""

#: classes/Quiz.php:1580
#: views/qna/contexts.php:17
#: views/quiz/contexts.php:19
#: views/quiz/contexts.php:67
#: assets/js/tutor-course-builder.js:22078
msgid "Question"
msgstr ""

#: classes/Quiz.php:1608
msgid "Question created successfully"
msgstr ""

#: classes/Quiz.php:1630
msgid "Invalid quiz question ID"
msgstr ""

#: classes/Quiz.php:1670
msgid "Please make sure the question has answer"
msgstr ""

#: classes/Quiz.php:1698
msgid "Question updated successfully"
msgstr ""

#: classes/Quiz.php:1738
msgid "Question order successfully updated"
msgstr ""

#: classes/Quiz.php:1815
msgid "Invalid question"
msgstr ""

#: classes/Quiz.php:1861
msgid "Invalid question type"
msgstr ""

#: classes/Quiz.php:1871
msgid "Question answer updated successfully"
msgstr ""

#: classes/Quiz.php:1876
msgid "Question answer saved successfully"
msgstr ""

#: classes/Quiz.php:1904
msgid "Answer deleted successfully"
msgstr ""

#: classes/Quiz.php:1941
msgid "Question answer order successfully updated"
msgstr ""

#: classes/Quiz.php:2028
msgid "Answer mark as correct updated"
msgstr ""

#: classes/Quiz.php:2047
msgid "Access Denied."
msgstr ""

#: classes/Quiz.php:2120
msgid "Invalid attempt ID"
msgstr ""

#: classes/Quiz.php:2128
msgid "Attempt deleted successfully!"
msgstr ""

#: classes/QuizBuilder.php:212
msgid "Invalid payload"
msgstr ""

#. translators: %s is the tracking key required for each question.
#: classes/QuizBuilder.php:235
msgid "%s is required for each question"
msgstr ""

#. translators: %s is the tracking key for which the value is invalid.
#: classes/QuizBuilder.php:242
msgid "Invalid value for %s"
msgstr ""

#: classes/QuizBuilder.php:248
msgid "Question settings is required with array data"
msgstr ""

#: classes/QuizBuilder.php:400
msgid "Quiz saved successfully"
msgstr ""

#: classes/QuizBuilder.php:402
#: assets/js/tutor-addon-list.js:440
#: assets/js/tutor-admin.js:305
#: assets/js/tutor-admin.js:319
#: assets/js/tutor-admin.js:2592
#: assets/js/tutor-coupon.js:980
#: assets/js/tutor-course-builder.js:5938
#: assets/js/tutor-front.js:216
#: assets/js/tutor-front.js:290
#: assets/js/tutor-front.js:316
#: assets/js/tutor-front.js:904
#: assets/js/tutor-front.js:2441
#: assets/js/tutor-front.js:3603
#: assets/js/tutor-front.js:4178
#: assets/js/tutor-import-export.js:443
#: assets/js/tutor-order-details.js:442
#: assets/js/tutor-payment-settings.js:443
#: assets/js/tutor-tax-settings.js:442
#: assets/js/tutor.js:2958
#: assets/js/tutor.js:2963
msgid "Error"
msgstr ""

#: classes/Quiz_Attempts_List.php:227
#: includes/translate-text.php:45
#: views/quiz/attempt-details.php:296
#: views/quiz/attempt-table.php:159
msgid "Pass"
msgstr ""

#: classes/Quiz_Attempts_List.php:233
#: includes/translate-text.php:53
#: views/quiz/attempt-details.php:298
#: views/quiz/attempt-table.php:160
msgid "Fail"
msgstr ""

#: classes/Q_And_A.php:100
msgid "Empty Content Not Allowed!"
msgstr ""

#: classes/Q_And_A.php:325
msgid "Permission Denied!"
msgstr ""

#: classes/Reviews.php:56
#: classes/Student.php:308
#: assets/js/tutor-addon-list.js:6499
#: assets/js/tutor-admin.js:1810
#: assets/js/tutor-admin.js:1817
#: assets/js/tutor-admin.js:1858
#: assets/js/tutor-admin.js:1865
#: assets/js/tutor-admin.js:1908
#: assets/js/tutor-admin.js:1915
#: assets/js/tutor-admin.js:1996
#: assets/js/tutor-admin.js:2003
#: assets/js/tutor-admin.js:3187
msgid "Something went wrong!"
msgstr ""

#: classes/Reviews.php:112
msgid "Only admin can change review status"
msgstr ""

#: classes/Shortcode.php:113
msgid "Sign-In"
msgstr ""

#. translators: %s is anchor link for signin
#: classes/Shortcode.php:115
msgid "Please %s to view this page"
msgstr ""

#: classes/Student.php:221
msgid "Profile Updated"
msgstr ""

#: classes/Student.php:291
msgid "Incorrect Previous Password"
msgstr ""

#: classes/Student.php:294
msgid "New Password Required"
msgstr ""

#: classes/Student.php:297
msgid "Confirm Password Required"
msgstr ""

#: classes/Student.php:300
msgid "New password and confirm password does not matched"
msgstr ""

#: classes/Student.php:305
msgid "Password Changed"
msgstr ""

#: classes/Student.php:334
msgid "Social Profile Updated"
msgstr ""

#: classes/Taxonomies.php:48
#: classes/Taxonomies.php:132
#: classes/Taxonomies.php:265
msgid "Thumbnail"
msgstr ""

#: classes/Taxonomies.php:52
#: classes/Taxonomies.php:139
msgid "Upload/Add image"
msgstr ""

#: classes/Taxonomies.php:53
#: classes/Taxonomies.php:140
msgid "Remove image"
msgstr ""

#: classes/Taxonomies.php:77
#: classes/Taxonomies.php:164
msgid "Choose an image"
msgstr ""

#: classes/Taxonomies.php:79
#: classes/Taxonomies.php:166
msgid "Use image"
msgstr ""

#: classes/Taxonomies.php:236
#: assets/js/tutor-course-builder.js:22727
msgid "Image"
msgstr ""

#: classes/Template.php:483
msgid "Profile Page"
msgstr ""

#: classes/Tools_V2.php:105
msgid "Status Settings"
msgstr ""

#: classes/Tools_V2.php:111
#: views/pages/tools/status.php:22
msgid "WordPress environment"
msgstr ""

#: classes/Tools_V2.php:120
#: views/pages/tools/status.php:27
msgid "Home URL"
msgstr ""

#: classes/Tools_V2.php:129
#: views/pages/tools/status.php:40
msgid "WordPress version"
msgstr ""

#: classes/Tools_V2.php:137
#: views/pages/tools/status.php:75
msgid "WordPress multisite"
msgstr ""

#: classes/Tools_V2.php:145
#: views/pages/tools/status.php:94
msgid "WordPress debug mode"
msgstr ""

#: classes/Tools_V2.php:153
#: classes/Tools_V2.php:254
#: views/pages/tools/status.php:116
#: assets/js/tutor-coupon.js:6013
#: assets/js/tutor-course-builder.js:5572
#: assets/js/tutor-order-details.js:7533
#: assets/js/tutor-payment-settings.js:9469
#: assets/js/tutor-tax-settings.js:8942
msgid "Language"
msgstr ""

#: classes/Tools_V2.php:163
#: views/pages/tools/status.php:34
msgid "Site URL"
msgstr ""

#: classes/Tools_V2.php:173
msgid "Tutor version"
msgstr ""

#: classes/Tools_V2.php:181
#: views/pages/tools/status.php:80
msgid "WordPress memory limit"
msgstr ""

#: classes/Tools_V2.php:189
msgid "WordPress Cron"
msgstr ""

#: classes/Tools_V2.php:197
#: views/pages/tools/status.php:121
msgid "External object cache"
msgstr ""

#: classes/Tools_V2.php:206
#: views/pages/tools/status.php:139
msgid "Server environment"
msgstr ""

#: classes/Tools_V2.php:214
#: views/pages/tools/status.php:145
msgid "Server info"
msgstr ""

#: classes/Tools_V2.php:222
#: views/pages/tools/status.php:150
msgid "PHP version"
msgstr ""

#: classes/Tools_V2.php:230
#: views/pages/tools/status.php:173
msgid "PHP post max size"
msgstr ""

#: classes/Tools_V2.php:238
#: views/pages/tools/status.php:178
msgid "PHP time limit"
msgstr ""

#: classes/Tools_V2.php:246
#: views/pages/tools/status.php:188
msgid "cURL version"
msgstr ""

#: classes/Tools_V2.php:264
#: views/pages/tools/status.php:208
msgid "MySQL version"
msgstr ""

#: classes/Tools_V2.php:271
#: views/pages/tools/status.php:231
msgid "Default timezone is UTC"
msgstr ""

#: classes/Tools_V2.php:278
#: views/pages/tools/status.php:245
msgid "fsockopen/cURL"
msgstr ""

#: classes/Tools_V2.php:285
#: views/pages/tools/status.php:261
msgid "DOMDocument"
msgstr ""

#: classes/Tools_V2.php:292
#: views/pages/tools/status.php:278
msgid "GZip"
msgstr ""

#: classes/Tools_V2.php:299
#: views/pages/tools/status.php:295
msgid "Multibyte string"
msgstr ""

#: classes/Tools_V2.php:309
#: views/options/template/import_export.php:15
msgid "Import/Export"
msgstr ""

#: classes/Tools_V2.php:311
msgid "Import/Export Settings"
msgstr ""

#: classes/Tools_V2.php:321
msgid "Tutor Pages Settings"
msgstr ""

#: classes/Tools_V2.php:330
#: views/pages/tools/manage-tokens.php:36
msgid "Rest API"
msgstr ""

#: classes/Tools_V2.php:332
msgid "Token List"
msgstr ""

#: classes/Tools_V2.php:341
#: classes/Tools_V2.php:343
#: views/pages/tools/settings-log.php:15
msgid "Settings Log"
msgstr ""

#. Translators: %1$s: Memory limit, %2$s: Docs link.
#: classes/Tools_V2.php:456
#: views/pages/tools/status.php:86
msgid "%1$s - We recommend setting memory to at least 64MB. See: %2$s"
msgstr ""

#. Translators: %1$s: Memory limit, %2$s: Docs link.
#: classes/Tools_V2.php:456
#: views/pages/tools/status.php:86
msgid "Increasing memory allocated to PHP"
msgstr ""

#: classes/Tools_V2.php:468
#: views/pages/tools/status.php:160
msgid "Tutor will run under this version of PHP, however, it has reached end of life. We recommend using PHP version 7.2 or above for greater performance and security."
msgstr ""

#: classes/Tools_V2.php:469
#: views/pages/tools/status.php:162
msgid "We recommend using PHP version 7.2 or above for greater performance and security."
msgstr ""

#. translators: %1$s: MySQL version number, %2$s: WordPress requirements URL
#: classes/Tools_V2.php:486
#: views/pages/tools/status.php:216
msgid "%1$s - We recommend a minimum MySQL version of 5.6. See: %2$s"
msgstr ""

#. translators: %1$s: MySQL version number, %2$s: WordPress requirements URL
#: classes/Tools_V2.php:486
#: views/pages/tools/status.php:216
msgid "WordPress requirements"
msgstr ""

#. translators: %s: default timezone
#: classes/Tools_V2.php:492
#: views/pages/tools/status.php:237
msgid "Default timezone is %s - it should be UTC"
msgstr ""

#: classes/Tools_V2.php:497
#: views/pages/tools/status.php:254
msgid "Your server does not have fsockopen or cURL enabled - PayPal IPN and other scripts which communicate with other servers will not work. Contact your hosting provider."
msgstr ""

#. translators: %s: classname and link.
#: classes/Tools_V2.php:503
#: views/pages/tools/status.php:271
msgid "Your server does not have the %s class enabled - HTML/Multipart emails, and also some extensions, will not work without DOMDocument."
msgstr ""

#. translators: %s: classname and link.
#: classes/Tools_V2.php:509
#: views/pages/tools/status.php:288
msgid "Your server does not support the %s function - this is required to use the GeoIP database from MaxMind."
msgstr ""

#. translators: %s: classname and link.
#: classes/Tools_V2.php:515
#: views/pages/tools/status.php:307
msgid "Your server does not support the %s functions - this is required for better character encoding. Some fallbacks will be used instead for it."
msgstr ""

#: classes/Tutor.php:1040
msgid "Tutor Instructor"
msgstr ""

#: classes/Tutor.php:1140
#: classes/Utils.php:9470
#: templates/dashboard.php:48
#: templates/dashboard/dashboard.php:136
msgid "Dashboard"
msgstr ""

#: classes/Tutor.php:1149
msgid "Student Registration"
msgstr ""

#: classes/Tutor.php:1158
msgid "Instructor Registration"
msgstr ""

#: classes/TutorEDD.php:62
msgid "EDD"
msgstr ""

#: classes/TutorEDD.php:67
msgid "Tutor Course Attachments Settings"
msgstr ""

#: classes/TutorEDD.php:71
msgid "Enable EDD"
msgstr ""

#: classes/TutorEDD.php:72
msgid "This will enable sell your product via EDD"
msgstr ""

#: classes/TutorEDD.php:93
msgid "Easy Digital Downloads"
msgstr ""

#: classes/Tutor_Setup.php:192
#: assets/js/tutor-coupon.js:8048
msgid "OFF"
msgstr ""

#: classes/Tutor_Setup.php:197
msgid "ON"
msgstr ""

#: classes/Tutor_Setup.php:247
#: includes/tutor-template-functions.php:1379
msgid "seconds"
msgstr ""

#: classes/Tutor_Setup.php:248
#: classes/Tutor_Setup.php:256
#: includes/tutor-template-functions.php:1378
msgid "minutes"
msgstr ""

#: classes/Tutor_Setup.php:249
#: includes/tutor-template-functions.php:1377
msgid "hours"
msgstr ""

#: classes/Tutor_Setup.php:250
msgid "days"
msgstr ""

#: classes/Tutor_Setup.php:251
msgid "weeks"
msgstr ""

#: classes/Tutor_Setup.php:343
msgid "Admin / Owner"
msgstr ""

#: classes/Tutor_Setup.php:390
msgid "Unlimited"
msgstr ""

#: classes/Tutor_Setup.php:441
msgid "Course permalink"
msgstr ""

#. translators: %s: sample permalink
#: classes/Tutor_Setup.php:443
#: classes/Tutor_Setup.php:450
msgid "Example:  %s"
msgstr ""

#: classes/Tutor_Setup.php:448
msgid "Lesson permalink"
msgstr ""

#: classes/Tutor_Setup.php:454
msgid "Question and Answer"
msgstr ""

#: classes/Tutor_Setup.php:455
msgid "Allows a Q&A forum on each course."
msgstr ""

#: classes/Tutor_Setup.php:459
msgid "Courses Per Row"
msgstr ""

#: classes/Tutor_Setup.php:460
msgid "How many courses per row on the archive pages."
msgstr ""

#: classes/Tutor_Setup.php:465
msgid "How many courses per page on the archive pages."
msgstr ""

#: classes/Tutor_Setup.php:491
msgid "Payment Withdrawal Method"
msgstr ""

#: classes/Tutor_Setup.php:498
msgid "Currency "
msgstr ""

#: classes/Tutor_Setup.php:503
msgid "Currency Symbol"
msgstr ""

#: classes/Tutor_Setup.php:504
msgid "Choose the currency for transactions"
msgstr ""

#: classes/Tutor_Setup.php:510
msgid "Currency Symbol Position"
msgstr ""

#: classes/Tutor_Setup.php:511
msgid "Set the position of the currency symbol"
msgstr ""

#: classes/Tutor_Setup.php:543
#: ecommerce/Settings.php:112
#: ecommerce/Settings.php:119
msgid "Currency"
msgstr ""

#: classes/Tutor_Setup.php:546
#: templates/single/quiz/body.php:73
msgid "Finish"
msgstr ""

#: classes/Tutor_Setup.php:567
msgid "Congratulations, you’re all set!"
msgstr ""

#: classes/Tutor_Setup.php:570
msgctxt "tutor setup content"
msgid "Tutor LMS is up and running on your website! If you really want to become a Tutor LMS genius, read our "
msgstr ""

#: classes/Tutor_Setup.php:572
msgctxt "tutor setup content"
msgid "documentation"
msgstr ""

#: classes/Tutor_Setup.php:574
msgctxt "tutor setup content"
msgid "that covers everything!"
msgstr ""

#: classes/Tutor_Setup.php:577
msgctxt "tutor-setup-assistance"
msgid "If you need further assistance, please don’t hesitate to contact us via our "
msgstr ""

#: classes/Tutor_Setup.php:579
msgctxt "tutor-setup-assistance"
msgid "contact form."
msgstr ""

#: classes/Tutor_Setup.php:590
msgid "Create a New Course"
msgstr ""

#: classes/Tutor_Setup.php:593
msgid "Explore Addons"
msgstr ""

#: classes/Tutor_Setup.php:617
#: classes/Tutor_Setup.php:646
#: templates/single/common/footer.php:34
#: templates/single/next-previous-pagination.php:17
msgid "Previous"
msgstr ""

#: classes/Tutor_Setup.php:621
#: classes/Tutor_Setup.php:650
msgid "Skip this step"
msgstr ""

#: classes/Tutor_Setup.php:625
#: templates/single/common/footer.php:40
#: templates/single/next-previous-pagination.php:23
#: assets/js/tutor-course-builder.js:25032
#: assets/js/tutor-course-builder.js:37311
msgid "Next"
msgstr ""

#: classes/Tutor_Setup.php:653
msgid "Finish Setup"
msgstr ""

#: classes/Tutor_Setup.php:680
msgctxt "tutor-wizard-greeting"
msgid "Hello "
msgstr ""

#: classes/Tutor_Setup.php:687
msgid "Welcome to Tutor LMS"
msgstr ""

#: classes/Tutor_Setup.php:695
msgid "Get started with an all-in-one platform to create, manage, and sell your courses effortlessly—trusted by over 100,000 eLearning websites worldwide."
msgstr ""

#: classes/Tutor_Setup.php:700
msgid "Let’s Start"
msgstr ""

#: classes/Tutor_Setup.php:707
msgid "I already know, skip it!"
msgstr ""

#: classes/Tutor_Setup.php:732
msgid "How do you want to set up?"
msgstr ""

#: classes/Tutor_Setup.php:733
msgid "Select the option that best fits your needs. You can change this setting anytime."
msgstr ""

#: classes/Tutor_Setup.php:746
msgid "Individual"
msgstr ""

#: classes/Tutor_Setup.php:747
msgid "Start as an independent educator and share your expertise."
msgstr ""

#: classes/Tutor_Setup.php:761
msgid "Marketplace"
msgstr ""

#: classes/Tutor_Setup.php:762
msgid "Build a marketplace that empowers others to sell courses online."
msgstr ""

#: classes/Tutor_Setup.php:770
msgid "Previous "
msgstr ""

#: classes/Tutor_Setup.php:776
msgid "Next "
msgstr ""

#: classes/Tutor_Setup.php:801
msgid "Tutor &rsaquo; Setup Wizard"
msgstr ""

#: classes/User.php:451
msgid "As membership is turned off, students and instructors will not be able to sign up. <strong>Press Enable</strong> or go to <strong>Settings > General > Membership</strong> and enable \"Anyone can register\"."
msgstr ""

#: classes/User.php:462
msgid "Dismiss"
msgstr ""

#: classes/User.php:571
msgid "User list fetched successfully!"
msgstr ""

#: classes/Utils.php:1825
msgid " left"
msgstr ""

#: classes/Utils.php:2603
#: templates/public-profile.php:149
msgid "Course Enrolled"
msgstr ""

#: classes/Utils.php:3049
msgid "Logout"
msgstr ""

#: classes/Utils.php:3073
msgid "Retrieve Password"
msgstr ""

#: classes/Utils.php:3190
#: classes/Withdraw_Requests_List.php:83
#: includes/translate-text.php:61
#: models/UserModel.php:95
#: views/pages/instructors.php:55
msgid "Approved"
msgstr ""

#: classes/Utils.php:3191
#: includes/translate-text.php:93
#: views/pages/instructors.php:56
msgid "Blocked"
msgstr ""

#: classes/Utils.php:3887
#: templates/shortcode/instructor-filter.php:85
msgid "Ratings"
msgstr ""

#: classes/Utils.php:3887
#: templates/dashboard/dashboard.php:356
msgid "Rating"
msgstr ""

#: classes/Utils.php:5153
#: assets/js/tutor-course-builder.js:20096
#: assets/js/tutor-course-builder.js:23904
msgid "True/False"
msgstr ""

#: classes/Utils.php:5158
msgid "Single Choice"
msgstr ""

#: classes/Utils.php:5163
#: assets/js/tutor-course-builder.js:20100
#: assets/js/tutor-course-builder.js:23909
msgid "Multiple Choice"
msgstr ""

#: classes/Utils.php:5168
msgid "Open Ended"
msgstr ""

#: classes/Utils.php:5173
msgid "Fill In The Blanks"
msgstr ""

#: classes/Utils.php:5178
#: assets/js/tutor-course-builder.js:20112
#: assets/js/tutor-course-builder.js:23924
msgid "Short Answer"
msgstr ""

#: classes/Utils.php:5183
#: assets/js/tutor-course-builder.js:20116
#: assets/js/tutor-course-builder.js:23929
msgid "Matching"
msgstr ""

#: classes/Utils.php:5188
#: assets/js/tutor-course-builder.js:20198
msgid "Image Matching"
msgstr ""

#: classes/Utils.php:5193
#: assets/js/tutor-course-builder.js:20120
#: assets/js/tutor-course-builder.js:23934
msgid "Image Answering"
msgstr ""

#: classes/Utils.php:5198
#: assets/js/tutor-course-builder.js:20124
#: assets/js/tutor-course-builder.js:23939
msgid "Ordering"
msgstr ""

#: classes/Utils.php:5537
msgid "All Levels"
msgstr ""

#: classes/Utils.php:5538
msgid "Beginner"
msgstr ""

#: classes/Utils.php:5539
msgid "Intermediate"
msgstr ""

#: classes/Utils.php:5540
msgid "Expert"
msgstr ""

#: classes/Utils.php:5889
msgid "Facebook"
msgstr ""

#: classes/Utils.php:5894
msgid "Twitter"
msgstr ""

#: classes/Utils.php:5899
msgid "Linkedin"
msgstr ""

#: classes/Utils.php:5904
msgid "Website"
msgstr ""

#: classes/Utils.php:5909
msgid "Github"
msgstr ""

#: classes/Utils.php:7454
#: includes/translate-text.php:69
#: models/OrderModel.php:268
#: templates/dashboard/purchase_history.php:213
#: assets/js/tutor-order-details.js:10616
msgid "Completed"
msgstr ""

#: classes/Utils.php:7458
msgid "In Progress"
msgstr ""

#: classes/Utils.php:7460
msgid "Not Taken"
msgstr ""

#: classes/Utils.php:7502
msgid "Cart"
msgstr ""

#: classes/Utils.php:7503
#: ecommerce/Settings.php:235
msgid "Checkout"
msgstr ""

#: classes/Utils.php:7685
msgid "Set Your Profile Photo"
msgstr ""

#: classes/Utils.php:7686
msgid "Set Your Bio"
msgstr ""

#: classes/Utils.php:7691
msgid "Set Withdraw Method"
msgstr ""

#: classes/Utils.php:9025
msgid "All Time"
msgstr ""

#: classes/Utils.php:9026
#: templates/dashboard/purchase_history.php:47
msgid "Today"
msgstr ""

#: classes/Utils.php:9027
msgid "Last 30 Days"
msgstr ""

#: classes/Utils.php:9028
msgid "Last 90 Days"
msgstr ""

#: classes/Utils.php:9029
msgid "Last 365 Days"
msgstr ""

#: classes/Utils.php:9141
#: assets/js/tutor-course-builder.js:32586
msgid "Course Info"
msgstr ""

#: classes/Utils.php:9145
#: classes/Utils.php:9486
#: templates/dashboard/reviews.php:28
#: templates/dashboard/reviews/given-reviews.php:27
msgid "Reviews"
msgstr ""

#: classes/Utils.php:9394
#: templates/single/lesson/sidebar_question_and_answer.php:43
msgid "No Data Available in this Section"
msgstr ""

#: classes/Utils.php:9395
msgid "No Data Found from your Search/Filter"
msgstr ""

#: classes/Utils.php:9420
#: views/elements/create-course-empty-state.php:25
msgid "Create Course"
msgstr ""

#: classes/Utils.php:9425
msgid "Create Bundle"
msgstr ""

#: classes/Utils.php:9430
#: templates/dashboard.php:48
#: templates/dashboard/dashboard.php:338
#: templates/dashboard/my-courses.php:75
msgid "My Courses"
msgstr ""

#: classes/Utils.php:9445
#: templates/dashboard/withdraw.php:57
msgid "Withdrawals"
msgstr ""

#: classes/Utils.php:9474
#: templates/dashboard/my-profile.php:39
msgid "My Profile"
msgstr ""

#: classes/Utils.php:9478
#: templates/dashboard/dashboard.php:166
#: templates/dashboard/enrolled-courses.php:20
msgid "Enrolled Courses"
msgstr ""

#: classes/Utils.php:9482
#: templates/dashboard/wishlist.php:23
#: templates/single/course/lead-info.php:101
msgid "Wishlist"
msgstr ""

#: classes/Utils.php:9490
#: templates/dashboard/my-quiz-attempts.php:35
msgid "My Quiz Attempts"
msgstr ""

#: classes/Utils.php:9496
#: templates/dashboard/purchase_history.php:77
#: templates/dashboard/purchase_history.php:98
msgid "Order History"
msgstr ""

#: classes/Utils.php:9503
#: templates/dashboard/question-answer.php:53
#: templates/single/course/enrolled/question_and_answer.php:114
#: views/pages/question_answer.php:50
msgid "Question & Answer"
msgstr ""

#: classes/Utils.php:9581
msgid "HTML 5 (mp4)"
msgstr ""

#: classes/Utils.php:9585
msgid "External URL"
msgstr ""

#: classes/Utils.php:9589
msgid "YouTube"
msgstr ""

#: classes/Utils.php:9593
msgid "Vimeo"
msgstr ""

#: classes/Utils.php:9597
msgid "Embedded"
msgstr ""

#: classes/Utils.php:9601
msgid "Shortcode"
msgstr ""

#: classes/Utils.php:10298
#: assets/js/tutor-addon-list.js:1511
#: assets/js/tutor-addon-list.js:1517
#: assets/js/tutor-coupon.js:3480
#: assets/js/tutor-coupon.js:3637
#: assets/js/tutor-coupon.js:9331
#: assets/js/tutor-coupon.js:9337
#: assets/js/tutor-course-builder.js:17112
#: assets/js/tutor-course-builder.js:17118
#: assets/js/tutor-front.js:686
#: assets/js/tutor-front.js:1293
#: assets/js/tutor-front.js:1297
#: assets/js/tutor-import-export.js:1517
#: assets/js/tutor-import-export.js:1523
#: assets/js/tutor-import-export.js:6596
#: assets/js/tutor-order-details.js:1514
#: assets/js/tutor-order-details.js:1520
#: assets/js/tutor-payment-settings.js:1516
#: assets/js/tutor-payment-settings.js:1522
#: assets/js/tutor-tax-settings.js:1516
#: assets/js/tutor-tax-settings.js:1522
#: assets/js/tutor.js:2963
msgid "Something went wrong"
msgstr ""

#: classes/Utils.php:10303
msgid "You are not authorzied to perform this action"
msgstr ""

#: classes/Utils.php:10304
msgid "Nonce not matched. Action failed!"
msgstr ""

#: classes/Utils.php:10305
msgid "Invalid request"
msgstr ""

#: classes/Utils.php:10306
msgid "Authentication failed"
msgstr ""

#: classes/Utils.php:10307
msgid "Authorization required"
msgstr ""

#: classes/Utils.php:10308
msgid "Requested resource not found"
msgstr ""

#: classes/Utils.php:10309
msgid "Internal server error"
msgstr ""

#: classes/Utils.php:10310
msgid "Request timed out"
msgstr ""

#: classes/Utils.php:10311
msgid "Access to this resource is forbidden"
msgstr ""

#: classes/Utils.php:10312
msgid "HTTP method not allowed"
msgstr ""

#: classes/Utils.php:10313
msgid "Too many requests"
msgstr ""

#: classes/Utils.php:10314
msgid "Validation error"
msgstr ""

#: classes/Utils.php:10315
msgid "Database operation failed"
msgstr ""

#: classes/Utils.php:10316
msgid "Requested file not found"
msgstr ""

#: classes/Utils.php:10317
msgid "Unsupported media type"
msgstr ""

#: classes/Utils.php:10363
msgid "Gutenberg"
msgstr ""

#: classes/Utils.php:10378
msgid "Droip"
msgstr ""

#: classes/Utils.php:10396
msgid "Elementor"
msgstr ""

#: classes/Utils.php:10424
#: assets/js/tutor-course-builder.js:12397
#: assets/js/tutor-course-builder.js:12566
msgid "Classic Editor"
msgstr ""

#. translators: %s: timestamp
#: classes/Utils.php:10516
msgid "%s left"
msgstr ""

#: classes/Withdraw.php:60
msgid "Bank Transfer"
msgstr ""

#: classes/Withdraw.php:62
msgid "Get your payment directly into your bank account"
msgstr ""

#: classes/Withdraw.php:67
msgid "Account Name"
msgstr ""

#: classes/Withdraw.php:71
msgid "Account Number"
msgstr ""

#: classes/Withdraw.php:75
msgid "Bank Name"
msgstr ""

#: classes/Withdraw.php:79
msgid "IBAN"
msgstr ""

#: classes/Withdraw.php:83
msgid "BIC / SWIFT"
msgstr ""

#: classes/Withdraw.php:90
msgid "E-Check"
msgstr ""

#: classes/Withdraw.php:95
msgid "Your Physical Address"
msgstr ""

#: classes/Withdraw.php:96
msgid "We will send you an E-Check to this address directly."
msgstr ""

#: classes/Withdraw.php:102
#: ecommerce/Ecommerce.php:228
msgid "PayPal"
msgstr ""

#: classes/Withdraw.php:107
msgid "PayPal E-Mail Address"
msgstr ""

#: classes/Withdraw.php:108
msgid "We will use this email address to send the money to your Paypal account"
msgstr ""

#: classes/Withdraw.php:219
msgid "Withdrawal information saved!"
msgstr ""

#. translators: 1: total pending withdraw request 2: available for withdraw
#: classes/Withdraw.php:246
msgid "You have total %1$s pending withdraw request. You can't make more than %2$s withdraw request at a time"
msgstr ""

#: classes/Withdraw.php:258
msgid "Please save withdraw method "
msgstr ""

#. translators: 1: strong tag start 2: min withdrawal amount 3: strong tag end
#: classes/Withdraw.php:264
msgid "Minimum withdrawal amount is %1$s %2$s %3$s "
msgstr ""

#: classes/Withdraw.php:269
msgid "Insufficient balance."
msgstr ""

#: classes/Withdraw.php:315
msgid "Withdrawal Request Sent!"
msgstr ""

#: classes/Withdraw_Requests_List.php:95
#: includes/translate-text.php:65
msgid "Rejected"
msgstr ""

#: classes/WooCommerce.php:271
msgid "Since monetization is currently disabled, your courses are set to free. Enable Tutor LMS monetization to start selling your courses."
msgstr ""

#: classes/WooCommerce.php:472
msgid "For Tutor"
msgstr ""

#: classes/WooCommerce.php:473
msgid "This checkmark ensure that you will sell a specific course via this product."
msgstr ""

#: ecommerce/AdminMenu.php:33
#: ecommerce/AdminMenu.php:50
#: ecommerce/OrderController.php:144
msgid "Orders"
msgstr ""

#: ecommerce/AdminMenu.php:52
#: ecommerce/CouponController.php:132
msgid "Coupons"
msgstr ""

#: ecommerce/BillingController.php:94
msgid "Billing"
msgstr ""

#: ecommerce/BillingController.php:184
msgid "Failed to save billing info"
msgstr ""

#: ecommerce/BillingController.php:190
msgid "Billing info saved successfully"
msgstr ""

#: ecommerce/Cart/BaseCart.php:38
msgid "Failed to add item to the cart"
msgstr ""

#: ecommerce/Cart/EddCart.php:37
msgid "Downloadable item not found"
msgstr ""

#: ecommerce/Cart/EddCart.php:42
#: ecommerce/Cart/NativeCart.php:55
#: ecommerce/Cart/WooCart.php:59
msgid "Item already exists in cart"
msgstr ""

#: ecommerce/Cart/WooCart.php:54
msgid "Woocommerce Product not found"
msgstr ""

#: ecommerce/CartController.php:121
#: templates/loop/add-to-cart-edd.php:26
#: templates/single/course/add-to-cart-tutor.php:88
#: views/pages/addons.php:85
msgid "Buy Now"
msgstr ""

#: ecommerce/CartController.php:226
#: ecommerce/CartController.php:283
msgid "Invalid course id."
msgstr ""

#: ecommerce/CartController.php:236
msgid "The course is already in the cart."
msgstr ""

#: ecommerce/CartController.php:246
msgid "The course was added to the cart successfully."
msgstr ""

#: ecommerce/CartController.php:255
msgid "Failed to add to cart."
msgstr ""

#: ecommerce/CartController.php:300
msgid "The course was removed successfully."
msgstr ""

#: ecommerce/CartController.php:306
msgid "Course remove failed."
msgstr ""

#: ecommerce/CheckoutController.php:200
msgid "You're already enrolled in this course."
msgstr ""

#: ecommerce/CheckoutController.php:201
msgid "Start learning!"
msgstr ""

#: ecommerce/CheckoutController.php:226
msgid "You are already enrolled in the following courses. Please remove those from your cart and continue."
msgstr ""

#: ecommerce/CheckoutController.php:228
msgid "You are already enrolled in the following course. Please remove that from your cart and continue."
msgstr ""

#: ecommerce/CheckoutController.php:231
#: templates/loop/add-to-cart-tutor.php:31
#: templates/loop/course-in-cart.php:18
#: templates/single/course/add-to-cart-tutor.php:81
#: templates/single/course/add-to-cart-woocommerce.php:26
#: assets/js/tutor-front.js:3139
msgid "View Cart"
msgstr ""

#: ecommerce/CheckoutController.php:582
msgid "Invalid cart items"
msgstr ""

#: ecommerce/CheckoutController.php:589
#: ecommerce/CheckoutController.php:1026
msgid "Billing information update failed!"
msgstr ""

#: ecommerce/CheckoutController.php:597
#: ecommerce/CheckoutController.php:1035
msgid "Billing info save failed!"
msgstr ""

#: ecommerce/CheckoutController.php:604
msgid "Select a payment method"
msgstr ""

#. translators: wp error message.
#: ecommerce/CheckoutController.php:637
msgctxt "guest checkout"
msgid "Order placement failed. %s"
msgstr ""

#: ecommerce/CheckoutController.php:675
#: ecommerce/CheckoutController.php:978
msgid "Failed to place order!"
msgstr ""

#. translators: %s: tax rate
#: ecommerce/CheckoutController.php:765
msgid "Tax (%s)"
msgstr ""

#: ecommerce/CheckoutController.php:815
#: ecommerce/CheckoutController.php:1056
msgid "Order not found!"
msgstr ""

#: ecommerce/CheckoutController.php:986
msgid "Your order has been placed successfully!"
msgstr ""

#: ecommerce/CheckoutController.php:1060
msgid "Invalid order ID!"
msgstr ""

#: ecommerce/CouponController.php:175
msgid "Add items first"
msgstr ""

#: ecommerce/CouponController.php:212
msgid "Coupon code already exists!"
msgstr ""

#: ecommerce/CouponController.php:238
msgid "Coupon created successfully!"
msgstr ""

#: ecommerce/CouponController.php:241
msgid "Failed to create!"
msgstr ""

#: ecommerce/CouponController.php:303
msgid "Coupon updated successfully!"
msgstr ""

#: ecommerce/CouponController.php:306
msgid "Failed to update!"
msgstr ""

#: ecommerce/CouponController.php:347
msgid "Coupon application list retrieved successfully!"
msgstr ""

#: ecommerce/CouponController.php:542
#: ecommerce/OrderController.php:890
msgid "No items selected for the bulk action."
msgstr ""

#: ecommerce/CouponController.php:549
msgid "Invalid bulk action."
msgstr ""

#: ecommerce/CouponController.php:567
msgid "Coupon updated successfully."
msgstr ""

#: ecommerce/CouponController.php:569
msgid "Failed to update coupon."
msgstr ""

#: ecommerce/CouponController.php:590
msgid "Invalid coupon ID"
msgstr ""

#: ecommerce/CouponController.php:599
msgid "Coupon delete successfully."
msgstr ""

#: ecommerce/CouponController.php:601
msgid "Failed to delete coupon."
msgstr ""

#: ecommerce/CouponController.php:621
msgid "Coupon code is required"
msgstr ""

#: ecommerce/CouponController.php:631
#: models/CouponModel.php:1223
msgid "Coupon not found"
msgstr ""

#: ecommerce/CouponController.php:655
msgid "Coupon retrieved successfully"
msgstr ""

#: ecommerce/CouponController.php:776
msgid "Coupon usage is disabled"
msgstr ""

#: ecommerce/CouponController.php:802
msgid "Coupon applied successfully"
msgstr ""

#: ecommerce/Ecommerce.php:126
msgid "Native"
msgstr ""

#. translators: %s: payment gateway
#: ecommerce/Ecommerce.php:214
msgid "%s payment method is not configured properly. Please contact with site administrator!"
msgstr ""

#: ecommerce/EmailController.php:530
#: ecommerce/EmailController.php:594
#: ecommerce/EmailController.php:654
msgid "New order placed"
msgstr ""

#: ecommerce/EmailController.php:533
#: ecommerce/EmailController.php:597
#: ecommerce/EmailController.php:657
msgid "New order emails are sent to chosen recipient(s) when a new order is received."
msgstr ""

#: ecommerce/EmailController.php:534
msgid "Your order has been received! 🎉"
msgstr ""

#: ecommerce/EmailController.php:535
msgid "Your order has been received!"
msgstr ""

#: ecommerce/EmailController.php:549
#: ecommerce/EmailController.php:611
msgid "Hi"
msgstr ""

#: ecommerce/EmailController.php:550
msgid "Thank you for your order. We've received your order successfully, and it is now being processed."
msgstr ""

#: ecommerce/EmailController.php:551
msgid "Below are the details of your order:"
msgstr ""

#: ecommerce/EmailController.php:552
#: ecommerce/EmailController.php:581
#: ecommerce/EmailController.php:614
#: ecommerce/EmailController.php:642
#: ecommerce/EmailController.php:670
msgid "Order ID:"
msgstr ""

#: ecommerce/EmailController.php:553
#: ecommerce/EmailController.php:584
#: ecommerce/EmailController.php:615
#: ecommerce/EmailController.php:671
msgid "Order Date:"
msgstr ""

#: ecommerce/EmailController.php:554
#: ecommerce/EmailController.php:585
#: ecommerce/EmailController.php:645
#: ecommerce/EmailController.php:672
msgid "Total Amount:"
msgstr ""

#: ecommerce/EmailController.php:557
#: ecommerce/EmailController.php:588
msgid "We will let you know once your order has been completed and is ready for access."
msgstr ""

#: ecommerce/EmailController.php:561
#: ecommerce/EmailController.php:623
#: ecommerce/EmailController.php:679
msgid "Order status updated"
msgstr ""

#: ecommerce/EmailController.php:564
#: ecommerce/EmailController.php:626
#: ecommerce/EmailController.php:682
msgid "Order status update emails are sent to chosen recipient(s) whenever a order status updated."
msgstr ""

#: ecommerce/EmailController.php:565
msgid "Your Order Status Has Been Updated to {order_status} "
msgstr ""

#: ecommerce/EmailController.php:566
msgid "Your Order Status Has Been Updated to {order_status}"
msgstr ""

#: ecommerce/EmailController.php:579
#: ecommerce/EmailController.php:640
#: ecommerce/EmailController.php:697
msgid "Hi {user_name},"
msgstr ""

#: ecommerce/EmailController.php:580
msgid "We're reaching out to let you know that your order status has been updated to {order_status}. We understand the importance of keeping you informed at every step of the way. Below is a summary of your order:"
msgstr ""

#: ecommerce/EmailController.php:582
#: ecommerce/EmailController.php:643
msgid "Order Status:"
msgstr ""

#: ecommerce/EmailController.php:583
#: templates/dashboard/reviews.php:93
msgid "Course:"
msgstr ""

#: ecommerce/EmailController.php:598
msgid "A New Student Has Enrolled in Your Course! 🎉"
msgstr ""

#: ecommerce/EmailController.php:599
msgid "A New Student Has Enrolled in Your Course!"
msgstr ""

#: ecommerce/EmailController.php:612
msgid "We're excited to let you know that a new student has just enrolled in one of your courses! Here are the course details:"
msgstr ""

#: ecommerce/EmailController.php:613
msgid "Student Name:"
msgstr ""

#: ecommerce/EmailController.php:616
msgid "Payment Status:"
msgstr ""

#: ecommerce/EmailController.php:619
#: ecommerce/EmailController.php:648
#: ecommerce/EmailController.php:675
#: ecommerce/EmailController.php:706
msgid "Please review the order and ensure everything is in place for the student's access to the course. Thank you."
msgstr ""

#: ecommerce/EmailController.php:627
#: ecommerce/EmailController.php:628
msgid "Instructor Notice: Your Student's Order Status is Now {order_status}"
msgstr ""

#: ecommerce/EmailController.php:641
msgid "We'd like to update you on your course enrollment status. One of your students has an order that has been updated to {order_status}. Here are the details:"
msgstr ""

#: ecommerce/EmailController.php:644
msgid "Course Name:"
msgstr ""

#: ecommerce/EmailController.php:658
#: ecommerce/EmailController.php:659
msgid "A New Order Has Been Placed on Your Platform!"
msgstr ""

#: ecommerce/EmailController.php:669
msgid "Below are the order details:"
msgstr ""

#: ecommerce/EmailController.php:683
#: ecommerce/EmailController.php:684
msgid "An Order's Status Has Been Updated to {order_status}"
msgstr ""

#: ecommerce/EmailController.php:698
msgid "We're reaching out to let you know that the order status of {student_name} has been updated to {order_status}. Here is the summary of the order:"
msgstr ""

#: ecommerce/EmailController.php:699
#: templates/dashboard/purchase_history.php:103
msgid "Order ID"
msgstr ""

#: ecommerce/EmailController.php:700
msgid "Order Date"
msgstr ""

#: ecommerce/EmailController.php:701
msgid "Order Status"
msgstr ""

#: ecommerce/EmailController.php:702
#: templates/dashboard/dashboard.php:350
msgid "Course Name"
msgstr ""

#: ecommerce/EmailController.php:703
msgid "Total Amount"
msgstr ""

#: ecommerce/OptionKeys.php:56
msgid "Optional"
msgstr ""

#: ecommerce/OptionKeys.php:57
msgid "Required"
msgstr ""

#: ecommerce/OptionKeys.php:58
msgid "Don't Include"
msgstr ""

#. translators: %s: username
#: ecommerce/OrderActivitiesController.php:131
msgid "Order marked as paid"
msgstr ""

#. translators: %s: username
#: ecommerce/OrderActivitiesController.php:131
msgid "Order marked as paid by %s"
msgstr ""

#: ecommerce/OrderController.php:203
msgid "Invalid payment status"
msgstr ""

#: ecommerce/OrderController.php:243
#: templates/ecommerce/checkout-details.php:140
msgid "Sale discount"
msgstr ""

#: ecommerce/OrderController.php:305
msgid "Order ID is required"
msgstr ""

#: ecommerce/OrderController.php:315
msgid "Order not found"
msgstr ""

#: ecommerce/OrderController.php:322
msgid "Order retrieved successfully"
msgstr ""

#: ecommerce/OrderController.php:366
msgid "Failed to update order payment status"
msgstr ""

#: ecommerce/OrderController.php:372
msgid "Order payment status successfully updated"
msgstr ""

#: ecommerce/OrderController.php:417
msgid "Failed to cancel order status"
msgstr ""

#: ecommerce/OrderController.php:425
msgid "Order successfully canceled"
msgstr ""

#: ecommerce/OrderController.php:455
msgid "Invalid refund amount provided"
msgstr ""

#: ecommerce/OrderController.php:461
msgid "Order refunded by "
msgstr ""

#: ecommerce/OrderController.php:469
msgid "Refund amount exceeded."
msgstr ""

#: ecommerce/OrderController.php:484
msgid "Partially refunded by "
msgstr ""

#: ecommerce/OrderController.php:541
msgid "Order refund successful"
msgstr ""

#: ecommerce/OrderController.php:547
msgid "Order refunded successfully, but pending payment gateway issuance."
msgstr ""

#: ecommerce/OrderController.php:552
msgid "Failed to make refund"
msgstr ""

#: ecommerce/OrderController.php:610
msgid "Failed to make a comment"
msgstr ""

#: ecommerce/OrderController.php:616
msgid "Order comment successful added"
msgstr ""

#: ecommerce/OrderController.php:685
#: ecommerce/OrderController.php:696
msgid "Failed to add a discount"
msgstr ""

#: ecommerce/OrderController.php:693
msgid "Order discount successful added"
msgstr ""

#: ecommerce/OrderController.php:935
msgid "Order updated successfully."
msgstr ""

#: ecommerce/OrderController.php:937
msgid "Failed to update order."
msgstr ""

#: ecommerce/PaymentHandler.php:129
msgid "Order Placement Success"
msgstr ""

#: ecommerce/PaymentHandler.php:131
msgid "Order Placement Failed"
msgstr ""

#: ecommerce/Settings.php:93
msgid "Cart Page"
msgstr ""

#: ecommerce/Settings.php:96
msgid "Select the page you wish to set as the cart page."
msgstr ""

#: ecommerce/Settings.php:102
msgid "Checkout Page"
msgstr ""

#: ecommerce/Settings.php:105
msgid "Select the page to be used as the checkout page."
msgstr ""

#: ecommerce/Settings.php:123
msgid "Choose the currency for transactions."
msgstr ""

#: ecommerce/Settings.php:129
msgid "Currency Position"
msgstr ""

#: ecommerce/Settings.php:133
msgid "Set the position of the currency symbol."
msgstr ""

#: ecommerce/Settings.php:138
msgid "Thousand Separator"
msgstr ""

#: ecommerce/Settings.php:141
msgid "Specify the thousand separator."
msgstr ""

#: ecommerce/Settings.php:146
msgid "Decimal Separator"
msgstr ""

#: ecommerce/Settings.php:149
msgid "Specify the decimal separator."
msgstr ""

#: ecommerce/Settings.php:154
msgid "Number of Decimals"
msgstr ""

#: ecommerce/Settings.php:156
msgid "Set the number of decimal places."
msgstr ""

#: ecommerce/Settings.php:184
#: assets/js/tutor-payment-settings.js:12788
msgid "Payment Methods"
msgstr ""

#: ecommerce/Settings.php:199
msgid "Payment Settings"
msgstr ""

#: ecommerce/Settings.php:211
#: assets/js/tutor-tax-settings.js:11077
msgid "Taxes"
msgstr ""

#: ecommerce/Settings.php:226
msgid "Tax Settings"
msgstr ""

#: ecommerce/Settings.php:241
msgid "Checkout Configuration"
msgstr ""

#: ecommerce/Settings.php:242
msgid "Customize your checkout process to suit your preferences."
msgstr ""

#: ecommerce/Settings.php:249
msgid "Enable Coupon Code"
msgstr ""

#: ecommerce/Settings.php:251
msgid "Allow users to apply the coupon code during checkout."
msgstr ""

#: ecommerce/Settings.php:256
msgid "Enable \"Buy Now\" Button"
msgstr ""

#: ecommerce/Settings.php:258
msgid "Allow users to purchase courses directly without adding them to the cart."
msgstr ""

#: ecommerce/Settings.php:343
msgid "Left"
msgstr ""

#: ecommerce/Settings.php:344
msgid "Right"
msgstr ""

#: ecommerce/Settings.php:497
msgid "Test"
msgstr ""

#: ecommerce/Settings.php:498
#: templates/single/course/course-topics.php:121
msgid "Live"
msgstr ""

#. translators: %s: time difference
#: helpers/DateTimeHelper.php:199
#: templates/dashboard/question-answer/answers.php:41
#: templates/dashboard/question-answer/answers.php:78
#: templates/single/course/enrolled/announcements.php:41
#: templates/single/course/reviews-loop.php:35
#: views/pages/answer.php:62
#: views/pages/answer.php:107
msgid "%s ago"
msgstr ""

#. translators: %s: time difference
#: helpers/DateTimeHelper.php:201
msgid "%s from now"
msgstr ""

#: helpers/PluginInstaller.php:114
msgid "An error occurred while fetching the plugin download link."
msgstr ""

#: helpers/ValidationHelper.php:67
msgid " is required"
msgstr ""

#: helpers/ValidationHelper.php:74
msgid " is not numeric"
msgstr ""

#. translators: %1$s: field name, %2$d: value
#: helpers/ValidationHelper.php:82
msgid "%1$s need to be greater than %2$d"
msgstr ""

#. translators: %1$s: field name, %2$d: value
#: helpers/ValidationHelper.php:90
msgid "%1$s need to be less than %2$d"
msgstr ""

#. translators: %s: field name
#: helpers/ValidationHelper.php:97
msgid "%s is not valid email"
msgstr ""

#. translators: %1$s: field name, %2$d: value
#: helpers/ValidationHelper.php:104
msgid "%1$s minimum length is %2$d"
msgstr ""

#. translators: %1$s: field name, %2$d: value
#: helpers/ValidationHelper.php:111
msgid "%1$s maximum length is %2$d"
msgstr ""

#: helpers/ValidationHelper.php:118
msgid " extension is not valid"
msgstr ""

#: helpers/ValidationHelper.php:125
msgid " string is not valid"
msgstr ""

#: helpers/ValidationHelper.php:131
msgid " is not boolean"
msgstr ""

#: helpers/ValidationHelper.php:137
msgid " is not an array"
msgstr ""

#: helpers/ValidationHelper.php:144
msgid " invalid date format"
msgstr ""

#: helpers/ValidationHelper.php:155
msgid " record not found"
msgstr ""

#: helpers/ValidationHelper.php:164
msgid " user does not exist"
msgstr ""

#: includes/ecommerce-functions.php:26
msgid "Course added to cart"
msgstr ""

#: includes/tinymce_translate.php:19
#: assets/js/tutor-course-builder.js:34608
#: assets/js/tutor-payment-settings.js:11023
msgid "Tutor ShortCode"
msgstr ""

#: includes/tinymce_translate.php:20
#: assets/js/tutor-course-builder.js:34612
#: assets/js/tutor-payment-settings.js:11027
msgid "Student Registration Form"
msgstr ""

#: includes/tinymce_translate.php:21
#: assets/js/tutor-course-builder.js:34617
#: assets/js/tutor-payment-settings.js:11032
msgid "Instructor Registration Form"
msgstr ""

#: includes/tinymce_translate.php:22
msgctxt "tinyMCE button courses"
msgid "Courses"
msgstr ""

#: includes/tinymce_translate.php:23
#: assets/js/tutor-course-builder.js:34625
#: assets/js/tutor-payment-settings.js:11040
msgid "Courses Shortcode"
msgstr ""

#: includes/tinymce_translate.php:24
#: assets/js/tutor-course-builder.js:34629
#: assets/js/tutor-payment-settings.js:11044
msgid "Course id, separate by (,) comma"
msgstr ""

#: includes/tinymce_translate.php:25
#: assets/js/tutor-course-builder.js:34634
#: assets/js/tutor-payment-settings.js:11049
msgid "Exclude Course IDS"
msgstr ""

#: includes/tinymce_translate.php:26
#: assets/js/tutor-course-builder.js:34639
#: assets/js/tutor-payment-settings.js:11054
msgid "Category IDS"
msgstr ""

#: includes/tinymce_translate.php:27
msgctxt "tinyMCE button order by"
msgid "Order By :"
msgstr ""

#: includes/tinymce_translate.php:28
msgctxt "tinyMCE button order"
msgid "Order :"
msgstr ""

#: includes/tinymce_translate.php:29
#: assets/js/tutor-course-builder.js:34680
#: assets/js/tutor-payment-settings.js:11095
msgid "Count"
msgstr ""

#: includes/translate-text.php:28
#: restapi/RestAuth.php:408
msgid "Read"
msgstr ""

#: includes/translate-text.php:31
msgid "Unread"
msgstr ""

#: includes/translate-text.php:34
#: views/qna/qna-single.php:71
msgid "Important"
msgstr ""

#: includes/translate-text.php:37
msgid "Archived"
msgstr ""

#: includes/translate-text.php:49
#: views/quiz/attempt-details.php:722
msgid "Correct"
msgstr ""

#: includes/translate-text.php:57
msgid "Wrong"
msgstr ""

#: includes/translate-text.php:73
#: models/OrderModel.php:267
#: assets/js/tutor-order-details.js:10612
msgid "Incomplete"
msgstr ""

#: includes/translate-text.php:81
#: includes/translate-text.php:85
#: includes/translate-text.php:97
#: models/OrderModel.php:269
#: models/UserModel.php:95
#: templates/dashboard/purchase_history.php:229
#: assets/js/tutor-order-details.js:10620
msgid "Cancelled"
msgstr ""

#: includes/translate-text.php:89
#: templates/single/assignment/content.php:177
#: templates/single/course/course-topics.php:119
msgid "Expired"
msgstr ""

#: includes/translate-text.php:105
msgid "Trial"
msgstr ""

#: includes/translate-text.php:121
#: models/OrderModel.php:287
msgid "Partially Refunded"
msgstr ""

#: includes/translate-text.php:125
#: models/OrderModel.php:286
#: templates/dashboard/purchase_history.php:225
#: assets/js/tutor-order-details.js:9918
msgid "Refunded"
msgstr ""

#: includes/translate-text.php:129
#: models/OrderModel.php:284
#: assets/js/tutor-order-details.js:9922
msgid "Unpaid"
msgstr ""

#: includes/translate-text.php:133
#: models/OrderModel.php:283
#: templates/course-filter/filters.php:16
#: assets/js/tutor-course-builder.js:10825
#: assets/js/tutor-order-details.js:9906
msgid "Paid"
msgstr ""

#: includes/translate-text.php:137
#: models/OrderModel.php:285
#: templates/single/assignment/content.php:487
#: assets/js/tutor-admin.js:1024
#: assets/js/tutor-admin.js:1057
#: assets/js/tutor-admin.js:1548
#: assets/js/tutor-admin.js:1702
#: assets/js/tutor-admin.js:1708
#: assets/js/tutor-admin.js:1810
#: assets/js/tutor-admin.js:1817
#: assets/js/tutor-admin.js:1858
#: assets/js/tutor-admin.js:1865
#: assets/js/tutor-admin.js:1908
#: assets/js/tutor-admin.js:1915
#: assets/js/tutor-admin.js:1996
#: assets/js/tutor-admin.js:2003
#: assets/js/tutor-admin.js:2169
#: assets/js/tutor-admin.js:2176
#: assets/js/tutor-admin.js:2227
#: assets/js/tutor-admin.js:2234
#: assets/js/tutor-admin.js:2287
#: assets/js/tutor-admin.js:2294
#: assets/js/tutor-admin.js:3187
#: assets/js/tutor-front.js:1523
#: assets/js/tutor-front.js:1951
#: assets/js/tutor-front.js:2105
#: assets/js/tutor-front.js:2111
#: assets/js/tutor-front.js:2436
#: assets/js/tutor-front.js:2967
#: assets/js/tutor-front.js:2974
#: assets/js/tutor-front.js:3150
#: assets/js/tutor-front.js:3157
#: assets/js/tutor-front.js:3219
#: assets/js/tutor-front.js:3226
#: assets/js/tutor-front.js:3500
#: assets/js/tutor-front.js:3542
#: assets/js/tutor-front.js:3549
#: assets/js/tutor-order-details.js:9910
#: assets/js/tutor-setup.js:1517
#: assets/js/tutor-setup.js:1524
#: assets/js/tutor.js:2546
#: assets/js/tutor.js:2553
#: assets/js/tutor.js:3045
#: assets/js/tutor.js:3048
#: assets/js/tutor.js:3149
#: assets/js/tutor.js:3154
#: assets/js/tutor.js:3161
msgid "Failed"
msgstr ""

#: includes/translate-text.php:141
msgid "Partially Paid"
msgstr ""

#: includes/translate-text.php:164
msgctxt "true/false question options"
msgid "True"
msgstr ""

#: includes/translate-text.php:167
msgctxt "true/false question options"
msgid "False"
msgstr ""

#: includes/translate-text.php:173
#: templates/single/assignment/content.php:61
#: templates/single/assignment/content.php:147
#: templates/single/quiz/top.php:76
#: assets/js/tutor-course-builder.js:10584
msgid "Day"
msgid_plural "Days"
msgstr[0] ""
msgstr[1] ""

#: includes/translate-text.php:179
#: templates/single/assignment/content.php:61
#: templates/single/assignment/content.php:148
#: templates/single/quiz/top.php:75
#: assets/js/tutor-course-builder.js:10582
msgid "Hour"
msgid_plural "Hours"
msgstr[0] ""
msgstr[1] ""

#: includes/translate-text.php:185
#: templates/single/assignment/content.php:61
#: templates/single/quiz/top.php:74
msgid "Minute"
msgid_plural "Minutes"
msgstr[0] ""
msgstr[1] ""

#: includes/translate-text.php:191
#: templates/single/quiz/top.php:73
msgid "Second"
msgstr ""

#: includes/translate-text.php:196
msgctxt "Week name"
msgid "Monday"
msgstr ""

#: includes/translate-text.php:199
msgctxt "Week name"
msgid "Tuesday"
msgstr ""

#: includes/translate-text.php:202
msgctxt "Week name"
msgid "Wednesday"
msgstr ""

#: includes/translate-text.php:205
msgctxt "Week name"
msgid "Thursday"
msgstr ""

#: includes/translate-text.php:208
msgctxt "Week name"
msgid "Friday"
msgstr ""

#: includes/translate-text.php:211
msgctxt "Week name"
msgid "Saturday"
msgstr ""

#: includes/translate-text.php:214
msgctxt "Week name"
msgid "Sunday"
msgstr ""

#: includes/translate-text.php:219
#: includes/translate-text.php:255
msgctxt "Month name"
msgid "January"
msgstr ""

#: includes/translate-text.php:222
#: includes/translate-text.php:258
msgctxt "Month name"
msgid "February"
msgstr ""

#: includes/translate-text.php:225
#: includes/translate-text.php:261
msgctxt "Month name"
msgid "March"
msgstr ""

#: includes/translate-text.php:228
#: includes/translate-text.php:264
msgctxt "Month name"
msgid "April"
msgstr ""

#: includes/translate-text.php:231
#: includes/translate-text.php:267
msgctxt "Month name"
msgid "May"
msgstr ""

#: includes/translate-text.php:234
#: includes/translate-text.php:270
msgctxt "Month name"
msgid "June"
msgstr ""

#: includes/translate-text.php:237
#: includes/translate-text.php:273
msgctxt "Month name"
msgid "July"
msgstr ""

#: includes/translate-text.php:240
#: includes/translate-text.php:276
msgctxt "Month name"
msgid "August"
msgstr ""

#: includes/translate-text.php:243
#: includes/translate-text.php:279
msgctxt "Month name"
msgid "September"
msgstr ""

#: includes/translate-text.php:246
#: includes/translate-text.php:282
msgctxt "Month name"
msgid "October"
msgstr ""

#: includes/translate-text.php:249
#: includes/translate-text.php:285
msgctxt "Month name"
msgid "November"
msgstr ""

#: includes/translate-text.php:252
#: includes/translate-text.php:288
msgctxt "Month name"
msgid "December"
msgstr ""

#: includes/tutor-general-functions.php:210
msgid "Search Course Category. ex. Design, Development, Business"
msgstr ""

#: includes/tutor-general-functions.php:211
msgid "Select a category"
msgstr ""

#: includes/tutor-general-functions.php:255
msgid "Search Course Tags. ex. Design, Development, Business"
msgstr ""

#: includes/tutor-general-functions.php:256
msgid "Select a tag"
msgstr ""

#: includes/tutor-general-functions.php:498
#: includes/tutor-general-functions.php:505
msgid "file not exists"
msgstr ""

#: includes/tutor-general-functions.php:1663
#: templates/single/course/add-to-cart-tutor.php:63
msgid "Incl. tax"
msgstr ""

#: includes/tutor-template-functions.php:55
msgid "The file you are trying to load does not exist in your theme or Tutor LMS plugin location. If you are extending the Tutor LMS plugin, please create a php file here: "
msgstr ""

#: includes/tutor-template-functions.php:1377
msgid "h"
msgstr ""

#: includes/tutor-template-functions.php:1377
#: assets/js/tutor-course-builder.js:19800
msgid "hour"
msgstr ""

#: includes/tutor-template-functions.php:1378
msgid "m"
msgstr ""

#: includes/tutor-template-functions.php:1378
msgid "minute"
msgstr ""

#: includes/tutor-template-functions.php:1379
msgid "s"
msgstr ""

#: includes/tutor-template-functions.php:1379
msgid "second"
msgstr ""

#: includes/tutor-template-functions.php:1615
msgid "You don't have the right to edit this course"
msgstr ""

#: includes/tutor-template-functions.php:1616
msgid "Please make sure you are logged in to correct account"
msgstr ""

#: models/CouponModel.php:220
#: assets/js/tutor-coupon.js:5250
msgid "All courses and bundles"
msgstr ""

#: models/CouponModel.php:221
#: assets/js/tutor-coupon.js:5244
msgid "All courses"
msgstr ""

#: models/CouponModel.php:222
#: assets/js/tutor-coupon.js:5247
msgid "All bundles"
msgstr ""

#: models/CouponModel.php:223
#: assets/js/tutor-coupon.js:5256
msgid "Specific courses"
msgstr ""

#: models/CouponModel.php:224
#: assets/js/tutor-coupon.js:5259
msgid "Specific bundles"
msgstr ""

#: models/CouponModel.php:225
#: assets/js/tutor-coupon.js:5262
msgid "Specific category"
msgstr ""

#: models/CouponModel.php:271
msgid "no_minimum"
msgstr ""

#: models/CouponModel.php:272
msgid "minimum_purchase"
msgstr ""

#: models/CouponModel.php:273
msgid "minimum_quantity"
msgstr ""

#: models/CouponModel.php:286
#: views/pages/ecommerce/coupon-list.php:101
#: assets/js/tutor-coupon.js:7589
msgid "Code"
msgstr ""

#: models/CouponModel.php:287
#: views/pages/ecommerce/coupon-list.php:149
#: assets/js/tutor-coupon.js:7592
#: assets/js/tutor-coupon.js:8050
msgid "Automatic"
msgstr ""

#: models/CouponModel.php:1224
msgid "Coupon expired"
msgstr ""

#: models/CouponModel.php:1225
msgid "Coupon invalid"
msgstr ""

#: models/CouponModel.php:1226
msgid "Coupon usage limit exceeded"
msgstr ""

#: models/CouponModel.php:1227
msgid "Coupon user usage limit exceeded"
msgstr ""

#. translators: %s - Minimum purchase amount (e.g., $50).
#: models/CouponModel.php:1229
msgid "This coupon requires a minimum purchase %s"
msgstr ""

#. translators: 1 - Quantity number, 2 - 'quantities' or 'quantity'.
#: models/CouponModel.php:1232
msgid "This coupon requires minimum purchase of %1$d %2$s"
msgstr ""

#. translators: 1 - Quantity number, 2 - 'quantities' or 'quantity'.
#: models/CouponModel.php:1232
msgid "quantities"
msgstr ""

#. translators: 1 - Quantity number, 2 - 'quantities' or 'quantity'.
#: models/CouponModel.php:1232
msgid "quantity"
msgstr ""

#. translators: %s - Reason or context where coupon is not applicable.
#: models/CouponModel.php:1235
msgid "Coupon not applicable %s"
msgstr ""

#. translators: %s - List or name of applicable items.
#: models/CouponModel.php:1238
msgid "This coupon is only applicable to %s"
msgstr ""

#: models/OrderActivitiesModel.php:76
#: assets/js/tutor-import-export.js:8523
msgid "History"
msgstr ""

#: models/OrderActivitiesModel.php:77
#: assets/js/tutor-order-details.js:9835
#: assets/js/tutor-order-details.js:9973
#: assets/js/tutor-order-details.js:10133
msgid "Refund"
msgstr ""

#: models/OrderActivitiesModel.php:78
msgid "Comment"
msgstr ""

#: models/OrderModel.php:367
msgid "Failed to insert order items"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1425
msgid "Order marked as cancelled"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1425
msgid "Order marked as cancelled by %s"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1428
msgid "Order marked as completed"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1428
msgid "Order marked as completed by %s"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1431
msgid "Order marked as incomplete"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1431
msgid "Order marked as incomplete by %s"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1434
msgid "Order marked as trash"
msgstr ""

#. translators: %s: username
#: models/OrderModel.php:1434
msgid "Order marked as trash by %s"
msgstr ""

#: models/OrderModel.php:1803
msgid "Payment Is Pending Due To Gateway Processing."
msgstr ""

#: models/OrderModel.php:1817
#: templates/dashboard/purchase_history.php:284
msgid "Pay"
msgstr ""

#: restapi/RestAuth.php:244
msgid "Invalid meta id"
msgstr ""

#: restapi/RestAuth.php:252
msgid "API keys permanently revoked"
msgstr ""

#: restapi/RestAuth.php:254
msgid "API keys revoke failed, please try again."
msgstr ""

#: restapi/RestAuth.php:383
#: templates/dashboard/announcements/details.php:45
#: templates/dashboard/reviews/given-reviews.php:76
#: templates/single/assignment/content.php:531
#: views/fragments/announcement-list.php:174
#: views/fragments/announcement-list.php:322
#: views/options/field-types/toggle_switch_button_thumb.php:28
#: views/pages/course-list.php:447
#: views/pages/ecommerce/coupon-list.php:169
#: views/pages/ecommerce/order-list.php:186
#: views/pages/instructors.php:219
#: assets/js/tutor-course-builder.js:9924
#: assets/js/tutor-course-builder.js:20945
#: assets/js/tutor-course-builder.js:21376
#: assets/js/tutor-course-builder.js:21971
#: assets/js/tutor-course-builder.js:22648
#: assets/js/tutor-course-builder.js:25872
#: assets/js/tutor-course-builder.js:26426
#: assets/js/tutor-tax-settings.js:8219
msgid "Edit"
msgstr ""

#: restapi/RestAuth.php:387
msgid "Revoke"
msgstr ""

#: restapi/REST_Author.php:61
msgid "Author details retrieved successfully"
msgstr ""

#: restapi/REST_Author.php:70
msgid "Author not found"
msgstr ""

#: restapi/REST_Course.php:184
msgid "Course retrieved successfully"
msgstr ""

#: restapi/REST_Course.php:193
msgid "Course not found"
msgstr ""

#: restapi/REST_Course.php:216
msgid "Course detail retrieved successfully"
msgstr ""

#: restapi/REST_Course.php:223
msgid "Detail not found for given ID"
msgstr ""

#: restapi/REST_Course.php:282
msgid "Categories field is not an array"
msgstr ""

#: restapi/REST_Course.php:286
msgid "Tags field is not an array"
msgstr ""

#: restapi/REST_Course.php:330
msgid "Course contents retrieved successfully"
msgstr ""

#: restapi/REST_Course.php:338
msgid "Contents for this course with the given course id not found"
msgstr ""

#: restapi/REST_Course_Announcement.php:64
msgid "Announcement retrieved successfully"
msgstr ""

#: restapi/REST_Course_Announcement.php:73
msgid "Announcement not found for given ID"
msgstr ""

#: restapi/REST_Lesson.php:61
msgid "topic_id is required"
msgstr ""

#: restapi/REST_Lesson.php:109
msgid "Lesson retrieved successfully"
msgstr ""

#: restapi/REST_Lesson.php:118
msgid "Lesson not found for the given topic ID"
msgstr ""

#: restapi/REST_Quiz.php:106
#: restapi/REST_Quiz.php:196
msgid "Quiz not found for given ID"
msgstr ""

#: restapi/REST_Quiz.php:140
#: restapi/REST_Quiz.php:187
msgid "Quiz retrieved successfully"
msgstr ""

#: restapi/REST_Quiz.php:261
msgid "Question retrieved successfully"
msgstr ""

#: restapi/REST_Quiz.php:270
msgid "Question not found for given ID"
msgstr ""

#: restapi/REST_Quiz.php:330
msgid "Quiz attempts retrieved successfully"
msgstr ""

#: restapi/REST_Quiz.php:339
msgid "Quiz attempts not found for given ID"
msgstr ""

#: restapi/REST_Rating.php:75
msgid "Course rating retrieved successfully"
msgstr ""

#: restapi/REST_Rating.php:84
msgid "Rating not found for given ID"
msgstr ""

#: restapi/REST_Topic.php:58
msgid "course_id is required"
msgstr ""

#: restapi/REST_Topic.php:73
msgid "Topic retrieved successfully"
msgstr ""

#: restapi/REST_Topic.php:81
msgid "Topic not found for given course ID"
msgstr ""

#: templates/course-embed.php:95
#: templates/loop/course-author.php:32
#: templates/loop/meta.php:53
#: templates/single/course/enrolled/announcements.php:34
#: templates/single/course/lead-info.php:72
msgid "By"
msgstr ""

#: templates/course-embed.php:99
#: templates/loop/course-author.php:36
#: templates/loop/meta.php:57
msgid "In"
msgstr ""

#: templates/course-embed.php:124
#: templates/email/order_new_email_to_admin.php:32
#: templates/email/order_new_email_to_students.php:32
#: templates/email/order_new_email_to_teachers.php:33
#: templates/email/order_updated_email_to_admin.php:33
#: templates/email/order_updated_email_to_students.php:33
#: templates/email/order_updated_email_to_teachers.php:33
msgid "View Details"
msgstr ""

#: templates/course-filter/course-archive-filter-bar.php:26
msgid "Release Date (newest first)"
msgstr ""

#: templates/course-filter/course-archive-filter-bar.php:29
msgid "Release Date (oldest first)"
msgstr ""

#: templates/course-filter/course-archive-filter-bar.php:32
msgid "Course Title (a-z)"
msgstr ""

#: templates/course-filter/course-archive-filter-bar.php:35
msgid "Course Title (z-a)"
msgstr ""

#: templates/course-filter/filters.php:15
#: templates/dashboard/my-courses.php:190
#: templates/loop/course-in-cart.php:19
#: templates/single/course/course-entry-box.php:255
#: templates/single/course/wc-price-html.php:20
#: views/pages/course-list.php:378
#: assets/js/tutor-course-builder.js:10822
#: assets/js/tutor-course-builder.js:10828
msgid "Free"
msgstr ""

#: templates/course-filter/filters.php:38
#: views/elements/filters.php:214
#: assets/js/tutor-course-builder.js:7713
#: assets/js/tutor-course-builder.js:7942
msgid "Search"
msgstr ""

#: templates/course-filter/filters.php:123
#: templates/dashboard/purchase_history.php:112
#: views/pages/course-list.php:267
#: assets/js/tutor-coupon.js:3619
#: assets/js/tutor-course-builder.js:10015
msgid "Price"
msgstr ""

#: templates/course-filter/filters.php:144
msgid "Clear All Filters"
msgstr ""

#: templates/dashboard.php:60
msgid "Menu"
msgstr ""

#: templates/dashboard.php:98
msgid "Hello"
msgstr ""

#: templates/dashboard.php:123
msgid "Become an instructor"
msgstr ""

#: templates/dashboard.php:153
msgid "Your Application is pending as of"
msgstr ""

#: templates/dashboard/announcements.php:73
#: views/fragments/announcement-list.php:380
#: views/pages/announcements.php:103
msgid "Create Announcement"
msgstr ""

#: templates/dashboard/announcements.php:76
#: views/pages/announcements.php:106
msgid "Notify all students of your course"
msgstr ""

#: templates/dashboard/announcements.php:82
#: views/pages/announcements.php:112
msgid "Add New Announcement"
msgstr ""

#: templates/dashboard/announcements.php:102
#: templates/dashboard/announcements/create.php:41
#: templates/dashboard/announcements/update.php:42
#: templates/dashboard/assignments.php:51
#: templates/dashboard/elements/filters.php:39
#: views/elements/filters.php:112
#: views/fragments/announcement-list.php:64
msgid "No course found"
msgstr ""

#: templates/dashboard/announcements.php:108
#: templates/dashboard/assignments.php:57
#: templates/dashboard/elements/filters.php:44
#: templates/dashboard/question-answer.php:72
#: views/elements/filters.php:185
msgid "Sort By"
msgstr ""

#: templates/dashboard/announcements.php:110
#: templates/dashboard/assignments.php:59
#: templates/dashboard/elements/filters.php:46
#: views/elements/filters.php:192
msgid "ASC"
msgstr ""

#: templates/dashboard/announcements.php:111
#: templates/dashboard/assignments.php:60
#: templates/dashboard/elements/filters.php:47
#: views/elements/filters.php:189
msgid "DESC"
msgstr ""

#: templates/dashboard/announcements.php:116
#: templates/dashboard/purchase_history.php:109
#: templates/dashboard/reviews.php:57
#: templates/single/assignment/content.php:444
#: views/elements/filters.php:200
#: views/fragments/announcement-list.php:245
#: views/fragments/announcement-list.php:249
#: views/pages/course-list.php:274
#: views/pages/ecommerce/order-list.php:95
#: views/pages/tools/settings-log.php:42
#: views/quiz/contexts.php:16
#: views/quiz/contexts.php:66
#: assets/js/tutor-admin.js:1780
#: assets/js/tutor-import-export.js:8496
msgid "Date"
msgstr ""

#: templates/dashboard/announcements.php:122
#: templates/dashboard/assignments.php:71
#: templates/dashboard/elements/filters.php:57
#: views/elements/course-filters.php:105
#: views/elements/course-filters.php:181
#: views/elements/filters.php:207
#: assets/js/tutor.js:1578
#: assets/js/tutor.js:1727
#: assets/js/tutor.js:1868
#: assets/js/tutor.js:1903
msgid "Loading..."
msgstr ""

#: templates/dashboard/announcements/create.php:18
msgid "Create New Announcement"
msgstr ""

#: templates/dashboard/announcements/create.php:31
#: templates/dashboard/announcements/update.php:32
#: views/fragments/announcement-list.php:54
msgid "Select Course"
msgstr ""

#: templates/dashboard/announcements/create.php:47
#: templates/dashboard/announcements/update.php:48
#: views/fragments/announcement-list.php:71
msgid "Announcement Title"
msgstr ""

#: templates/dashboard/announcements/create.php:49
#: templates/dashboard/announcements/update.php:50
#: views/fragments/announcement-list.php:73
msgid "Announcement title"
msgstr ""

#: templates/dashboard/announcements/create.php:53
#: templates/dashboard/announcements/update.php:54
#: views/fragments/announcement-list.php:78
msgid "Summary"
msgstr ""

#: templates/dashboard/announcements/create.php:55
#: templates/dashboard/announcements/update.php:56
#: views/fragments/announcement-list.php:80
msgid "Summary..."
msgstr ""

#: templates/dashboard/announcements/details.php:35
#: views/pages/course-list.php:106
msgid "Publish Date"
msgstr ""

#: templates/dashboard/announcements/details.php:44
#: templates/dashboard/my-courses.php:295
#: templates/dashboard/reviews/given-reviews.php:81
#: templates/dashboard/settings/profile.php:102
#: views/fragments/announcement-list.php:171
#: views/fragments/announcement-list.php:328
#: views/options/field-types/media.php:43
#: views/pages/tools/settings-log.php:81
#: views/qna/qna-single.php:81
#: views/qna/qna-table.php:157
#: assets/js/tutor-admin.js:1699
#: assets/js/tutor-admin.js:1704
#: assets/js/tutor-admin.js:1775
#: assets/js/tutor-course-builder.js:9957
#: assets/js/tutor-course-builder.js:10151
#: assets/js/tutor-course-builder.js:21418
#: assets/js/tutor-course-builder.js:22014
#: assets/js/tutor-course-builder.js:22690
#: assets/js/tutor-course-builder.js:23817
#: assets/js/tutor-course-builder.js:25926
#: assets/js/tutor-course-builder.js:26019
#: assets/js/tutor-course-builder.js:26472
#: assets/js/tutor-course-builder.js:26527
#: assets/js/tutor-course-builder.js:28777
#: assets/js/tutor-course-builder.js:29003
#: assets/js/tutor-course-builder.js:37996
#: assets/js/tutor-front.js:2102
#: assets/js/tutor-front.js:2107
#: assets/js/tutor-import-export.js:8516
#: assets/js/tutor-payment-settings.js:5813
#: assets/js/tutor-tax-settings.js:7978
#: assets/js/tutor-tax-settings.js:8229
msgid "Delete"
msgstr ""

#: templates/dashboard/announcements/update.php:18
msgid "Update Announcement"
msgstr ""

#: templates/dashboard/announcements/update.php:63
#: views/pages/withdraw_requests.php:103
#: views/pages/withdraw_requests.php:257
#: views/quiz/instructor-feedback.php:38
#: assets/js/tutor-course-builder.js:10441
#: assets/js/tutor-course-builder.js:18340
#: assets/js/tutor-course-builder.js:19671
#: assets/js/tutor-course-builder.js:30934
#: assets/js/tutor-course-builder.js:30939
msgid "Update"
msgstr ""

#: templates/dashboard/assignments.php:65
#: templates/dashboard/elements/filters.php:51
msgid "Create Date"
msgstr ""

#: templates/dashboard/assignments.php:84
msgid "Assignment Name"
msgstr ""

#: templates/dashboard/assignments.php:87
#: templates/single/assignment/content.php:447
#: views/quiz/contexts.php:20
#: views/quiz/contexts.php:70
#: views/quiz/header-context/course-single-previous-attempts.php:44
msgid "Total Marks"
msgstr ""

#: templates/dashboard/assignments.php:90
msgid "Total Submit"
msgstr ""

#: templates/dashboard/assignments.php:123
#: templates/dashboard/assignments/submitted.php:111
#: templates/instructor/cover.php:43
#: templates/instructor/default.php:37
#: templates/instructor/minimal-horizontal.php:35
#: templates/instructor/minimal.php:39
#: templates/instructor/portrait-horizontal.php:40
#: views/fragments/announcement-list.php:310
#: views/pages/students.php:152
#: views/quiz/attempt-table.php:177
#: views/quiz/contexts.php:25
#: assets/js/tutor-coupon.js:8092
msgid "Details"
msgstr ""

#: templates/dashboard/assignments/review.php:19
msgid "Sorry, but you are looking for something that isn't here."
msgstr ""

#: templates/dashboard/assignments/review.php:25
msgid "Assignments submission not found or not completed"
msgstr ""

#: templates/dashboard/assignments/review.php:42
#: templates/dashboard/assignments/submitted.php:37
#: templates/single/quiz/parts/question.php:200
#: views/qna/qna-single.php:56
#: views/quiz/header-context/backend-dashboard-students-attempts.php:20
#: views/quiz/header-context/course-single-previous-attempts.php:17
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:14
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:15
msgid "Back"
msgstr ""

#: templates/dashboard/assignments/review.php:60
#: templates/dashboard/assignments/submitted.php:80
#: templates/dashboard/question-answer.php:61
#: templates/dashboard/reviews.php:54
#: templates/public-profile.php:137
#: views/elements/search-filter.php:43
#: views/qna/contexts.php:16
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:31
#: assets/js/tutor-order-details.js:10276
msgid "Student"
msgstr ""

#: templates/dashboard/assignments/review.php:69
msgid "Submitted Date"
msgstr ""

#: templates/dashboard/assignments/review.php:81
msgid "Assignment Description:"
msgstr ""

#: templates/dashboard/assignments/review.php:93
msgid "Attach assignment file(s)"
msgstr ""

#: templates/dashboard/assignments/review.php:110
#: templates/global/attachments.php:30
#: templates/single/assignment/content.php:556
#: views/fragments/attachments.php:30
#: assets/js/tutor-course-builder.js:34995
msgid "Size"
msgstr ""

#: templates/dashboard/assignments/review.php:131
msgid "Evaluation"
msgstr ""

#: templates/dashboard/assignments/review.php:132
msgid "Assignment evaluated"
msgstr ""

#: templates/dashboard/assignments/review.php:138
msgid "Your Points"
msgstr ""

#: templates/dashboard/assignments/review.php:141
msgid "Evaluate mark can not be greater than total mark"
msgstr ""

#. translators: %s: max mark
#: templates/dashboard/assignments/review.php:145
msgid "Evaluate this assignment out of %s"
msgstr ""

#: templates/dashboard/assignments/review.php:151
#: templates/dashboard/reviews.php:60
msgid "Feedback"
msgstr ""

#: templates/dashboard/assignments/review.php:160
msgid "Evaluate this submission"
msgstr ""

#: templates/dashboard/assignments/submitted.php:25
#: templates/dashboard/assignments/submitted.php:107
#: assets/js/tutor-front.js:478
msgid "No Limit"
msgstr ""

#: templates/dashboard/assignments/submitted.php:50
msgid "Assignment Submission Period"
msgstr ""

#: templates/dashboard/assignments/submitted.php:54
#: templates/dashboard/assignments/submitted.php:89
#: assets/js/tutor-course-builder.js:18499
msgid "Total Points"
msgstr ""

#: templates/dashboard/assignments/submitted.php:58
msgid "Pass Points"
msgstr ""

#: templates/dashboard/assignments/submitted.php:66
msgid "Sort By:"
msgstr ""

#: templates/dashboard/assignments/submitted.php:68
msgid "Latest"
msgstr ""

#: templates/dashboard/assignments/submitted.php:69
msgid "Oldest"
msgstr ""

#: templates/dashboard/assignments/submitted.php:83
msgid "Submission Date"
msgstr ""

#: templates/dashboard/assignments/submitted.php:86
msgid "Submission Deadline"
msgstr ""

#: templates/dashboard/assignments/submitted.php:92
#: templates/single/assignment/content.php:456
#: views/quiz/contexts.php:24
#: views/quiz/contexts.php:75
#: views/quiz/contexts.php:100
msgid "Result"
msgstr ""

#: templates/dashboard/assignments/submitted.php:106
msgid "Evaluate"
msgstr ""

#: templates/dashboard/dashboard.php:36
#: templates/dashboard/notifications/profile-completion.php:20
msgid "Complete Your Profile"
msgstr ""

#: templates/dashboard/dashboard.php:59
msgid "Please complete profile"
msgstr ""

#: templates/dashboard/dashboard.php:61
msgid "You are almost done"
msgstr ""

#: templates/dashboard/dashboard.php:63
msgid "Thanks for completing your profile"
msgstr ""

#: templates/dashboard/dashboard.php:123
msgid "Click Here"
msgstr ""

#: templates/dashboard/dashboard.php:179
#: templates/dashboard/enrolled-courses.php:21
msgid "Active Courses"
msgstr ""

#: templates/dashboard/dashboard.php:192
#: templates/dashboard/enrolled-courses.php:22
msgid "Completed Courses"
msgstr ""

#: templates/dashboard/dashboard.php:208
msgid "Total Students"
msgstr ""

#: templates/dashboard/dashboard.php:221
#: views/pages/instructors.php:127
msgid "Total Courses"
msgstr ""

#: templates/dashboard/dashboard.php:234
msgid "Total Earnings"
msgstr ""

#: templates/dashboard/dashboard.php:256
msgid "In Progress Courses"
msgstr ""

#: templates/dashboard/dashboard.php:290
msgid "Completed Lessons:"
msgstr ""

#: templates/dashboard/dashboard.php:295
#: templates/dashboard/elements/pagination.php:57
#: views/elements/pagination.php:20
#: assets/js/tutor-coupon.js:2565
#: assets/js/tutor-import-export.js:5096
msgid "of"
msgstr ""

#: templates/dashboard/dashboard.php:299
msgid "lesson"
msgid_plural "lessons"
msgstr[0] ""
msgstr[1] ""

#: templates/dashboard/dashboard.php:340
msgid "View All"
msgstr ""

#: templates/dashboard/dashboard.php:353
msgid "Enrolled"
msgstr ""

#: templates/dashboard/elements/pagination.php:53
#: views/elements/pagination.php:16
#: assets/js/tutor-coupon.js:2549
#: assets/js/tutor-import-export.js:5080
msgid "Page"
msgstr ""

#: templates/dashboard/enrolled-courses.php:69
#: templates/dashboard/settings/nav-bar.php:54
#: templates/single/course/enrolled/nav.php:34
#: views/elements/navbar.php:67
#: views/options/template/tab.php:42
msgid "More"
msgstr ""

#: templates/dashboard/instructor/apply_for_instructor.php:22
msgid "Instructor Application"
msgstr ""

#: templates/dashboard/instructor/apply_for_instructor.php:29
msgid "Do you want to start your career as an instructor?"
msgstr ""

#: templates/dashboard/instructor/apply_for_instructor.php:35
msgid "Tell us your qualifications, show us your passion, and begin teaching with us!"
msgstr ""

#: templates/dashboard/instructor/apply_for_instructor.php:41
msgid "Apply Now"
msgstr ""

#: templates/dashboard/instructor/logged-in.php:20
msgid "You have been rejected from being an instructor."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:38
msgid "Your application will be reviewed and the results will be sent to you by email."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:40
msgid "Your application has been accepted. Further necessary details have been sent to your registered email account."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:42
msgid "You have been blocked from being an instructor."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:53
msgid "Instructor Application Received"
msgstr ""

#: templates/dashboard/instructor/logged-in.php:62
msgid "Thank you for registering as an instructor! "
msgstr ""

#: templates/dashboard/instructor/logged-in.php:64
msgid "Congratulations! You are now registered as an instructor."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:66
msgid "Unfortunately, your instructor status has been removed."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:75
msgid "We've received your application, and we will review it soon. Please hang tight!"
msgstr ""

#: templates/dashboard/instructor/logged-in.php:77
msgid "Start building your first course today and let your eLearning journey begin."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:79
msgid "Please contact the site administrator for further information."
msgstr ""

#: templates/dashboard/instructor/logged-in.php:86
msgid "Go to Dashboard"
msgstr ""

#: templates/dashboard/instructor/registration.php:19
#: templates/dashboard/registration.php:18
msgid "Oooh! Access Denied"
msgstr ""

#: templates/dashboard/instructor/registration.php:20
#: templates/dashboard/registration.php:19
msgid "You do not have access to this area of the application. Please refer to your system  administrator."
msgstr ""

#: templates/dashboard/instructor/registration.php:22
#: templates/dashboard/registration.php:21
msgid "Go to Home"
msgstr ""

#: templates/dashboard/instructor/registration.php:58
#: templates/dashboard/instructor/registration.php:61
#: templates/dashboard/my-profile.php:29
#: templates/dashboard/registration.php:60
#: templates/dashboard/registration.php:63
#: templates/dashboard/settings/profile.php:126
#: templates/dashboard/settings/profile.php:128
#: templates/ecommerce/billing-form-fields.php:35
#: templates/ecommerce/billing-form-fields.php:37
#: views/pages/add_new_instructor.php:41
#: views/pages/add_new_instructor.php:47
#: views/pages/instructors.php:277
msgid "First Name"
msgstr ""

#: templates/dashboard/instructor/registration.php:68
#: templates/dashboard/instructor/registration.php:71
#: templates/dashboard/my-profile.php:30
#: templates/dashboard/registration.php:70
#: templates/dashboard/registration.php:73
#: templates/dashboard/settings/profile.php:133
#: templates/dashboard/settings/profile.php:135
#: templates/ecommerce/billing-form-fields.php:44
#: templates/ecommerce/billing-form-fields.php:46
#: views/pages/add_new_instructor.php:55
#: views/pages/add_new_instructor.php:61
#: views/pages/instructors.php:285
msgid "Last Name"
msgstr ""

#: templates/dashboard/instructor/registration.php:83
#: templates/dashboard/instructor/registration.php:86
#: templates/dashboard/registration.php:83
#: templates/dashboard/registration.php:86
#: templates/dashboard/settings/profile.php:142
#: views/pages/add_new_instructor.php:68
#: views/pages/add_new_instructor.php:74
msgid "User Name"
msgstr ""

#: templates/dashboard/instructor/registration.php:93
#: templates/dashboard/instructor/registration.php:96
#: templates/dashboard/registration.php:93
#: templates/dashboard/registration.php:96
#: views/pages/add_new_instructor.php:81
#: views/pages/add_new_instructor.php:87
msgid "E-Mail"
msgstr ""

#: templates/dashboard/instructor/registration.php:108
#: templates/dashboard/instructor/registration.php:111
#: templates/dashboard/registration.php:108
#: templates/dashboard/registration.php:111
#: templates/dashboard/settings/nav-bar.php:25
#: templates/login-form.php:63
#: templates/template-part/form-retrieve-password.php:36
#: views/pages/add_new_instructor.php:107
#: views/pages/add_new_instructor.php:113
#: views/pages/instructors.php:328
#: assets/js/tutor-course-builder.js:11214
#: assets/js/tutor-course-builder.js:28938
msgid "Password"
msgstr ""

#: templates/dashboard/instructor/registration.php:129
#: templates/dashboard/registration.php:130
#: views/pages/add_new_instructor.php:120
msgid "Password confirmation"
msgstr ""

#: templates/dashboard/instructor/registration.php:134
#: templates/dashboard/registration.php:135
#: views/pages/add_new_instructor.php:126
msgid "Password Confirmation"
msgstr ""

#: templates/dashboard/instructor/registration.php:160
#: templates/dashboard/registration.php:161
msgid "By signing up, I agree with the website's"
msgstr ""

#: templates/dashboard/instructor/registration.php:160
#: templates/dashboard/registration.php:161
msgid "Terms and Conditions"
msgstr ""

#: templates/dashboard/instructor/registration.php:165
msgid "Register as instructor"
msgstr ""

#: templates/dashboard/logged-in.php:14
msgid "You are already logged in"
msgstr ""

#: templates/dashboard/my-courses.php:63
#: views/pages/course-list.php:192
#: assets/js/tutor-course-builder.js:7308
#: assets/js/tutor-course-builder.js:30927
#: assets/js/tutor-course-builder.js:30934
msgid "Schedule"
msgstr ""

#: templates/dashboard/my-courses.php:126
msgid "Co-author"
msgstr ""

#: templates/dashboard/my-courses.php:183
#: assets/js/tutor-order-details.js:10331
msgid "Plan:"
msgstr ""

#: templates/dashboard/my-courses.php:227
#: templates/single/lesson/comment.php:90
#: templates/single/password-protected.php:55
#: views/pages/tools/manage-tokens.php:199
#: views/qna/qna-new.php:36
#: assets/js/tutor-course-builder.js:30918
msgid "Submit"
msgstr ""

#: templates/dashboard/my-courses.php:247
#: assets/js/tutor-course-builder.js:9940
#: assets/js/tutor-course-builder.js:21390
#: assets/js/tutor-course-builder.js:21986
#: assets/js/tutor-course-builder.js:22662
#: assets/js/tutor-course-builder.js:23800
#: assets/js/tutor-course-builder.js:25897
#: assets/js/tutor-course-builder.js:26446
msgid "Duplicate"
msgstr ""

#: templates/dashboard/my-courses.php:266
msgid "Move to Draft"
msgstr ""

#: templates/dashboard/my-courses.php:285
msgid "Cancel Submission"
msgstr ""

#: templates/dashboard/my-courses.php:321
msgid "Delete This Course?"
msgstr ""

#: templates/dashboard/my-courses.php:322
msgid "Are you sure you want to delete this course permanently from the site? Please confirm your choice."
msgstr ""

#: templates/dashboard/my-courses.php:329
#: templates/dashboard/reviews/given-reviews.php:140
#: views/fragments/announcement-list.php:220
#: views/qna/qna-single.php:198
#: views/qna/qna-table.php:185
msgid "Yes, Delete This"
msgstr ""

#: templates/dashboard/my-profile.php:28
#: views/pages/students.php:92
msgid "Registration Date"
msgstr ""

#: templates/dashboard/my-profile.php:31
#: views/pages/instructors.php:295
msgid "Username"
msgstr ""

#: templates/dashboard/my-profile.php:33
#: templates/dashboard/settings/profile.php:149
#: templates/dashboard/settings/profile.php:151
#: views/pages/add_new_instructor.php:94
#: views/pages/add_new_instructor.php:100
#: views/pages/instructors.php:303
msgid "Phone Number"
msgstr ""

#: templates/dashboard/my-profile.php:34
#: templates/dashboard/settings/profile.php:158
msgid "Skill/Occupation"
msgstr ""

#: templates/dashboard/my-profile.php:35
#: templates/public-profile.php:179
msgid "Biography"
msgstr ""

#: templates/dashboard/notifications/profile-completion.php:22
msgid "Complete your profile so people can know more about you! Go to Profile"
msgstr ""

#: templates/dashboard/notifications/profile-completion.php:27
msgid "Set Your"
msgstr ""

#: templates/dashboard/notifications/profile-completion.php:40
msgid "% Complete"
msgstr ""

#: templates/dashboard/notifications/profile-completion.php:42
msgid "You are almost done!"
msgstr ""

#: templates/dashboard/purchase_history.php:52
msgid "Monthly"
msgstr ""

#: templates/dashboard/purchase_history.php:57
msgid "Yearly"
msgstr ""

#: templates/dashboard/purchase_history.php:106
#: views/pages/ecommerce/coupon-list.php:89
#: views/pages/ecommerce/order-list.php:92
#: views/pages/instructors.php:119
#: assets/js/tutor-course-builder.js:19691
#: assets/js/tutor-import-export.js:6558
msgid "Name"
msgstr ""

#: templates/dashboard/purchase_history.php:119
#: templates/ecommerce/checkout.php:90
#: assets/js/tutor-order-details.js:10190
msgid "Payment Method"
msgstr ""

#: templates/dashboard/question-answer/answers.php:20
#: views/pages/answer.php:18
msgid "Answer"
msgstr ""

#: templates/dashboard/question-answer/answers.php:110
#: templates/single/lesson/comments-loop.php:38
#: templates/single/lesson/comments-loop.php:88
#: views/qna/contexts.php:18
#: views/qna/qna-single.php:144
#: views/qna/qna-single.php:179
#: views/qna/qna-table.php:128
msgid "Reply"
msgstr ""

#: templates/dashboard/registration.php:166
msgid "Register"
msgstr ""

#: templates/dashboard/reviews.php:34
#: templates/dashboard/reviews/given-reviews.php:32
msgid "Received"
msgstr ""

#: templates/dashboard/reviews.php:40
#: templates/dashboard/reviews/given-reviews.php:35
msgid "Given"
msgstr ""

#: templates/dashboard/reviews/edit-review-form.php:22
#: templates/dashboard/reviews/given-reviews.php:115
#: templates/single/course/reviews.php:182
msgid "write a review"
msgstr ""

#: templates/dashboard/reviews/edit-review-form.php:25
#: templates/dashboard/reviews/given-reviews.php:122
#: views/modal/review.php:42
msgid "Update Review"
msgstr ""

#: templates/dashboard/reviews/given-reviews.php:59
msgid "Course: "
msgstr ""

#: templates/dashboard/reviews/given-reviews.php:102
#: views/modal/review.php:22
msgid "How would you rate this course?"
msgstr ""

#: templates/dashboard/reviews/given-reviews.php:103
#: views/modal/review.php:23
msgid "Select Rating"
msgstr ""

#: templates/dashboard/reviews/given-reviews.php:137
msgid "Do You Want to Delete This Review?"
msgstr ""

#: templates/dashboard/reviews/given-reviews.php:138
msgid "Are you sure you want to delete this review permanently from the site? Please confirm your choice."
msgstr ""

#: templates/dashboard/settings/nav-bar.php:20
msgid "Profile"
msgstr ""

#: templates/dashboard/settings/nav-bar.php:35
msgid "Social Profile"
msgstr ""

#: templates/dashboard/settings/profile.php:78
msgid "Update Cover Photo"
msgstr ""

#: templates/dashboard/settings/profile.php:78
msgid "Upload Cover Photo"
msgstr ""

#: templates/dashboard/settings/profile.php:84
msgid "Profile Photo Size"
msgstr ""

#: templates/dashboard/settings/profile.php:84
msgid "200x200"
msgstr ""

#: templates/dashboard/settings/profile.php:84
#: templates/dashboard/settings/profile.php:85
msgid "pixels"
msgstr ""

#: templates/dashboard/settings/profile.php:85
msgid "Cover Photo Size"
msgstr ""

#: templates/dashboard/settings/profile.php:85
msgid "700x430"
msgstr ""

#: templates/dashboard/settings/profile.php:86
msgid "Saving..."
msgstr ""

#: templates/dashboard/settings/profile.php:99
msgid "Upload Photo"
msgstr ""

#: templates/dashboard/settings/profile.php:160
msgid "UX Designer"
msgstr ""

#: templates/dashboard/settings/profile.php:166
#: assets/js/tutor-course-builder.js:5386
#: assets/js/tutor-course-builder.js:13691
msgid "Timezone"
msgstr ""

#: templates/dashboard/settings/profile.php:179
#: views/pages/add_new_instructor.php:133
#: views/pages/instructors.php:351
msgid "Bio"
msgstr ""

#: templates/dashboard/settings/profile.php:191
msgid "Display name publicly as"
msgstr ""

#: templates/dashboard/settings/profile.php:204
msgid "The display name is shown in all public fields, such as the author name, instructor name, student name, and name that will be printed on the certificate."
msgstr ""

#: templates/dashboard/settings/profile.php:213
#: templates/dashboard/settings/social-profile.php:46
msgid "Update Profile"
msgstr ""

#: templates/dashboard/settings/reset-password.php:28
#: templates/dashboard/settings/reset-password.php:29
msgid "Current Password"
msgstr ""

#: templates/dashboard/settings/reset-password.php:38
msgid "New Password"
msgstr ""

#: templates/dashboard/settings/reset-password.php:46
#: templates/dashboard/settings/reset-password.php:76
msgid "Type Password"
msgstr ""

#: templates/dashboard/settings/reset-password.php:68
msgid "Re-type New Password"
msgstr ""

#: templates/dashboard/settings/reset-password.php:89
msgid "Reset Password"
msgstr ""

#: templates/dashboard/settings/social-profile.php:21
msgid "Social Profile Link"
msgstr ""

#: templates/dashboard/settings/withdraw-settings.php:28
msgid "Select a withdraw method"
msgstr ""

#: templates/dashboard/settings/withdraw-settings.php:53
msgid "Min withdraw"
msgstr ""

#: templates/dashboard/settings/withdraw-settings.php:146
msgid "Save Withdrawal Account"
msgstr ""

#: templates/dashboard/settings/withdraw-settings.php:158
msgid "There's no Withdrawal method selected yet! To select a Withdraw method, please contact the Site Admin."
msgstr ""

#: templates/dashboard/withdraw.php:38
msgid "Please contact the site administrator for more information."
msgstr ""

#: templates/dashboard/withdraw.php:39
msgid "Withdrawal request is pending for approval, please hold tight."
msgstr ""

#. translators: %s: current balance
#: templates/dashboard/withdraw.php:72
msgid "Current Balance is %s"
msgstr ""

#. translators: %s: available balance
#: templates/dashboard/withdraw.php:79
msgid "You have %s ready to withdraw now"
msgstr ""

#. translators: %s: available balance
#: templates/dashboard/withdraw.php:82
msgid "You have %s and this is insufficient balance to withdraw"
msgstr ""

#. translators: %s: total pending withdrawal
#: templates/dashboard/withdraw.php:90
msgid "Total Pending Withdrawal %s"
msgstr ""

#: templates/dashboard/withdraw.php:101
#: templates/dashboard/withdraw.php:147
msgid "Withdrawal Request"
msgstr ""

#. translators: %s: Withdraw Method Name
#: templates/dashboard/withdraw.php:118
msgid "The preferred payment method is selected as %s. "
msgstr ""

#. translators: %1$s: a tag start, %2$s: a tag end
#: templates/dashboard/withdraw.php:121
msgid "You can change your %1$s Withdraw Preference %2$s"
msgstr ""

#: templates/dashboard/withdraw.php:148
msgid "Please check your transaction notification on your connected withdrawal method"
msgstr ""

#: templates/dashboard/withdraw.php:152
msgid "Withdrawable Balance"
msgstr ""

#: templates/dashboard/withdraw.php:157
msgid "Selected Payment Method"
msgstr ""

#: templates/dashboard/withdraw.php:172
#: templates/dashboard/withdraw.php:230
#: views/pages/ecommerce/coupon-list.php:143
#: views/pages/withdraw_requests.php:97
#: views/pages/withdraw_requests.php:245
#: assets/js/tutor-coupon.js:5240
#: assets/js/tutor-order-details.js:9282
#: assets/js/tutor-order-details.js:9776
msgid "Amount"
msgstr ""

#: templates/dashboard/withdraw.php:180
msgid "Minimum withdraw amount is"
msgstr ""

#: templates/dashboard/withdraw.php:197
msgid "Submit Request"
msgstr ""

#: templates/dashboard/withdraw.php:215
msgid "Withdrawal History"
msgstr ""

#: templates/dashboard/withdraw.php:224
msgid "Withdrawal Method"
msgstr ""

#: templates/dashboard/withdraw.php:227
msgid "Requested On"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:53
#: templates/ecommerce/billing-form-fields.php:55
#: views/pages/instructors.php:317
msgid "Email Address"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:61
msgid "Country"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:64
msgid "Select Country"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:80
#: assets/js/tutor-tax-settings.js:7597
msgid "State"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:84
#: assets/js/tutor-front.js:3085
msgid "N/A"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:87
msgid "Select State"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:103
#: templates/ecommerce/billing-form-fields.php:105
msgid "City"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:112
#: templates/ecommerce/billing-form-fields.php:114
msgid "Postcode / ZIP"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:121
#: templates/ecommerce/billing-form-fields.php:123
msgid "Phone"
msgstr ""

#: templates/ecommerce/billing-form-fields.php:130
#: templates/ecommerce/billing-form-fields.php:132
msgid "Address"
msgstr ""

#: templates/ecommerce/billing.php:19
#: templates/ecommerce/checkout.php:76
#: assets/js/tutor-order-details.js:10287
msgid "Billing Address"
msgstr ""

#: templates/ecommerce/billing.php:31
msgid "Save Address"
msgstr ""

#. translators: %d: Number of courses in the cart.
#: templates/ecommerce/cart.php:39
msgid "%d Course in Cart"
msgid_plural "%d Courses in Cart"
msgstr[0] ""
msgstr[1] ""

#. translators: %d: Number of courses in the cart.
#: templates/ecommerce/cart.php:67
#: templates/ecommerce/checkout-details.php:89
msgid "%d Course bundle"
msgstr ""

#: templates/ecommerce/cart.php:95
#: assets/js/tutor-course-builder.js:5793
#: assets/js/tutor-course-builder.js:11842
#: assets/js/tutor-payment-settings.js:6050
#: assets/js/tutor-payment-settings.js:12249
msgid "Remove"
msgstr ""

#: templates/ecommerce/cart.php:115
msgid "Summary:"
msgstr ""

#: templates/ecommerce/cart.php:119
msgid "Subtotal:"
msgstr ""

#: templates/ecommerce/cart.php:124
msgid "Tax:"
msgstr ""

#: templates/ecommerce/cart.php:131
msgid "Grand total"
msgstr ""

#. translators: %s: tax amount
#: templates/ecommerce/cart.php:140
#: templates/ecommerce/checkout-details.php:202
msgid "(Incl. Tax %s)"
msgstr ""

#: templates/ecommerce/cart.php:146
msgid "Proceed to checkout"
msgstr ""

#: templates/ecommerce/cart.php:154
msgid "Empty shopping cart"
msgstr ""

#: templates/ecommerce/cart.php:155
msgid "No courses in the cart"
msgstr ""

#: templates/ecommerce/cart.php:156
msgid "Continue Browsing"
msgstr ""

#: templates/ecommerce/checkout-details.php:59
msgid "Order Details"
msgstr ""

#: templates/ecommerce/checkout-details.php:130
#: assets/js/tutor-order-details.js:10016
msgid "Subtotal"
msgstr ""

#: templates/ecommerce/checkout-details.php:152
msgid "Have a coupon?"
msgstr ""

#: templates/ecommerce/checkout-details.php:154
msgid "Click here"
msgstr ""

#: templates/ecommerce/checkout-details.php:162
msgid "Add coupon code"
msgstr ""

#: templates/ecommerce/checkout-details.php:163
#: views/elements/course-filters.php:33
#: views/elements/filters.php:41
#: views/elements/search-filter.php:28
#: views/elements/search-filter.php:48
#: views/pages/tools/settings-log.php:66
#: assets/js/tutor-admin.js:1775
#: assets/js/tutor-order-details.js:9405
#: assets/js/tutor-tax-settings.js:7624
#: assets/js/tutor-tax-settings.js:10641
#: assets/js/tutor.js:1888
msgid "Apply"
msgstr ""

#: templates/ecommerce/checkout-details.php:183
msgid "Tax"
msgstr ""

#: templates/ecommerce/checkout-details.php:191
msgid "Grand Total"
msgstr ""

#: templates/ecommerce/checkout-details.php:215
#: templates/single/course/course-entry-box.php:265
msgid "Enroll Now"
msgstr ""

#: templates/ecommerce/checkout-details.php:215
msgid "Pay Now"
msgstr ""

#: templates/ecommerce/checkout.php:67
msgid "Already have an account?"
msgstr ""

#: templates/ecommerce/checkout.php:69
msgid "Login"
msgstr ""

#: templates/ecommerce/checkout.php:78
msgid "Continue as Guest"
msgstr ""

#: templates/ecommerce/checkout.php:105
msgid "No payment method found. Please contact the site administrator."
msgstr ""

#: templates/ecommerce/checkout.php:150
msgid "I agree with the website's"
msgstr ""

#: templates/ecommerce/checkout.php:151
msgid "Terms of Use"
msgstr ""

#: templates/ecommerce/checkout.php:153
msgid "and"
msgstr ""

#: templates/ecommerce/order-placement-failed.php:24
msgid "payment failed"
msgstr ""

#: templates/ecommerce/order-placement-failed.php:29
msgid "Payment failed"
msgstr ""

#: templates/ecommerce/order-placement-failed.php:32
msgid "An error occurred. Please try to place the order again"
msgstr ""

#: templates/ecommerce/order-placement-failed.php:38
msgid "Back to Checkout"
msgstr ""

#: templates/ecommerce/order-placement-success.php:18
msgid "order confirmed"
msgstr ""

#: templates/ecommerce/order-placement-success.php:23
msgid "Order Confirmed"
msgstr ""

#: templates/ecommerce/order-placement-success.php:26
msgid "You will receive an order confirmation email shortly"
msgstr ""

#: templates/ecommerce/order-placement-success.php:32
msgid "Continue Shopping"
msgstr ""

#: templates/ecommerce/order-placement-success.php:35
msgid "Check Order List"
msgstr ""

#. translators: %s: user login
#: templates/email/send-reset-password.php:19
msgid "Hi %s,"
msgstr ""

#. translators: %s: site name
#: templates/email/send-reset-password.php:26
msgid "Someone has requested a new password for the following account on %s:"
msgstr ""

#. translators: %s: user login
#: templates/email/send-reset-password.php:33
msgid "Username: %s"
msgstr ""

#: templates/email/send-reset-password.php:37
msgid "If you didn't make this request, just ignore this email. If you'd like to proceed:"
msgstr ""

#: templates/email/send-reset-password.php:41
msgid "Click here to reset your password"
msgstr ""

#: templates/email/send-reset-password.php:44
msgid "Thanks for reading."
msgstr ""

#: templates/global/alert.php:27
msgid "Please define alert class"
msgstr ""

#: templates/global/attachments.php:45
msgid "No Attachment Found"
msgstr ""

#: templates/instructor/cover.php:16
msgid "Instructor Cover Photo"
msgstr ""

#: templates/login-form.php:59
msgid "Username or Email Address"
msgstr ""

#: templates/login-form.php:76
msgid "Keep me signed in"
msgstr ""

#: templates/login-form.php:80
msgid "Forgot Password?"
msgstr ""

#: templates/login-form.php:86
msgid "Sign In"
msgstr ""

#: templates/login-form.php:99
msgid "Don't have an account?"
msgstr ""

#: templates/login-form.php:101
msgid "Register Now"
msgstr ""

#: templates/login.php:34
#: views/modal/login.php:24
msgid "Hi, Welcome back!"
msgstr ""

#: templates/loop/add-to-cart-edd.php:26
msgid "Purchase"
msgstr ""

#: templates/loop/add-to-cart-tutor.php:39
#: templates/single/course/add-to-cart-tutor.php:97
msgid "Add to Cart"
msgstr ""

#: templates/loop/course-continue.php:20
#: templates/loop/course-continue.php:40
#: templates/single/course/course-entry-box.php:145
#: templates/single/course/course-entry-box.php:224
msgid "Start Learning"
msgstr ""

#: templates/loop/course-continue.php:38
#: templates/loop/course-continue.php:42
#: templates/loop/course-continue.php:47
#: templates/single/course/course-entry-box.php:148
msgid "Continue Learning"
msgstr ""

#: templates/loop/course-continue.php:45
#: templates/single/course/course-entry-box.php:161
msgid "Review Progress"
msgstr ""

#: templates/loop/course-price-edd.php:29
#: templates/loop/course-price-tutor.php:20
#: templates/loop/course-price-woocommerce.php:23
#: templates/loop/course-price.php:38
msgid "Enroll Course"
msgstr ""

#: templates/loop/course-price-edd.php:57
#: templates/loop/course-price-tutor.php:65
#: templates/loop/course-price-woocommerce.php:65
#: templates/loop/course-price.php:66
msgid "% Booked"
msgstr ""

#: templates/modal/alert.php:37
#: assets/js/tutor-course-builder.js:4695
#: assets/js/tutor-course-builder.js:7436
#: assets/js/tutor-course-builder.js:8034
#: assets/js/tutor-course-builder.js:17379
#: assets/js/tutor-course-builder.js:20378
#: assets/js/tutor-course-builder.js:20574
#: assets/js/tutor-course-builder.js:21138
#: assets/js/tutor-course-builder.js:21573
#: assets/js/tutor-course-builder.js:22178
#: assets/js/tutor-course-builder.js:22826
#: assets/js/tutor-course-builder.js:25113
#: assets/js/tutor-course-builder.js:26006
#: assets/js/tutor-course-builder.js:30638
msgid "Ok"
msgstr ""

#: templates/modal/confirm.php:19
#: assets/js/tutor-course-builder.js:25145
msgid "Yes"
msgstr ""

#: templates/permission-denied.php:28
msgid "You don't have permission to access this page"
msgstr ""

#: templates/permission-denied.php:29
msgid "Please make sure you are logged in to correct account if the content needs authorization."
msgstr ""

#: templates/profile/bio.php:24
msgid "Bio data is empty"
msgstr ""

#: templates/profile/courses_taken.php:49
msgid "No course yet."
msgstr ""

#: templates/public-profile.php:149
msgid "Courses Enrolled"
msgstr ""

#: templates/public-profile.php:154
msgid "Courses Completed"
msgstr ""

#: templates/public-profile.php:154
msgid "Course Completed"
msgstr ""

#: templates/shortcode/instructor-filter.php:17
msgid "Relevant"
msgstr ""

#: templates/shortcode/instructor-filter.php:19
msgid "Popular"
msgstr ""

#: templates/shortcode/instructor-filter.php:44
#: views/elements/course-filters.php:76
#: views/elements/course-filters.php:85
msgid "Filters"
msgstr ""

#: templates/shortcode/instructor-filter.php:50
#: assets/js/tutor-coupon.js:5040
#: assets/js/tutor-course-builder.js:16229
#: assets/js/tutor-import-export.js:7084
#: assets/js/tutor-order-details.js:7268
#: assets/js/tutor-payment-settings.js:9204
#: assets/js/tutor-tax-settings.js:7263
msgid "Clear"
msgstr ""

#: templates/shortcode/instructor-filter.php:77
#: templates/single/assignment/content.php:412
#: templates/single/assignment/content.php:594
#: templates/single/course/course-content.php:41
#: views/options/template/color_picker.php:106
#: assets/js/tutor-setup.js:28
#: assets/js/tutor.js:28
msgid "Show More"
msgstr ""

#: templates/shortcode/instructor-filter.php:108
msgid "Search any instructor..."
msgstr ""

#: templates/shortcode/instructor-filter.php:113
msgid "Sort by"
msgstr ""

#: templates/single/assignment/content.php:149
#: templates/single/quiz/top.php:77
#: assets/js/tutor-course-builder.js:10586
msgid "Week"
msgid_plural "Weeks"
msgstr[0] ""
msgstr[1] ""

#: templates/single/assignment/content.php:157
msgid "Duration:"
msgstr ""

#: templates/single/assignment/content.php:159
#: templates/single/quiz/parts/meta.php:41
msgid "No limit"
msgstr ""

#: templates/single/assignment/content.php:163
msgid "Deadline:"
msgstr ""

#. translators: %1$s is the number value (e.g., 3), %2$s is the time unit (e.g., days).
#: templates/single/assignment/content.php:170
msgid "%1$s %2$s after you start the assignment"
msgstr ""

#: templates/single/assignment/content.php:183
msgid "N\\A"
msgstr ""

#: templates/single/assignment/content.php:191
msgid "Total Marks:"
msgstr ""

#: templates/single/assignment/content.php:195
msgid "Passing Mark:"
msgstr ""

#: templates/single/assignment/content.php:214
msgid "You have missed the submission deadline. Please contact the instructor for more information."
msgstr ""

#: templates/single/assignment/content.php:240
#: assets/js/tutor-course-builder.js:18382
#: assets/js/tutor-course-builder.js:29389
msgid "Attachments"
msgstr ""

#: templates/single/assignment/content.php:259
#: views/fragments/thumbnail-uploader.php:35
#: views/fragments/thumbnail-uploader.php:40
msgid "Size: "
msgstr ""

#: templates/single/assignment/content.php:285
msgid "Assignment Submission"
msgstr ""

#: templates/single/assignment/content.php:289
msgid "Assignment answer form"
msgstr ""

#: templates/single/assignment/content.php:314
msgctxt "Assignment attachment"
msgid "Attach assignment files (Max: "
msgstr ""

#: templates/single/assignment/content.php:315
msgctxt "Assignment attachment"
msgid " file)"
msgstr ""

#: templates/single/assignment/content.php:326
msgid "Choose file"
msgstr ""

#: templates/single/assignment/content.php:335
#: views/fragments/thumbnail-uploader.php:47
#: views/fragments/thumbnail-uploader.php:52
msgid "File Support: "
msgstr ""

#: templates/single/assignment/content.php:337
msgid "Any standard Image, Document, Presentation, Sheet, PDF or Text file is allowed"
msgstr ""

#: templates/single/assignment/content.php:341
msgid "Total File Size: Max"
msgstr ""

#: templates/single/assignment/content.php:344
#: assets/js/tutor-course-builder.js:18545
msgid "MB"
msgstr ""

#: templates/single/assignment/content.php:389
msgid "Submit Assignment"
msgstr ""

#: templates/single/assignment/content.php:421
#: templates/single/assignment/content.php:623
msgid "Skip To Next"
msgstr ""

#: templates/single/assignment/content.php:450
#: views/quiz/contexts.php:71
msgid "Pass Marks"
msgstr ""

#: templates/single/assignment/content.php:453
#: views/quiz/contexts.php:23
#: views/quiz/contexts.php:74
msgid "Earned Marks"
msgstr ""

#: templates/single/assignment/content.php:483
msgid "Passed"
msgstr ""

#: templates/single/assignment/content.php:510
#: views/quiz/attempt-details.php:321
msgid "Instructor Note"
msgstr ""

#: templates/single/assignment/content.php:521
msgid "Your Assignment"
msgstr ""

#: templates/single/assignment/content.php:605
msgid "Continue Lesson"
msgstr ""

#: templates/single/assignment/content.php:617
msgid "Start Assignment"
msgstr ""

#: templates/single/common/header.php:68
msgid "Your Progress:"
msgstr ""

#: templates/single/common/header.php:73
msgid "of "
msgstr ""

#: templates/single/course/add-to-cart-edd.php:21
msgid "Please make sure that your EDD product exists and valid for this course"
msgstr ""

#: templates/single/course/add-to-cart-woocommerce.php:81
msgid "Please make sure that your product exists and valid for this course"
msgstr ""

#: templates/single/course/continue-lesson.php:40
msgid "Continue to lesson"
msgstr ""

#: templates/single/course/course-benefits.php:23
msgid "What Will You Learn?"
msgstr ""

#: templates/single/course/course-content.php:30
msgid "About Course"
msgstr ""

#: templates/single/course/course-entry-box.php:43
#: templates/single/course/course-entry-box.php:44
msgid "Last Updated"
msgstr ""

#: templates/single/course/course-entry-box.php:89
msgid "Course Progress"
msgstr ""

#: templates/single/course/course-entry-box.php:131
msgid "Retake This Course"
msgstr ""

#: templates/single/course/course-entry-box.php:189
msgid "Complete Course"
msgstr ""

#: templates/single/course/course-entry-box.php:205
msgid "You enrolled in this course on"
msgstr ""

#: templates/single/course/course-entry-box.php:239
msgid "This course is full right now. We limit the number of students to create an optimized and productive group dynamic."
msgstr ""

#: templates/single/course/course-entry-box.php:271
msgid "Free access this course"
msgstr ""

#: templates/single/course/course-target-audience.php:24
msgid "Audience"
msgstr ""

#: templates/single/course/course-topics.php:38
#: templates/single/lesson/lesson_sidebar.php:42
#: assets/js/tutor-course-builder.js:32599
msgid "Course Content"
msgstr ""

#: templates/single/course/enrolled/question_and_answer.php:67
#: templates/single/course/reviews.php:152
#: templates/single/lesson/comment.php:39
msgid "Load More"
msgstr ""

#: templates/single/course/instructors.php:21
msgid "A course by"
msgstr ""

#: templates/single/course/lead-info.php:78
msgid "Categories:"
msgstr ""

#: templates/single/course/lead-info.php:92
msgid "Uncategorized"
msgstr ""

#: templates/single/course/material-includes.php:24
msgid "Material Includes"
msgstr ""

#: templates/single/course/q_and_a_turned_off.php:17
msgid "This feature has been disabled by the administrator"
msgstr ""

#: templates/single/course/reviews.php:57
msgid "Student Ratings & Reviews"
msgstr ""

#: templates/single/course/reviews.php:63
msgid "No Review Yet"
msgstr ""

#: templates/single/course/reviews.php:80
msgid "Total "
msgstr ""

#: templates/single/course/reviews.php:82
msgid " Rating"
msgid_plural " Ratings"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: rating count
#: templates/single/course/reviews.php:112
msgid "%s Rating"
msgid_plural "%s Ratings"
msgstr[0] ""
msgstr[1] ""

#: templates/single/course/reviews.php:138
msgid "Write a review"
msgstr ""

#: templates/single/course/reviews.php:138
msgid "Edit review"
msgstr ""

#: templates/single/course/reviews.php:186
msgid "Submit Review"
msgstr ""

#: templates/single/lesson/comment.php:73
msgid "Join the conversation"
msgstr ""

#: templates/single/lesson/comment.php:84
#: templates/single/lesson/comments-loop.php:82
msgid "Write your comment here…"
msgstr ""

#: templates/single/lesson/complete_form.php:29
msgid "Mark as Complete"
msgstr ""

#: templates/single/lesson/content.php:127
#: assets/js/tutor-course-builder.js:29439
msgid "Overview"
msgstr ""

#: templates/single/lesson/content.php:136
#: templates/single/lesson/content.php:176
#: assets/js/tutor-course-builder.js:19915
msgid "Exercise Files"
msgstr ""

#: templates/single/lesson/content.php:149
msgid "Comments"
msgstr ""

#: templates/single/lesson/required-enroll.php:16
msgid "Please enroll in this course to view course content."
msgstr ""

#. translators: %s: course name
#: templates/single/lesson/required-enroll.php:18
msgid "Course name : %s"
msgstr ""

#: templates/single/lesson/sidebar_question_and_answer.php:45
msgid "No questions yet"
msgstr ""

#: templates/single/lesson/sidebar_question_and_answer.php:48
msgid "Describe what you're trying to achieve and where you're getting stuck"
msgstr ""

#: templates/single/password-protected.php:34
msgid "Course is locked"
msgstr ""

#: templates/single/password-protected.php:42
msgid "Enter your password"
msgstr ""

#: templates/single/password-protected.php:51
msgid "Show password"
msgstr ""

#: templates/single/quiz/no_course_belongs.php:15
msgid "No course found for this quiz"
msgstr ""

#: templates/single/quiz/no_course_belongs.php:16
msgid "It seems there is no course belongs with this quiz, you can not attempt on this quiz without a course belongs, please notify to your instructor to fix this issue."
msgstr ""

#: templates/single/quiz/parts/image-answer.php:36
msgid "Write your answer here"
msgstr ""

#: templates/single/quiz/parts/image-matching.php:65
#: templates/single/quiz/parts/matching.php:92
msgid "Drag your answer"
msgstr ""

#: templates/single/quiz/parts/meta.php:22
msgid "Questions No"
msgstr ""

#: templates/single/quiz/parts/meta.php:33
#: templates/single/quiz/top.php:100
msgid "Total Attempted"
msgstr ""

#: templates/single/quiz/parts/meta.php:45
#: assets/js/tutor-course-builder.js:24542
msgid "Attempts Allowed"
msgstr ""

#: templates/single/quiz/parts/meta.php:71
msgid "Time remaining: "
msgstr ""

#: templates/single/quiz/parts/meta.php:106
msgid "Reattempt"
msgstr ""

#: templates/single/quiz/parts/open-ended.php:24
#: templates/single/quiz/parts/short-answer.php:24
msgid "Character Remaining: "
msgstr ""

#: templates/single/quiz/parts/question.php:120
msgid "Marks : "
msgstr ""

#: templates/single/quiz/parts/question.php:206
msgid "Submit &amp; Next"
msgstr ""

#: templates/single/quiz/parts/question.php:206
#: templates/single/quiz/parts/question.php:224
msgid "Submit Quiz"
msgstr ""

#: templates/single/quiz/parts/question.php:210
msgid "Skip Question"
msgstr ""

#: templates/single/quiz/previous-attempts.php:58
#: templates/single/quiz/top.php:129
msgid "Start Quiz"
msgstr ""

#: templates/single/quiz/top.php:60
#: views/quiz/contexts.php:97
#: views/quiz/header-context/course-single-previous-attempts.php:32
#: assets/js/tutor-course-builder.js:24123
msgid "Questions"
msgstr ""

#: templates/single/quiz/top.php:81
#: views/quiz/contexts.php:68
#: views/quiz/header-context/course-single-previous-attempts.php:38
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:29
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:34
msgid "Quiz Time"
msgstr ""

#. translators: %d: count, %s: time unit.
#: templates/single/quiz/top.php:87
msgid "%1$s %2$s"
msgstr ""

#: templates/single/quiz/top.php:110
#: assets/js/tutor-course-builder.js:24570
msgid "Passing Grade"
msgstr ""

#: templates/single/quiz/top.php:134
msgid "Skip Quiz"
msgstr ""

#: templates/single/quiz/top.php:143
msgid "Do You Want to Skip This Quiz?"
msgstr ""

#: templates/single/quiz/top.php:144
msgid "Are you sure you want to skip this quiz? Please confirm your choice."
msgstr ""

#: templates/single/quiz/top.php:146
msgid "Yes, Skip This"
msgstr ""

#: templates/template-part/form-retrieve-password.php:27
msgid "Enter Password and Confirm Password to reset your password"
msgstr ""

#: templates/template-part/form-retrieve-password.php:45
msgid "Confirm Password"
msgstr ""

#: templates/template-part/form-retrieve-password.php:59
#: templates/template-part/retrieve-password.php:47
#: templates/template-part/retrieve-password.php:48
msgid "Reset password"
msgstr ""

#: templates/template-part/retrieve-password.php:29
msgid "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email."
msgstr ""

#: templates/template-part/retrieve-password.php:34
msgid "Username or email"
msgstr ""

#: traits/JsonResponse.php:48
msgid "Bad request"
msgstr ""

#: views/course-share.php:24
msgid "Share"
msgstr ""

#: views/course-share.php:35
msgid "Share Course"
msgstr ""

#: views/course-share.php:38
msgid "Page Link"
msgstr ""

#: views/course-share.php:48
msgid "Share On Social Media"
msgstr ""

#: views/elements/bulk-confirm-popup.php:24
msgid "Before You Proceed!"
msgstr ""

#: views/elements/bulk-confirm-popup.php:25
msgid "Are you sure you would like to perform this action? We suggest you proceed with caution."
msgstr ""

#: views/elements/bulk-confirm-popup.php:36
#: views/elements/common-confirm-popup.php:68
msgid "Yes, I’m sure"
msgstr ""

#: views/elements/common-confirm-popup.php:30
msgid "Do You Want to Delete This?"
msgstr ""

#: views/elements/common-confirm-popup.php:31
msgid "Are you sure you want to delete this permanently from the site? Please confirm your choice."
msgstr ""

#: views/elements/course-empty-state.php:29
#: views/elements/trashed-course-empty-state.php:16
msgid "No Courses Found."
msgstr ""

#: views/elements/course-empty-state.php:33
msgid "Try using different keywords"
msgstr ""

#: views/elements/course-filters.php:127
#: views/elements/course-filters.php:201
#: views/elements/filters.php:133
msgid "No record found"
msgstr ""

#: views/elements/course-filters.php:137
msgid "Apply Filters"
msgstr ""

#: views/elements/course-filters.php:155
#: views/elements/filters.php:218
#: assets/js/tutor-addon-list.js:5262
#: assets/js/tutor-coupon.js:3367
msgid "Search..."
msgstr ""

#: views/elements/course-filters.php:163
msgid "Clear All"
msgstr ""

#: views/elements/create-course-empty-state.php:17
msgid "Create Your First Course"
msgstr ""

#: views/elements/create-course-empty-state.php:20
msgid "Build an engaging eLearning course by adding lessons, quizzes, assignments, and more."
msgstr ""

#: views/elements/create-course-empty-state.php:35
msgid "Need help creating courses with Tutor LMS?"
msgstr ""

#: views/elements/create-course-empty-state.php:38
msgid "Explore our in-depth tutorials and create your eLearning courses with ease."
msgstr ""

#: views/elements/create-course-empty-state.php:43
msgid "Watch Tutorials"
msgstr ""

#: views/elements/filters.php:85
#: assets/js/tutor-payment-settings.js:12809
msgid "Reset"
msgstr ""

#: views/elements/filters.php:104
msgid "All Courses"
msgstr ""

#: views/elements/filters.php:125
msgid "All Category"
msgstr ""

#: views/elements/filters.php:142
#: views/pages/ecommerce/order-list.php:102
msgid "Payment Status"
msgstr ""

#: views/elements/filters.php:146
#: views/elements/filters.php:167
#: assets/js/tutor-admin.js:853
#: assets/js/tutor-course-builder.js:28040
#: assets/js/tutor-course-builder.js:28169
#: assets/js/tutor-front.js:1466
#: assets/js/tutor-import-export.js:6570
#: assets/js/tutor-setup.js:827
msgid "Select"
msgstr ""

#: views/elements/filters.php:163
msgid "Applies To"
msgstr ""

#. translators: %1$s: number of courses, %2$s: the anchor tag
#: views/elements/trashed-course-empty-state.php:22
msgid "You have %1$s course in Trash %2$s"
msgid_plural "You have %1$s courses in Trash %2$s"
msgstr[0] ""
msgstr[1] ""

#: views/elements/trashed-course-empty-state.php:29
msgid "View Trash"
msgstr ""

#: views/fragments/announcement-list.php:153
msgid "Published Date"
msgstr ""

#: views/fragments/announcement-list.php:213
msgid "Delete This Announcement?"
msgstr ""

#: views/fragments/announcement-list.php:214
msgid "Are you sure you want to delete this Announcement permanently from the site? Please confirm your choice."
msgstr ""

#: views/fragments/announcement-list.php:337
msgid "Edit Announcement"
msgstr ""

#: views/fragments/attachments.php:51
msgid "Upload Attachments"
msgstr ""

#: views/fragments/thumbnail-uploader.php:24
#: assets/js/tutor-setup.js:1191
#: assets/js/tutor.js:2220
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr ""

#: views/fragments/thumbnail-uploader.php:24
#: assets/js/tutor-setup.js:1193
#: assets/js/tutor.js:2222
msgid "Use this media"
msgstr ""

#: views/fragments/thumbnail-uploader.php:42
msgid "700x430 pixels"
msgstr ""

#: views/fragments/thumbnail-uploader.php:54
msgctxt "tutor-supported-image-type"
msgid ".jpg, .jpeg,. gif, or .png"
msgstr ""

#: views/fragments/thumbnail-uploader.php:61
#: views/options/field-types/upload_half.php:42
#: assets/js/tutor-course-builder.js:19760
#: assets/js/tutor-course-builder.js:21464
#: assets/js/tutor-course-builder.js:22729
#: assets/js/tutor-payment-settings.js:12221
#: assets/js/tutor-payment-settings.js:12497
msgid "Upload Image"
msgstr ""

#: views/maintenance.php:76
msgid "Under Maintenance"
msgstr ""

#: views/maintenance.php:77
msgid "Sorry for the inconvenience."
msgstr ""

#: views/metabox/user-profile-fields.php:14
msgid "Tutor Fields"
msgstr ""

#: views/metabox/user-profile-fields.php:27
msgid "Job Title"
msgstr ""

#: views/metabox/user-profile-fields.php:34
msgid "Profile Bio"
msgstr ""

#: views/metabox/user-profile-fields.php:41
msgid "Write a little bit more about you, it will show publicly."
msgstr ""

#: views/metabox/user-profile-fields.php:46
msgid "Profile Photo"
msgstr ""

#: views/metabox/user-profile-fields.php:58
msgid "Upload"
msgstr ""

#: views/modal/review.php:35
msgid "Tell us about your own personal experience taking this course. Was it a good match for you?"
msgstr ""

#: views/options/field-types/radio.php:13
#: views/options/field-types/select.php:22
msgid "Select Option"
msgstr ""

#: views/options/field-types/upload_half.php:32
msgid "File Support"
msgstr ""

#: views/options/field-types/upload_half.php:33
msgid "jpg, .jpeg, .png"
msgstr ""

#: views/options/field-types/upload_half.php:35
msgid "Image size ratio: 4:1"
msgstr ""

#: views/options/field-types/webhook_url.php:17
msgid "Webhook URL"
msgstr ""

#: views/options/field-types/webhook_url.php:29
#: views/pages/withdraw_requests.php:183
#: views/pages/withdraw_requests.php:210
#: assets/js/tutor-payment-settings.js:11731
msgid "Copy"
msgstr ""

#: views/options/settings.php:26
msgid "Search ...⌃⌥ + S or Alt+S for shortcut"
msgstr ""

#: views/options/settings.php:33
msgid "Save Changes"
msgstr ""

#: views/options/template/common/reset-button-template.php:20
#: assets/js/tutor-payment-settings.js:12805
msgid "Reset to Default Settings?"
msgstr ""

#: views/options/template/common/reset-button-template.php:21
#: assets/js/tutor-payment-settings.js:12808
msgid "WARNING! This will overwrite all customized settings of this section and reset them to default. Proceed with caution."
msgstr ""

#: views/options/template/common/reset-button-template.php:23
#: views/options/template/google_classroom.php:19
#: views/options/template/gradebook.php:19
#: assets/js/tutor-payment-settings.js:12828
msgid "Reset to Default"
msgstr ""

#: views/options/template/tutor_pages.php:18
#: views/pages/tools/tutor_pages.php:121
msgid "Note: This tool will install all the missing Tutor pages. Pages already defined and set up will not be replaced."
msgstr ""

#: views/options/template/tutor_pages.php:22
msgid "All Pages"
msgstr ""

#: views/options/template/tutor_pages.php:30
#: views/pages/tools/tutor_pages.php:19
msgid "Page Name"
msgstr ""

#: views/options/template/tutor_pages.php:82
#: views/pages/tools/tutor_pages.php:113
msgid "Re-Generate Tutor Pages"
msgstr ""

#: views/options/withdraw/withdraw_admin_options_generator.php:38
msgid "Enable/Disable"
msgstr ""

#: views/options/withdraw/withdraw_admin_options_generator.php:43
msgid "Enable "
msgstr ""

#: views/pages/addons.php:16
msgid "Tutor Addons"
msgstr ""

#: views/pages/addons.php:23
msgid "Plugins"
msgstr ""

#: views/pages/addons.php:56
msgctxt "addon-last-checked"
msgid "Last checked"
msgstr ""

#: views/pages/addons.php:58
msgctxt "addon-last-checked"
msgid "ago, It will check again after"
msgstr ""

#: views/pages/addons.php:60
msgctxt "addon-last-checked"
msgid "from now"
msgstr ""

#: views/pages/addons.php:110
msgid "Version:"
msgstr ""

#: views/pages/add_new_instructor.php:13
#: views/pages/add_new_instructor.php:150
msgid "Add new instructor"
msgstr ""

#: views/pages/answer.php:38
msgid "Write an answer here"
msgstr ""

#: views/pages/answer.php:42
msgid "Place answer"
msgstr ""

#: views/pages/answer.php:74
msgid "on"
msgstr ""

#: views/pages/course-builder.php:30
msgid "Tutor Course Builder"
msgstr ""

#: views/pages/course-list.php:267
msgid "Plan"
msgstr ""

#: views/pages/course-list.php:332
msgid "Topic:"
msgstr ""

#: views/pages/course-list.php:338
msgid "Lesson:"
msgstr ""

#: views/pages/course-list.php:344
msgid "Quiz:"
msgstr ""

#: views/pages/course-list.php:350
msgid "Assignment:"
msgstr ""

#: views/pages/course-list.php:417
msgid "Update course status"
msgstr ""

#: views/pages/course-list.php:437
msgid "View"
msgstr ""

#: views/pages/course-list.php:512
#: views/pages/ecommerce/coupon-list.php:227
#: views/pages/ecommerce/order-list.php:229
msgid "Deletion of the course will erase all its topics, lessons, quizzes, events, and other information. Please confirm your choice."
msgstr ""

#: views/pages/ecommerce/coupon-list.php:49
#: views/pages/ecommerce/order-list.php:41
#: views/pages/instructors.php:83
#: views/pages/tools/manage-tokens.php:42
msgid "Add New"
msgstr ""

#: views/pages/ecommerce/coupon-list.php:92
#: assets/js/tutor-coupon.js:5345
msgid "Applies to"
msgstr ""

#: views/pages/ecommerce/coupon-list.php:95
#: assets/js/tutor-coupon.js:5313
#: assets/js/tutor-order-details.js:10032
#: assets/js/tutor-order-details.js:10055
msgid "Discount"
msgstr ""

#: views/pages/ecommerce/coupon-list.php:98
#: views/quiz/contexts.php:96
#: assets/js/tutor-coupon.js:8083
#: assets/js/tutor-import-export.js:8480
msgid "Type"
msgstr ""

#: views/pages/ecommerce/coupon-list.php:107
msgid "Usage"
msgstr ""

#: views/pages/ecommerce/order-list.php:99
#: assets/js/tutor-coupon.js:7627
msgid "Method"
msgstr ""

#: views/pages/ecommerce/order-list.php:108
msgid "Total"
msgstr ""

#: views/pages/ecommerce/order-list.php:112
#: views/pages/tools/manage-tokens.php:52
#: views/qna/contexts.php:21
msgid "Action"
msgstr ""

#. translators: %s is the transaction ID
#: views/pages/ecommerce/order-list.php:166
#: assets/js/tutor-order-details.js:10196
msgid "Trx ID: %s"
msgstr ""

#: views/pages/feature-promotion.php:19
msgid "Built-in email notification and email marketing tool!"
msgstr ""

#: views/pages/feature-promotion.php:20
msgid "Send automated email notifications to students, instructors, and admins based on their actions and events on your eLearning website. Customize email templates, send manual emails, and do much more."
msgstr ""

#: views/pages/get-pro.php:13
msgid "Get pro plugin from themeum.com"
msgstr ""

#: views/pages/instructors.php:131
#: views/pages/instructors.php:182
msgid "Commission Rate"
msgstr ""

#: views/pages/instructors.php:177
msgid "Total Course"
msgstr ""

#: views/pages/instructors.php:232
msgid "No instructor found"
msgstr ""

#: views/pages/instructors.php:264
msgid "Add New Instructor"
msgstr ""

#: views/pages/instructors.php:280
msgid "Enter First Name"
msgstr ""

#: views/pages/instructors.php:280
#: views/pages/instructors.php:288
msgid "Only alphanumeric & space are allowed"
msgstr ""

#: views/pages/instructors.php:288
msgid "Enter Last Name"
msgstr ""

#: views/pages/instructors.php:298
msgid "Enter Username"
msgstr ""

#: views/pages/instructors.php:298
msgid "Only alphanumeric and underscore are allowed"
msgstr ""

#: views/pages/instructors.php:305
#: views/pages/instructors.php:353
msgid "(Optional)"
msgstr ""

#: views/pages/instructors.php:309
msgid "Enter Phone Number"
msgstr ""

#: views/pages/instructors.php:309
msgid "Only number is allowed"
msgstr ""

#: views/pages/instructors.php:320
msgid "Enter Your Email"
msgstr ""

#: views/pages/instructors.php:337
msgid "Retype Password"
msgstr ""

#: views/pages/instructors.php:370
msgid "Add Instructor"
msgstr ""

#: views/pages/instructors.php:401
msgid "A New Instructor Just Signed Up"
msgstr ""

#: views/pages/instructors.php:404
msgid "You can either accept or reject the application. The applicant will be notified via email either way."
msgstr ""

#: views/pages/instructors.php:423
msgid "Username:"
msgstr ""

#: views/pages/instructors.php:430
msgid "Email:"
msgstr ""

#: views/pages/instructors.php:441
#: views/pages/instructors.php:452
msgid "Approve the Instructor"
msgstr ""

#: views/pages/instructors.php:444
#: views/pages/instructors.php:448
msgid "Reject the Application"
msgstr ""

#: views/pages/instructors.php:460
msgid "Attempted invalid action"
msgstr ""

#: views/pages/instructors.php:471
msgid "Invalid instructor"
msgstr ""

#: views/pages/students.php:96
msgid "Course Taken"
msgstr ""

#: views/pages/tools/manage-tokens.php:48
#: views/pages/tools/manage-tokens.php:96
#: views/pages/tools/manage-tokens.php:162
#: assets/js/tutor-import-export.js:8487
msgid "User"
msgstr ""

#: views/pages/tools/manage-tokens.php:49
msgid "API Key"
msgstr ""

#: views/pages/tools/manage-tokens.php:50
msgid "Secret"
msgstr ""

#: views/pages/tools/manage-tokens.php:51
#: views/pages/tools/manage-tokens.php:106
#: views/pages/tools/manage-tokens.php:172
msgid "Permission"
msgstr ""

#: views/pages/tools/manage-tokens.php:66
msgid "No record available"
msgstr ""

#: views/pages/tools/manage-tokens.php:83
msgid "Generate API Key, Secret"
msgstr ""

#: views/pages/tools/manage-tokens.php:121
#: views/pages/tools/manage-tokens.php:187
#: views/qna/qna-single.php:174
msgid "Write here..."
msgstr ""

#: views/pages/tools/manage-tokens.php:134
msgid "Generate"
msgstr ""

#: views/pages/tools/manage-tokens.php:148
msgid "Update API"
msgstr ""

#: views/pages/tools/settings-log.php:23
msgid "Current Settings"
msgstr ""

#: views/pages/tools/settings-log.php:25
msgid "Last Update"
msgstr ""

#: views/pages/tools/settings-log.php:29
msgid "Export Settings"
msgstr ""

#: views/pages/tools/settings-log.php:37
msgid "Logs"
msgstr ""

#: views/pages/tools/settings-log.php:66
msgid "Yes, Restore Settings"
msgstr ""

#: views/pages/tools/settings-log.php:66
msgid "Restore Previous Settings?"
msgstr ""

#: views/pages/tools/settings-log.php:66
#: assets/js/tutor-admin.js:1775
#: assets/js/tutor-import-export.js:8865
msgid "WARNING! This will overwrite all existing settings, please proceed with caution."
msgstr ""

#: views/pages/tools/settings-log.php:75
#: assets/js/tutor-admin.js:1775
#: assets/js/tutor-course-builder.js:1147
#: assets/js/tutor-import-export.js:7441
#: assets/js/tutor-payment-settings.js:7389
msgid "Download"
msgstr ""

#: views/pages/tools/settings-log.php:79
msgid "Yes, Delete Settings"
msgstr ""

#: views/pages/tools/settings-log.php:79
msgid "Delete This Settings?"
msgstr ""

#: views/pages/tools/settings-log.php:79
msgid "WARNING! This will remove the settings history data from your system, please proceed with caution."
msgstr ""

#: views/pages/tools/settings-log.php:92
#: assets/js/tutor-admin.js:1778
msgid "No settings data found."
msgstr ""

#: views/pages/tools/settings-log.php:101
msgid "Reset Settings"
msgstr ""

#: views/pages/tools/settings-log.php:106
msgid "Restore to Default Settings"
msgstr ""

#: views/pages/tools/settings-log.php:108
msgid "Revert all settings back to their initial state."
msgstr ""

#: views/pages/tools/settings-log.php:114
msgid "Yes, Reset Settings"
msgstr ""

#: views/pages/tools/settings-log.php:115
msgid "Reset All Settings?"
msgstr ""

#: views/pages/tools/settings-log.php:116
msgid "WARNING! This will reset all settings to default, please proceed with caution."
msgstr ""

#: views/pages/tools/settings-log.php:117
msgid "Reset All Settings"
msgstr ""

#: views/pages/tools/status.php:13
msgid "Tutor Environment Status"
msgstr ""

#: views/pages/tools/status.php:29
msgid "The homepage URL of your site."
msgstr ""

#: views/pages/tools/status.php:35
msgid "The root URL of your site."
msgstr ""

#: views/pages/tools/status.php:41
msgid "The version of WordPress installed on your site."
msgstr ""

#. Translators: %1$s: Current version, %2$s: New version
#: views/pages/tools/status.php:60
msgid "%1$s - There is a newer version of WordPress available (%2$s)"
msgstr ""

#: views/pages/tools/status.php:69
msgid "Tutor Version"
msgstr ""

#: views/pages/tools/status.php:70
msgid "The version of tutor."
msgstr ""

#: views/pages/tools/status.php:76
msgid "Whether or not you have WordPress Multisite enabled."
msgstr ""

#: views/pages/tools/status.php:81
msgid "The maximum amount of memory (RAM) that your site can use at one time."
msgstr ""

#: views/pages/tools/status.php:95
msgid "Displays whether or not WordPress is in Debug Mode."
msgstr ""

#: views/pages/tools/status.php:105
msgid "WordPress cron"
msgstr ""

#: views/pages/tools/status.php:106
msgid "Displays whether or not WP Cron Jobs are enabled."
msgstr ""

#: views/pages/tools/status.php:117
msgid "The current language used by WordPress. Default = English"
msgstr ""

#: views/pages/tools/status.php:122
msgid "Displays whether or not WordPress is using an external object cache."
msgstr ""

#: views/pages/tools/status.php:146
msgid "Information about the web server that is currently hosting your site."
msgstr ""

#: views/pages/tools/status.php:151
msgid "The version of PHP installed on your hosting server."
msgstr ""

#: views/pages/tools/status.php:174
msgid "The largest filesize that can be contained in one post."
msgstr ""

#: views/pages/tools/status.php:179
msgid "The amount of time (in seconds) that your site will spend on a single operation before timing out (to avoid server lockups)"
msgstr ""

#: views/pages/tools/status.php:183
msgid "PHP max input vars"
msgstr ""

#: views/pages/tools/status.php:184
msgid "The maximum number of variables your server can use for a single function to avoid overloads."
msgstr ""

#: views/pages/tools/status.php:189
msgid "The version of cURL installed on your server."
msgstr ""

#: views/pages/tools/status.php:193
msgid "SUHOSIN installed"
msgstr ""

#: views/pages/tools/status.php:194
msgid "Suhosin is an advanced protection system for PHP installations. It was designed to protect your servers on the one hand against a number of well known problems in PHP applications and on the other hand against potential unknown vulnerabilities within these applications or the PHP core itself. If enabled on your server, Suhosin may need to be configured to increase its data submission limits."
msgstr ""

#: views/pages/tools/status.php:209
msgid "The version of MySQL installed on your hosting server."
msgstr ""

#: views/pages/tools/status.php:226
msgid "Max upload size"
msgstr ""

#: views/pages/tools/status.php:227
msgid "The largest filesize that can be uploaded to your WordPress installation."
msgstr ""

#: views/pages/tools/status.php:232
msgid "The default timezone for your server."
msgstr ""

#: views/pages/tools/status.php:246
msgid "Payment gateways can use cURL to communicate with remote servers to authorize payments, other plugins may also use it when communicating with remote services."
msgstr ""

#: views/pages/tools/status.php:262
msgid "HTML/Multipart emails use DOMDocument to generate inline CSS in templates."
msgstr ""

#: views/pages/tools/status.php:279
msgid "GZip (gzopen) is used to open the GEOIP database from MaxMind."
msgstr ""

#: views/pages/tools/status.php:296
msgid "Multibyte String (mbstring) is used to convert character encoding, like for emails or converting characters to lowercase."
msgstr ""

#: views/pages/tools/tutor_pages.php:46
#: views/pages/tools/tutor_pages.php:101
msgid " Page not set"
msgstr ""

#: views/pages/tools/tutor_pages.php:53
msgid " Page deleted, please set new one"
msgstr ""

#: views/pages/tools/tutor_pages.php:60
msgid "Page visibility is not public"
msgstr ""

#: views/pages/view_attempt.php:25
#: views/pages/view_attempt.php:29
msgid "Attemp not found"
msgstr ""

#. translators: %s: Tutor LMS version
#: views/pages/welcome.php:143
msgid "Welcome to <strong>Tutor LMS %s!</strong><br/>Redefining eLearning on WordPress"
msgstr ""

#: views/pages/welcome.php:155
msgid "Don't Show Again"
msgstr ""

#: views/pages/welcome.php:170
msgid "Reimagined Course & Quiz Builder"
msgstr ""

#: views/pages/welcome.php:173
msgid "The reimagined course & quiz builder lets instructors craft visually rich, interactive lessons, and quizzes with ease. Add multimedia, captivating quizzes, custom paths, and more to elevate every learner's journey."
msgstr ""

#: views/pages/welcome.php:192
msgid "Native eCommerce"
msgstr ""

#: views/pages/welcome.php:195
msgid "Sell courses easily with native payments! Control orders, coupons, and taxes while enjoying secure payments via top gateways—all without relying on third-party tools or dependencies."
msgstr ""

#: views/pages/welcome.php:212
msgid "Subscriptions & Memberships"
msgstr ""

#: views/pages/welcome.php:215
msgid "Create a recurring revenue stream with a robust subscriptions and memberships system. Provide course-specific or full-site access with customizable pricing tiers."
msgstr ""

#: views/pages/welcome.php:229
msgid "Advanced Analytics"
msgstr ""

#: views/pages/welcome.php:232
msgid "Get detailed insights on courses, students, earnings, statements, and do so much more with advanced analytics."
msgstr ""

#: views/pages/welcome.php:250
#: assets/js/tutor-coupon.js:6784
#: assets/js/tutor-coupon.js:7346
#: assets/js/tutor-course-builder.js:588
#: assets/js/tutor-course-builder.js:29703
#: assets/js/tutor-course-builder.js:31848
#: assets/js/tutor-course-builder.js:34390
#: assets/js/tutor-course-builder.js:37851
#: assets/js/tutor-order-details.js:8459
#: assets/js/tutor-order-details.js:8958
#: assets/js/tutor-payment-settings.js:8634
#: assets/js/tutor-payment-settings.js:10183
#: assets/js/tutor-payment-settings.js:10913
#: assets/js/tutor-payment-settings.js:11446
#: assets/js/tutor-tax-settings.js:9713
#: assets/js/tutor-tax-settings.js:10275
msgid "AI Studio"
msgstr ""

#: views/pages/welcome.php:253
msgid "Tap into the power of AI to save your course creation time and improve course quality. Generate course outlines, images, and contextual content at the click of a button."
msgstr ""

#: views/pages/welcome.php:277
msgid "Unified Design"
msgstr ""

#: views/pages/welcome.php:280
msgid "A cohesive, intuitive design that enhances user experience across all aspects of course creation and management."
msgstr ""

#: views/pages/welcome.php:296
msgid "And more…"
msgstr ""

#: views/pages/welcome.php:299
msgid "Explore additional features designed to elevate your eLearning experience."
msgstr ""

#: views/pages/welcome.php:314
msgid "Let's Start Building"
msgstr ""

#: views/pages/welcome.php:318
msgid "Compare Free vs Pro"
msgstr ""

#: views/pages/withdraw_requests.php:84
msgid "Request Date"
msgstr ""

#: views/pages/withdraw_requests.php:87
msgid "Request By"
msgstr ""

#: views/pages/withdraw_requests.php:91
msgid "Withdraw Method"
msgstr ""

#: views/pages/withdraw_requests.php:94
msgid "Withdraw Details"
msgstr ""

#: views/pages/withdraw_requests.php:163
msgid "Name:"
msgstr ""

#: views/pages/withdraw_requests.php:171
msgid "A/C Number:"
msgstr ""

#: views/pages/withdraw_requests.php:190
msgid "Bank Name:"
msgstr ""

#: views/pages/withdraw_requests.php:198
msgid "IBAN:"
msgstr ""

#: views/pages/withdraw_requests.php:217
msgid "BIC/SWIFT:"
msgstr ""

#: views/pages/withdraw_requests.php:308
msgid "No request found"
msgstr ""

#: views/pages/withdraw_requests.php:367
msgid "Approve Withdrawal?"
msgstr ""

#: views/pages/withdraw_requests.php:375
msgid "Yes, Approve Withdrawal"
msgstr ""

#: views/pages/withdraw_requests.php:411
msgid "Reject Withdrawal?"
msgstr ""

#: views/pages/withdraw_requests.php:415
#: views/pages/withdraw_requests.php:416
msgid "Invalid Payment Details"
msgstr ""

#: views/pages/withdraw_requests.php:418
#: views/pages/withdraw_requests.php:419
msgid "Invalid Request"
msgstr ""

#: views/pages/withdraw_requests.php:421
#: views/pages/withdraw_requests.php:422
#: assets/js/tutor-order-details.js:10484
msgid "Other"
msgstr ""

#: views/pages/withdraw_requests.php:433
msgid "Yes, Reject Withdrawal"
msgstr ""

#: views/qna/contexts.php:19
msgid "Waiting Since"
msgstr ""

#: views/qna/qna-new.php:18
msgid "Do you have any questions?"
msgstr ""

#: views/qna/qna-new.php:43
msgid "Ask a New Question"
msgstr ""

#: views/qna/qna-new.php:49
msgid "Ask Question"
msgstr ""

#: views/qna/qna-single.php:66
#: views/qna/qna-table.php:116
msgid "Solved"
msgstr ""

#: views/qna/qna-single.php:74
#: views/qna/qna-single.php:76
#: views/qna/qna-table.php:138
#: views/qna/qna-table.php:141
msgid "Archive"
msgstr ""

#: views/qna/qna-single.php:74
#: views/qna/qna-single.php:76
msgid "Un-Archive"
msgstr ""

#: views/qna/qna-single.php:127
msgid "ago"
msgstr ""

#: views/qna/qna-single.php:195
msgid "Do You Want to Delete This Question?"
msgstr ""

#: views/qna/qna-single.php:196
#: views/qna/qna-table.php:178
msgid "All the replies also will be deleted."
msgstr ""

#: views/qna/qna-table.php:67
msgid "This conversation is important"
msgstr ""

#: views/qna/qna-table.php:67
msgid "Mark this conversation as important"
msgstr ""

#: views/qna/qna-table.php:116
msgid "Unresolved Yet"
msgstr ""

#: views/qna/qna-table.php:138
#: views/qna/qna-table.php:141
msgid "Un-archive"
msgstr ""

#: views/qna/qna-table.php:147
msgid "Mark as Read"
msgstr ""

#: views/qna/qna-table.php:147
#: views/qna/qna-table.php:150
msgid "Mark as Unread"
msgstr ""

#: views/qna/qna-table.php:150
msgid "Mark as read"
msgstr ""

#: views/qna/qna-table.php:177
msgid "Delete This Question?"
msgstr ""

#: views/quiz/attempt-details.php:30
msgid "Attempt not found or access permission denied"
msgstr ""

#: views/quiz/attempt-details.php:334
msgid "Quiz Overview"
msgstr ""

#: views/quiz/attempt-details.php:730
msgid "Incorrect"
msgstr ""

#: views/quiz/attempt-details.php:743
msgid "Mark as correct"
msgstr ""

#: views/quiz/attempt-details.php:747
msgid "Mark as In correct"
msgstr ""

#: views/quiz/attempt-table.php:136
msgid "Student:"
msgstr ""

#: views/quiz/attempt-table.php:209
msgid "Would you like to delete Quiz Attempt permanently? We suggest you proceed with caution."
msgstr ""

#: views/quiz/contexts.php:17
msgid "Quiz Info"
msgstr ""

#: views/quiz/contexts.php:21
#: views/quiz/contexts.php:72
#: views/quiz/contexts.php:99
#: assets/js/tutor-front.js:977
msgid "Correct Answer"
msgstr ""

#: views/quiz/contexts.php:22
#: views/quiz/contexts.php:73
msgid "Incorrect Answer"
msgstr ""

#: views/quiz/contexts.php:65
msgid "Attempt By"
msgstr ""

#: views/quiz/contexts.php:69
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:32
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:37
msgid "Attempt Time"
msgstr ""

#: views/quiz/contexts.php:95
#: assets/js/tutor-course-builder.js:25149
msgid "No"
msgstr ""

#: views/quiz/contexts.php:98
msgid "Given Answer"
msgstr ""

#: views/quiz/header-context/course-single-previous-attempts.php:50
msgid "Passing Marks"
msgstr ""

#: views/quiz/instructor-feedback.php:23
msgid "Instructor Feedback"
msgstr ""

#: views/quiz/instructor-feedback.php:37
#: assets/js/tutor.js:3042
msgid "Updated"
msgstr ""

#: views/template-import/templates-list.php:36
#: assets/js/tutor-import-export.js:8890
#: assets/js/tutor-import-export.js:9108
msgid "Import"
msgstr ""

#: views/template-import/templates-list.php:55
msgid "Coming soon"
msgstr ""

#: views/template-import/templates-list.php:68
msgid "No template available."
msgstr ""

#: views/template-import/templates.php:24
msgid "Leverage the collection of magnificent Tutor starter templates to make a jump start."
msgstr ""

#: views/template-import/templates.php:86
msgid "Choose your color palette and continue with your design"
msgstr ""

#: views/template-import/templates.php:100
msgid "Include Tutor LMS demo courses"
msgstr ""

#: assets/js/tutor-addon-list.js:225
#: assets/js/tutor-coupon.js:587
#: assets/js/tutor-course-builder.js:3574
#: assets/js/tutor-course-builder.js:11136
#: assets/js/tutor-import-export.js:228
#: assets/js/tutor-order-details.js:227
#: assets/js/tutor-payment-settings.js:228
#: assets/js/tutor-tax-settings.js:227
msgid "Public"
msgstr ""

#: assets/js/tutor-addon-list.js:228
#: assets/js/tutor-coupon.js:590
#: assets/js/tutor-course-builder.js:3577
#: assets/js/tutor-course-builder.js:11139
#: assets/js/tutor-import-export.js:231
#: assets/js/tutor-order-details.js:230
#: assets/js/tutor-payment-settings.js:231
#: assets/js/tutor-tax-settings.js:230
msgid "Password Protected"
msgstr ""

#: assets/js/tutor-addon-list.js:443
#: assets/js/tutor-coupon.js:983
#: assets/js/tutor-course-builder.js:5941
#: assets/js/tutor-import-export.js:446
#: assets/js/tutor-order-details.js:445
#: assets/js/tutor-payment-settings.js:446
#: assets/js/tutor-tax-settings.js:445
msgid "Oops! Something went wrong"
msgstr ""

#: assets/js/tutor-addon-list.js:445
#: assets/js/tutor-coupon.js:985
#: assets/js/tutor-course-builder.js:5943
#: assets/js/tutor-import-export.js:448
#: assets/js/tutor-order-details.js:447
#: assets/js/tutor-payment-settings.js:448
#: assets/js/tutor-tax-settings.js:447
msgid "Try the following steps to resolve the issue:"
msgstr ""

#: assets/js/tutor-addon-list.js:445
#: assets/js/tutor-coupon.js:985
#: assets/js/tutor-course-builder.js:5943
#: assets/js/tutor-import-export.js:448
#: assets/js/tutor-order-details.js:447
#: assets/js/tutor-payment-settings.js:448
#: assets/js/tutor-tax-settings.js:447
msgid "Refresh the page."
msgstr ""

#: assets/js/tutor-addon-list.js:445
#: assets/js/tutor-coupon.js:985
#: assets/js/tutor-course-builder.js:5943
#: assets/js/tutor-import-export.js:448
#: assets/js/tutor-order-details.js:447
#: assets/js/tutor-payment-settings.js:448
#: assets/js/tutor-tax-settings.js:447
msgid "Clear your browser cache."
msgstr ""

#: assets/js/tutor-addon-list.js:447
#: assets/js/tutor-coupon.js:987
#: assets/js/tutor-course-builder.js:5945
#: assets/js/tutor-import-export.js:450
#: assets/js/tutor-order-details.js:449
#: assets/js/tutor-payment-settings.js:450
#: assets/js/tutor-tax-settings.js:449
msgid "Ensure the Free and Pro plugins are on the same version."
msgstr ""

#: assets/js/tutor-addon-list.js:459
#: assets/js/tutor-coupon.js:999
#: assets/js/tutor-course-builder.js:5957
#: assets/js/tutor-import-export.js:462
#: assets/js/tutor-order-details.js:461
#: assets/js/tutor-payment-settings.js:462
#: assets/js/tutor-tax-settings.js:461
msgid "Reload"
msgstr ""

#: assets/js/tutor-addon-list.js:461
#: assets/js/tutor-coupon.js:1001
#: assets/js/tutor-course-builder.js:5959
#: assets/js/tutor-import-export.js:464
#: assets/js/tutor-order-details.js:463
#: assets/js/tutor-payment-settings.js:464
#: assets/js/tutor-tax-settings.js:463
msgid "Still having trouble?"
msgstr ""

#: assets/js/tutor-addon-list.js:461
#: assets/js/tutor-coupon.js:1001
#: assets/js/tutor-course-builder.js:5959
#: assets/js/tutor-import-export.js:464
#: assets/js/tutor-order-details.js:463
#: assets/js/tutor-payment-settings.js:464
#: assets/js/tutor-tax-settings.js:463
msgid "Contact"
msgstr ""

#: assets/js/tutor-addon-list.js:463
#: assets/js/tutor-coupon.js:1003
#: assets/js/tutor-course-builder.js:5961
#: assets/js/tutor-import-export.js:466
#: assets/js/tutor-order-details.js:465
#: assets/js/tutor-payment-settings.js:466
#: assets/js/tutor-tax-settings.js:465
msgid "Support"
msgstr ""

#: assets/js/tutor-addon-list.js:463
#: assets/js/tutor-coupon.js:1003
#: assets/js/tutor-course-builder.js:5961
#: assets/js/tutor-import-export.js:466
#: assets/js/tutor-order-details.js:465
#: assets/js/tutor-payment-settings.js:466
#: assets/js/tutor-tax-settings.js:465
msgid "for assistance."
msgstr ""

#: assets/js/tutor-addon-list.js:6275
msgid "Install the following plugin(s) to enable this addon."
msgstr ""

#. translators: %s is the addon name
#: assets/js/tutor-addon-list.js:6277
msgid "The following plugin(s) will be activated upon activating the '%s'."
msgstr ""

#. translators: %s is the addon name
#: assets/js/tutor-addon-list.js:6279
msgid "The following plugin(s) will be installed upon activating the '%s'."
msgstr ""

#: assets/js/tutor-addon-list.js:6297
msgid "Installing..."
msgstr ""

#: assets/js/tutor-addon-list.js:6297
msgid "Activating..."
msgstr ""

#: assets/js/tutor-addon-list.js:6324
msgid "Activate"
msgstr ""

#: assets/js/tutor-addon-list.js:6324
msgid "Install & Activate"
msgstr ""

#: assets/js/tutor-addon-list.js:6387
msgid "Go to Settings"
msgstr ""

#: assets/js/tutor-addon-list.js:6494
msgid "Addon enabled successfully."
msgstr ""

#: assets/js/tutor-addon-list.js:6494
msgid "Addon disabled  successfully."
msgstr ""

#: assets/js/tutor-addon-list.js:6546
msgid "Available in Pro"
msgstr ""

#: assets/js/tutor-addon-list.js:6633
#: assets/js/tutor-course-builder.js:8980
msgid "Empty state banner"
msgstr ""

#: assets/js/tutor-addon-list.js:6634
msgid "No matching results found."
msgstr ""

#: assets/js/tutor-addon-list.js:6660
msgid "Get All of Add-Ons for a Single Price"
msgstr ""

#: assets/js/tutor-addon-list.js:6664
msgid "Unlock all add-ons with one payment! Easily enable them and customize for enhanced functionality and usability. Tailor your experience effortlessly."
msgstr ""

#: assets/js/tutor-addon-list.js:6738
msgid "Active Addons"
msgstr ""

#: assets/js/tutor-addon-list.js:6749
msgid "Available Addons"
msgstr ""

#: assets/js/tutor-admin.js:305
#: assets/js/tutor-admin.js:319
msgid "Amount must be less than 100"
msgstr ""

#: assets/js/tutor-admin.js:582
msgid "Reset Successful"
msgstr ""

#. translators: %s: Reset settings title
#: assets/js/tutor-admin.js:584
msgid "All modified settings of %s have been changed to default."
msgstr ""

#: assets/js/tutor-admin.js:799
#: assets/js/tutor-front.js:1412
#: assets/js/tutor-setup.js:773
msgid "No item found"
msgstr ""

#: assets/js/tutor-admin.js:851
#: assets/js/tutor-front.js:1464
#: assets/js/tutor-setup.js:825
msgid "Search ..."
msgstr ""

#. translators: %1$s is the Account name and %2$s is the Amount
#: assets/js/tutor-admin.js:969
msgid "You are approving %1$s withdrawal request for %2$s. Are you sure you want to approve?"
msgstr ""

#. translators: %1$s is the Account name and %2$s is the Amount
#: assets/js/tutor-admin.js:991
msgid "You are rejecting %1$s withdrawal request for %2$s. Are you sure you want to reject?"
msgstr ""

#: assets/js/tutor-admin.js:1024
#: assets/js/tutor-admin.js:1057
#: assets/js/tutor-front.js:2404
msgid "Something went wrong, please try again!"
msgstr ""

#: assets/js/tutor-admin.js:1078
msgid "Withdraw Reject Reason"
msgstr ""

#: assets/js/tutor-admin.js:1119
#: assets/js/tutor-admin.js:1270
#: assets/js/tutor-admin.js:3195
#: assets/js/tutor-front.js:1735
#: assets/js/tutor-setup.js:998
#: assets/js/tutor.js:2027
msgid "Operation failed"
msgstr ""

#: assets/js/tutor-admin.js:1161
msgid "Copied"
msgstr ""

#: assets/js/tutor-admin.js:1479
#: assets/js/tutor-admin.js:2470
#: assets/js/tutor-front.js:736
#: assets/js/tutor-front.js:1882
#: assets/js/tutor-front.js:2748
#: assets/js/tutor-front.js:2753
#: assets/js/tutor-front.js:2761
#: assets/js/tutor-front.js:2817
#: assets/js/tutor-front.js:3838
#: assets/js/tutor-front.js:4271
msgid "Warning"
msgstr ""

#: assets/js/tutor-admin.js:1479
#: assets/js/tutor-front.js:1882
msgid "Nothing was selected for bulk action."
msgstr ""

#: assets/js/tutor-admin.js:1520
#: assets/js/tutor-front.js:1923
msgid "Select checkbox for action"
msgstr ""

#: assets/js/tutor-admin.js:1547
#: assets/js/tutor-front.js:1950
msgid "Something went wrong, please try again "
msgstr ""

#: assets/js/tutor-admin.js:1704
#: assets/js/tutor-front.js:2107
msgid "Successfully deleted "
msgstr ""

#: assets/js/tutor-admin.js:1708
#: assets/js/tutor-front.js:2111
msgid "Delete failed "
msgstr ""

#: assets/js/tutor-admin.js:1775
msgid "Yes, Restore Settings\" data-heading=\"Restore Previous Settings?"
msgstr ""

#: assets/js/tutor-admin.js:1808
msgid "Reset all settings to default successfully!"
msgstr ""

#: assets/js/tutor-admin.js:1856
msgid "Applied settings successfully!"
msgstr ""

#: assets/js/tutor-admin.js:1905
msgid "Data deleted successfully!"
msgstr ""

#: assets/js/tutor-admin.js:2126
msgid "Something went wrong, please try again after refreshing page"
msgstr ""

#: assets/js/tutor-admin.js:2167
msgid "API key & secret generated successfully"
msgstr ""

#: assets/js/tutor-admin.js:2225
msgid "API key permission updated successfully"
msgstr ""

#: assets/js/tutor-admin.js:2545
#: assets/js/tutor-front.js:4132
msgid "Success!"
msgstr ""

#: assets/js/tutor-admin.js:3029
#: assets/js/tutor-admin.js:3279
msgid "Select or Upload Media Of Your Choice"
msgstr ""

#: assets/js/tutor-admin.js:3031
#: assets/js/tutor-admin.js:3281
msgid "Upload media"
msgstr ""

#: assets/js/tutor-admin.js:3113
msgid "New Instructor Added"
msgstr ""

#: assets/js/tutor-admin.js:3329
msgid "Do you want to save without any category?"
msgstr ""

#: assets/js/tutor-coupon.js:3134
#: assets/js/tutor-course-builder.js:29973
#: assets/js/tutor-import-export.js:5659
#: assets/js/tutor-order-details.js:5313
#: assets/js/tutor-payment-settings.js:6458
#: assets/js/tutor-tax-settings.js:6090
msgid "Limit exceeded"
msgstr ""

#: assets/js/tutor-coupon.js:3467
#: assets/js/tutor-coupon.js:3613
msgid "course item"
msgstr ""

#: assets/js/tutor-coupon.js:3589
msgid "Bundles"
msgstr ""

#. translators: %s is the starting price of the plan
#: assets/js/tutor-coupon.js:3626
#: assets/js/tutor-coupon.js:5363
msgid "Starting from %s"
msgstr ""

#: assets/js/tutor-coupon.js:3719
#: assets/js/tutor-coupon.js:3741
#: assets/js/tutor-coupon.js:3770
#: assets/js/tutor-course-builder.js:9407
#: assets/js/tutor-course-builder.js:9429
#: assets/js/tutor-course-builder.js:9458
#: assets/js/tutor-course-builder.js:9869
#: assets/js/tutor-course-builder.js:9870
#: assets/js/tutor-course-builder.js:10068
#: assets/js/tutor-course-builder.js:10081
#: assets/js/tutor-course-builder.js:10624
msgid "Until cancelled"
msgstr ""

#: assets/js/tutor-coupon.js:4115
msgid "Membership Plans"
msgstr ""

#: assets/js/tutor-coupon.js:4279
#: assets/js/tutor-course-builder.js:7995
#: assets/js/tutor-course-builder.js:8825
#: assets/js/tutor-course-builder.js:19424
#: assets/js/tutor-import-export.js:6721
msgid "Add"
msgstr ""

#: assets/js/tutor-coupon.js:4987
#: assets/js/tutor-course-builder.js:16176
#: assets/js/tutor-order-details.js:7215
#: assets/js/tutor-payment-settings.js:9151
#: assets/js/tutor-tax-settings.js:7210
msgid "No options available"
msgstr ""

#: assets/js/tutor-coupon.js:5129
#: assets/js/tutor-course-builder.js:29767
#: assets/js/tutor-order-details.js:6120
#: assets/js/tutor-payment-settings.js:10446
#: assets/js/tutor-tax-settings.js:7354
msgid "This field is required"
msgstr ""

#: assets/js/tutor-coupon.js:5147
#: assets/js/tutor-course-builder.js:29785
#: assets/js/tutor-order-details.js:6138
#: assets/js/tutor-payment-settings.js:10464
#: assets/js/tutor-tax-settings.js:7372
msgid "The field is required"
msgstr ""

#: assets/js/tutor-coupon.js:5155
#: assets/js/tutor-course-builder.js:29793
#: assets/js/tutor-order-details.js:6146
#: assets/js/tutor-payment-settings.js:10472
#: assets/js/tutor-tax-settings.js:7380
msgid "Invalid date entered!"
msgstr ""

#: assets/js/tutor-coupon.js:5173
#: assets/js/tutor-course-builder.js:29811
#: assets/js/tutor-order-details.js:6164
#: assets/js/tutor-payment-settings.js:10490
#: assets/js/tutor-tax-settings.js:7398
msgid "Invalid time entered!"
msgstr ""

#: assets/js/tutor-coupon.js:5253
msgid "All membership plans"
msgstr ""

#: assets/js/tutor-coupon.js:5265
msgid "Specific membership plans"
msgstr ""

#: assets/js/tutor-coupon.js:5321
#: assets/js/tutor-order-details.js:9345
msgid "Discount Type"
msgstr ""

#: assets/js/tutor-coupon.js:5333
#: assets/js/tutor-order-details.js:9369
msgid "Discount Value"
msgstr ""

#: assets/js/tutor-coupon.js:5436
msgid "Select items"
msgstr ""

#: assets/js/tutor-coupon.js:5443
msgid "Add Items"
msgstr ""

#: assets/js/tutor-coupon.js:5951
#: assets/js/tutor-course-builder.js:13238
#: assets/js/tutor-order-details.js:7471
#: assets/js/tutor-payment-settings.js:9407
#: assets/js/tutor-tax-settings.js:8880
msgid "Formal"
msgstr ""

#: assets/js/tutor-coupon.js:5954
#: assets/js/tutor-course-builder.js:13241
#: assets/js/tutor-order-details.js:7474
#: assets/js/tutor-payment-settings.js:9410
#: assets/js/tutor-tax-settings.js:8883
msgid "Casual"
msgstr ""

#: assets/js/tutor-coupon.js:5957
#: assets/js/tutor-course-builder.js:13244
#: assets/js/tutor-order-details.js:7477
#: assets/js/tutor-payment-settings.js:9413
#: assets/js/tutor-tax-settings.js:8886
msgid "Professional"
msgstr ""

#: assets/js/tutor-coupon.js:5960
#: assets/js/tutor-course-builder.js:13247
#: assets/js/tutor-order-details.js:7480
#: assets/js/tutor-payment-settings.js:9416
#: assets/js/tutor-tax-settings.js:8889
msgid "Enthusiastic"
msgstr ""

#: assets/js/tutor-coupon.js:5963
#: assets/js/tutor-course-builder.js:13250
#: assets/js/tutor-order-details.js:7483
#: assets/js/tutor-payment-settings.js:9419
#: assets/js/tutor-tax-settings.js:8892
msgid "Informational"
msgstr ""

#: assets/js/tutor-coupon.js:5966
#: assets/js/tutor-course-builder.js:13253
#: assets/js/tutor-order-details.js:7486
#: assets/js/tutor-payment-settings.js:9422
#: assets/js/tutor-tax-settings.js:8895
msgid "Funny"
msgstr ""

#: assets/js/tutor-coupon.js:5973
#: assets/js/tutor-course-builder.js:13260
#: assets/js/tutor-order-details.js:7493
#: assets/js/tutor-payment-settings.js:9429
#: assets/js/tutor-tax-settings.js:8902
msgid "Essay"
msgstr ""

#: assets/js/tutor-coupon.js:5976
#: assets/js/tutor-course-builder.js:13263
#: assets/js/tutor-order-details.js:7496
#: assets/js/tutor-payment-settings.js:9432
#: assets/js/tutor-tax-settings.js:8905
msgid "Paragraph"
msgstr ""

#: assets/js/tutor-coupon.js:5979
#: assets/js/tutor-course-builder.js:13266
#: assets/js/tutor-order-details.js:7499
#: assets/js/tutor-payment-settings.js:9435
#: assets/js/tutor-tax-settings.js:8908
msgid "Outline"
msgstr ""

#: assets/js/tutor-coupon.js:6003
#: assets/js/tutor-course-builder.js:5562
#: assets/js/tutor-order-details.js:7523
#: assets/js/tutor-payment-settings.js:9459
#: assets/js/tutor-tax-settings.js:8932
msgid "Character Limit"
msgstr ""

#: assets/js/tutor-coupon.js:6024
#: assets/js/tutor-course-builder.js:5583
#: assets/js/tutor-order-details.js:7544
#: assets/js/tutor-payment-settings.js:9480
#: assets/js/tutor-tax-settings.js:8953
msgid "Tone"
msgstr ""

#: assets/js/tutor-coupon.js:6033
#: assets/js/tutor-course-builder.js:5592
#: assets/js/tutor-order-details.js:7553
#: assets/js/tutor-payment-settings.js:9489
#: assets/js/tutor-tax-settings.js:8962
msgid "Format"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Mastering Digital Marketing: A Complete Guide"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "The Ultimate Photoshop Course for Beginners"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Python Programming: From Zero to Hero"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Creative Writing Essentials: Unlock Your Storytelling Potential"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "The Complete Guide to Web Development with React"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Master Public Speaking: Deliver Powerful Presentations"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Excel for Business: From Basics to Advanced Analytics"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Fitness Fundamentals: Build Strength and Confidence"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Photography Made Simple: Capture Stunning Shots"
msgstr ""

#: assets/js/tutor-coupon.js:6345
#: assets/js/tutor-course-builder.js:2915
#: assets/js/tutor-order-details.js:8020
#: assets/js/tutor-payment-settings.js:9640
#: assets/js/tutor-tax-settings.js:9274
msgid "Financial Freedom: Learn the Basics of Investing"
msgstr ""

#: assets/js/tutor-coupon.js:6515
#: assets/js/tutor-course-builder.js:3085
#: assets/js/tutor-order-details.js:8190
#: assets/js/tutor-payment-settings.js:9810
#: assets/js/tutor-tax-settings.js:9444
msgid "Craft Your Course Description"
msgstr ""

#: assets/js/tutor-coupon.js:6516
#: assets/js/tutor-course-builder.js:3086
#: assets/js/tutor-order-details.js:8191
#: assets/js/tutor-payment-settings.js:9811
#: assets/js/tutor-tax-settings.js:9445
msgid "Provide a brief overview of your course topic, target audience, and key takeaways"
msgstr ""

#: assets/js/tutor-coupon.js:6533
#: assets/js/tutor-course-builder.js:3103
#: assets/js/tutor-course-builder.js:7093
#: assets/js/tutor-order-details.js:8208
#: assets/js/tutor-payment-settings.js:7800
#: assets/js/tutor-payment-settings.js:9828
#: assets/js/tutor-tax-settings.js:9462
msgid "Inspire Me"
msgstr ""

#: assets/js/tutor-coupon.js:6625
#: assets/js/tutor-course-builder.js:3195
#: assets/js/tutor-order-details.js:8300
#: assets/js/tutor-payment-settings.js:9920
#: assets/js/tutor-tax-settings.js:9554
msgid "Rephrase"
msgstr ""

#: assets/js/tutor-coupon.js:6631
#: assets/js/tutor-course-builder.js:3201
#: assets/js/tutor-order-details.js:8306
#: assets/js/tutor-payment-settings.js:9926
#: assets/js/tutor-tax-settings.js:9560
msgid "Make Shorter"
msgstr ""

#: assets/js/tutor-coupon.js:6638
#: assets/js/tutor-course-builder.js:3208
#: assets/js/tutor-order-details.js:8313
#: assets/js/tutor-payment-settings.js:9933
#: assets/js/tutor-tax-settings.js:9567
msgid "Change Tone"
msgstr ""

#: assets/js/tutor-coupon.js:6649
#: assets/js/tutor-course-builder.js:3219
#: assets/js/tutor-order-details.js:8324
#: assets/js/tutor-payment-settings.js:9944
#: assets/js/tutor-tax-settings.js:9578
msgid "Translate to"
msgstr ""

#: assets/js/tutor-coupon.js:6659
#: assets/js/tutor-course-builder.js:3229
#: assets/js/tutor-order-details.js:8334
#: assets/js/tutor-payment-settings.js:9954
#: assets/js/tutor-tax-settings.js:9588
msgid "Write as Bullets"
msgstr ""

#: assets/js/tutor-coupon.js:6665
#: assets/js/tutor-course-builder.js:3235
#: assets/js/tutor-order-details.js:8340
#: assets/js/tutor-payment-settings.js:9960
#: assets/js/tutor-tax-settings.js:9594
msgid "Make Longer"
msgstr ""

#: assets/js/tutor-coupon.js:6671
#: assets/js/tutor-course-builder.js:3241
#: assets/js/tutor-order-details.js:8346
#: assets/js/tutor-payment-settings.js:9966
#: assets/js/tutor-tax-settings.js:9600
msgid "Simplify Language"
msgstr ""

#: assets/js/tutor-coupon.js:6738
#: assets/js/tutor-course-builder.js:3308
#: assets/js/tutor-course-builder.js:7112
#: assets/js/tutor-course-builder.js:31878
#: assets/js/tutor-course-builder.js:32874
#: assets/js/tutor-order-details.js:8413
#: assets/js/tutor-payment-settings.js:7819
#: assets/js/tutor-payment-settings.js:10033
#: assets/js/tutor-tax-settings.js:9667
msgid "Generate Now"
msgstr ""

#: assets/js/tutor-coupon.js:6743
#: assets/js/tutor-course-builder.js:3313
#: assets/js/tutor-course-builder.js:7112
#: assets/js/tutor-order-details.js:8418
#: assets/js/tutor-payment-settings.js:7819
#: assets/js/tutor-payment-settings.js:10038
#: assets/js/tutor-tax-settings.js:9672
msgid "Generate Again"
msgstr ""

#: assets/js/tutor-coupon.js:6750
#: assets/js/tutor-course-builder.js:1221
#: assets/js/tutor-course-builder.js:3320
#: assets/js/tutor-order-details.js:8425
#: assets/js/tutor-payment-settings.js:7463
#: assets/js/tutor-payment-settings.js:10045
#: assets/js/tutor-tax-settings.js:9679
msgid "Use This"
msgstr ""

#: assets/js/tutor-coupon.js:6782
#: assets/js/tutor-course-builder.js:37849
#: assets/js/tutor-order-details.js:8457
#: assets/js/tutor-payment-settings.js:8632
#: assets/js/tutor-tax-settings.js:9711
msgid "Upgrade to Tutor LMS Pro today and experience the power of "
msgstr ""

#: assets/js/tutor-coupon.js:6785
#: assets/js/tutor-course-builder.js:37852
#: assets/js/tutor-order-details.js:8460
#: assets/js/tutor-payment-settings.js:8635
#: assets/js/tutor-tax-settings.js:9714
msgid "Upgrade your plan to access the AI feature"
msgstr ""

#: assets/js/tutor-coupon.js:6786
#: assets/js/tutor-course-builder.js:37853
#: assets/js/tutor-order-details.js:8461
#: assets/js/tutor-payment-settings.js:8636
#: assets/js/tutor-tax-settings.js:9715
msgid "Don’t miss out on this game-changing feature!"
msgstr ""

#: assets/js/tutor-coupon.js:6787
#: assets/js/tutor-course-builder.js:37854
#: assets/js/tutor-order-details.js:8462
#: assets/js/tutor-payment-settings.js:8637
#: assets/js/tutor-tax-settings.js:9716
msgid "Generate a complete course outline in seconds!"
msgstr ""

#: assets/js/tutor-coupon.js:6787
#: assets/js/tutor-course-builder.js:37854
#: assets/js/tutor-order-details.js:8462
#: assets/js/tutor-payment-settings.js:8637
#: assets/js/tutor-tax-settings.js:9716
msgid "Let the AI Studio create Quizzes on your behalf and give your brain a well-deserved break."
msgstr ""

#: assets/js/tutor-coupon.js:6787
#: assets/js/tutor-course-builder.js:37854
#: assets/js/tutor-order-details.js:8462
#: assets/js/tutor-payment-settings.js:8637
#: assets/js/tutor-tax-settings.js:9716
msgid "Generate images, customize backgrounds, and even remove unwanted objects with ease."
msgstr ""

#: assets/js/tutor-coupon.js:6787
#: assets/js/tutor-course-builder.js:37854
#: assets/js/tutor-order-details.js:8462
#: assets/js/tutor-payment-settings.js:8637
#: assets/js/tutor-tax-settings.js:9716
msgid "Say goodbye to typos and grammar errors with AI-powered copy editing."
msgstr ""

#: assets/js/tutor-coupon.js:6797
#: assets/js/tutor-course-builder.js:11573
#: assets/js/tutor-course-builder.js:28265
#: assets/js/tutor-course-builder.js:29137
#: assets/js/tutor-course-builder.js:37864
#: assets/js/tutor-order-details.js:8472
#: assets/js/tutor-payment-settings.js:8647
#: assets/js/tutor-tax-settings.js:9726
msgid "Get Tutor LMS Pro"
msgstr ""

#: assets/js/tutor-coupon.js:7144
#: assets/js/tutor-course-builder.js:14738
#: assets/js/tutor-order-details.js:8756
#: assets/js/tutor-payment-settings.js:10599
#: assets/js/tutor-tax-settings.js:10073
msgid "Set OpenAI API key"
msgstr ""

#: assets/js/tutor-coupon.js:7157
#: assets/js/tutor-course-builder.js:14751
#: assets/js/tutor-order-details.js:8769
#: assets/js/tutor-payment-settings.js:10612
#: assets/js/tutor-tax-settings.js:10086
msgid "Connect API KEY"
msgstr ""

#: assets/js/tutor-coupon.js:7160
#: assets/js/tutor-course-builder.js:14754
#: assets/js/tutor-order-details.js:8772
#: assets/js/tutor-payment-settings.js:10615
#: assets/js/tutor-tax-settings.js:10089
msgid "API is not connected"
msgstr ""

#: assets/js/tutor-coupon.js:7162
#: assets/js/tutor-course-builder.js:14756
#: assets/js/tutor-order-details.js:8774
#: assets/js/tutor-payment-settings.js:10617
#: assets/js/tutor-tax-settings.js:10091
msgid "Please, ask your Admin to connect the API with Tutor LMS Pro."
msgstr ""

#. translators: %1$s and %2$s are opening and closing anchor tags for the "OpenAI User settings" link
#: assets/js/tutor-coupon.js:7171
#: assets/js/tutor-course-builder.js:14765
#: assets/js/tutor-order-details.js:8783
#: assets/js/tutor-payment-settings.js:10626
#: assets/js/tutor-tax-settings.js:10100
msgid "Find your Secret API key in your %1$sOpenAI User settings%2$s and paste it here to connect OpenAI with your Tutor LMS website."
msgstr ""

#: assets/js/tutor-coupon.js:7176
#: assets/js/tutor-course-builder.js:14770
#: assets/js/tutor-order-details.js:8788
#: assets/js/tutor-payment-settings.js:10631
#: assets/js/tutor-tax-settings.js:10105
msgid "The page will reload after submission. Make sure to save the course information."
msgstr ""

#: assets/js/tutor-coupon.js:7184
#: assets/js/tutor-course-builder.js:14778
#: assets/js/tutor-order-details.js:8796
#: assets/js/tutor-payment-settings.js:10639
#: assets/js/tutor-tax-settings.js:10113
msgid "OpenAI API key"
msgstr ""

#: assets/js/tutor-coupon.js:7185
#: assets/js/tutor-course-builder.js:14779
#: assets/js/tutor-order-details.js:8797
#: assets/js/tutor-payment-settings.js:10640
#: assets/js/tutor-tax-settings.js:10114
msgid "Enter your OpenAI API key"
msgstr ""

#: assets/js/tutor-coupon.js:7193
#: assets/js/tutor-course-builder.js:14787
#: assets/js/tutor-order-details.js:8805
#: assets/js/tutor-payment-settings.js:10648
#: assets/js/tutor-tax-settings.js:10122
msgid "Enable OpenAI"
msgstr ""

#: assets/js/tutor-coupon.js:7210
#: assets/js/tutor-coupon.js:11989
#: assets/js/tutor-course-builder.js:10441
#: assets/js/tutor-course-builder.js:12219
#: assets/js/tutor-course-builder.js:14804
#: assets/js/tutor-course-builder.js:18340
#: assets/js/tutor-course-builder.js:19671
#: assets/js/tutor-course-builder.js:25041
#: assets/js/tutor-order-details.js:8822
#: assets/js/tutor-payment-settings.js:10665
#: assets/js/tutor-payment-settings.js:12536
#: assets/js/tutor-tax-settings.js:10139
msgid "Save"
msgstr ""

#: assets/js/tutor-coupon.js:7357
#: assets/js/tutor-course-builder.js:34401
#: assets/js/tutor-order-details.js:8969
#: assets/js/tutor-payment-settings.js:10194
#: assets/js/tutor-tax-settings.js:10286
msgid "Create a Compelling Title"
msgstr ""

#: assets/js/tutor-coupon.js:7358
#: assets/js/tutor-course-builder.js:34402
#: assets/js/tutor-order-details.js:8970
#: assets/js/tutor-payment-settings.js:10195
#: assets/js/tutor-tax-settings.js:10287
msgid "Describe the main focus of your course in a few words"
msgstr ""

#: assets/js/tutor-coupon.js:7622
msgid "Coupon Info"
msgstr ""

#: assets/js/tutor-coupon.js:7622
msgid "Create a coupon code or set up automatic discounts."
msgstr ""

#. translators: %s is the current year (e.g., 2025)
#: assets/js/tutor-coupon.js:7641
msgid "e.g. Summer Sale %s"
msgstr ""

#: assets/js/tutor-coupon.js:7652
msgid "Coupon Code"
msgstr ""

#: assets/js/tutor-coupon.js:7653
msgid "e.g. SUMMER20"
msgstr ""

#: assets/js/tutor-coupon.js:7662
msgid "Generate Code"
msgstr ""

#: assets/js/tutor-coupon.js:7668
msgid "Coupon status"
msgstr ""

#: assets/js/tutor-coupon.js:7757
msgid "Usage Limitation"
msgstr ""

#: assets/js/tutor-coupon.js:7766
msgid "Limit number of times this coupon can be used in total"
msgstr ""

#: assets/js/tutor-coupon.js:7781
#: assets/js/tutor-coupon.js:7808
#: assets/js/tutor-coupon.js:8714
#: assets/js/tutor-course-builder.js:11019
#: assets/js/tutor-course-builder.js:11045
msgid "0"
msgstr ""

#: assets/js/tutor-coupon.js:7793
msgid "Limit number of times this coupon can be used by a customer"
msgstr ""

#: assets/js/tutor-coupon.js:7962
msgid "Coupon preview will appear here"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:7987
msgid "%s off all courses"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:7990
msgid "%s off all bundles"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:7993
msgid "%s off all courses and bundles"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:7996
msgid "%s off all membership plans"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:7999
msgid "%s off specific courses"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:8002
msgid "%s off specific bundles"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:8005
msgid "%s off specific category"
msgstr ""

#. translators: %s: Percentage or Amount of discount
#: assets/js/tutor-coupon.js:8008
msgid "%s off specific membership plans"
msgstr ""

#: assets/js/tutor-coupon.js:8027
msgid "today"
msgstr ""

#: assets/js/tutor-coupon.js:8027
msgid "tomorrow"
msgstr ""

#. translators: %d is the number of times the coupon was used
#: assets/js/tutor-coupon.js:8030
msgid "Total %d times used"
msgstr ""

#. translators: %s is the date from which coupon is active
#: assets/js/tutor-coupon.js:8032
msgid "Active from %s"
msgstr ""

#. translators: %s is the validity end date
#: assets/js/tutor-coupon.js:8053
msgid "Valid until %s"
msgstr ""

#: assets/js/tutor-coupon.js:8066
msgid "Right circle icon"
msgstr ""

#: assets/js/tutor-coupon.js:8097
msgid "One use per customer"
msgstr ""

#: assets/js/tutor-coupon.js:8103
msgid "Activity"
msgstr ""

#: assets/js/tutor-coupon.js:8108
msgid "Not active yet"
msgstr ""

#: assets/js/tutor-coupon.js:8565
msgid "Validity"
msgstr ""

#: assets/js/tutor-coupon.js:8569
msgid "Starts from"
msgstr ""

#: assets/js/tutor-coupon.js:8594
msgid "Set end date"
msgstr ""

#: assets/js/tutor-coupon.js:8595
msgid "Leaving the end date blank will make the coupon valid indefinitely."
msgstr ""

#: assets/js/tutor-coupon.js:8610
msgid "Ends in"
msgstr ""

#. translators: %s is the currency symbol, e.g. $, €, ¥
#: assets/js/tutor-coupon.js:8662
msgid "Minimum purchase amount (%s)"
msgstr ""

#: assets/js/tutor-coupon.js:8664
msgid "No minimum requirements"
msgstr ""

#: assets/js/tutor-coupon.js:8670
msgid "Minimum quantity of courses"
msgstr ""

#: assets/js/tutor-coupon.js:8678
msgid "Minimum Purchase Requirements"
msgstr ""

#: assets/js/tutor-coupon.js:8700
msgid "0.00"
msgstr ""

#: assets/js/tutor-coupon.js:11966
msgid "Update Coupon"
msgstr ""

#: assets/js/tutor-coupon.js:11966
msgid "Create Coupon"
msgstr ""

#. translators: %1$s is author's name and %2$s is creation date/time
#: assets/js/tutor-coupon.js:11973
#: assets/js/tutor-order-details.js:10703
msgid "Created by %1$s at %2$s"
msgstr ""

#. translators: %1$s is author's name and %2$s is update date/time
#: assets/js/tutor-coupon.js:11978
#: assets/js/tutor-order-details.js:10708
msgid "Updated by %1$s at %2$s"
msgstr ""

#: assets/js/tutor-course-builder.js:445
#: assets/js/tutor-course-builder.js:478
#: assets/js/tutor-payment-settings.js:11303
#: assets/js/tutor-payment-settings.js:11336
msgid "Back to WordPress Editor"
msgstr ""

#: assets/js/tutor-course-builder.js:451
#: assets/js/tutor-payment-settings.js:11309
msgid "Warning: Switching to the WordPress default editor may cause issues with your current layout, design, and content."
msgstr ""

#: assets/js/tutor-course-builder.js:452
#: assets/js/tutor-payment-settings.js:11310
msgid "Confirm"
msgstr ""

#. translators: %s is the editor name
#: assets/js/tutor-course-builder.js:509
#: assets/js/tutor-payment-settings.js:11367
msgid "Edit with %s"
msgstr ""

#: assets/js/tutor-course-builder.js:619
#: assets/js/tutor-payment-settings.js:11477
msgid "Edit with"
msgstr ""

#: assets/js/tutor-course-builder.js:1127
#: assets/js/tutor-course-builder.js:14000
#: assets/js/tutor-payment-settings.js:7369
#: assets/js/tutor-payment-settings.js:8397
msgid "Magic Fill"
msgstr ""

#: assets/js/tutor-course-builder.js:1181
#: assets/js/tutor-payment-settings.js:7423
msgid "Generated Image"
msgstr ""

#: assets/js/tutor-course-builder.js:2128
msgid "Topic saved successfully"
msgstr ""

#: assets/js/tutor-course-builder.js:2219
msgid "Lesson saved successfully"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A serene classroom setting with books and a chalkboard"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "An abstract representation of innovation and creativity"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A vibrant workspace with a laptop and coffee cup"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A modern design with digital learning icons"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A futuristic cityscape with a glowing pathway"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A peaceful nature scene with soft colors"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A professional boardroom with sleek visuals"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A stack of books with warm, inviting lighting"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A dynamic collage of technology and education themes"
msgstr ""

#: assets/js/tutor-course-builder.js:2693
#: assets/js/tutor-payment-settings.js:6093
msgid "A bold and minimalistic design with striking colors"
msgstr ""

#: assets/js/tutor-course-builder.js:3991
msgid "Paste YouTube Video URL"
msgstr ""

#: assets/js/tutor-course-builder.js:3992
msgid "Paste Vimeo Video URL"
msgstr ""

#: assets/js/tutor-course-builder.js:3993
msgid "Paste External Video URL"
msgstr ""

#: assets/js/tutor-course-builder.js:3994
msgid "Paste Video Shortcode"
msgstr ""

#: assets/js/tutor-course-builder.js:3995
msgid "Paste Embedded Video Code"
msgstr ""

#: assets/js/tutor-course-builder.js:4144
#: assets/js/tutor-course-builder.js:5698
#: assets/js/tutor-course-builder.js:29632
#: assets/js/tutor-course-builder.js:34903
#: assets/js/tutor-payment-settings.js:5955
#: assets/js/tutor-payment-settings.js:10842
msgid "Upload Media"
msgstr ""

#: assets/js/tutor-course-builder.js:4371
msgid "No video source selected"
msgstr ""

#: assets/js/tutor-course-builder.js:4383
msgid "Select from settings"
msgstr ""

#: assets/js/tutor-course-builder.js:4478
msgid "Invalid Shortcode"
msgstr ""

#: assets/js/tutor-course-builder.js:4481
msgid "Invalid URL"
msgstr ""

#: assets/js/tutor-course-builder.js:4484
msgid "Invalid YouTube URL"
msgstr ""

#: assets/js/tutor-course-builder.js:4487
msgid "Invalid Vimeo URL"
msgstr ""

#: assets/js/tutor-course-builder.js:4541
#: assets/js/tutor-course-builder.js:4556
msgid "Add from URL"
msgstr ""

#: assets/js/tutor-course-builder.js:4631
#: assets/js/tutor-course-builder.js:7411
#: assets/js/tutor-course-builder.js:11229
msgid "Upload Thumbnail"
msgstr ""

#: assets/js/tutor-course-builder.js:4632
msgid "Upload a thumbnail image for your video"
msgstr ""

#: assets/js/tutor-course-builder.js:4636
msgid "Replace Thumbnail"
msgstr ""

#: assets/js/tutor-course-builder.js:4665
msgid "Select source"
msgstr ""

#: assets/js/tutor-course-builder.js:4679
msgid "Paste Here"
msgstr ""

#: assets/js/tutor-course-builder.js:4782
msgid "Failed to fetch the video data"
msgstr ""

#: assets/js/tutor-course-builder.js:4803
msgid "Error fetching Vimeo video duration:"
msgstr ""

#: assets/js/tutor-course-builder.js:4872
msgid "Please add an option."
msgstr ""

#: assets/js/tutor-course-builder.js:4878
msgid "Please finish editing all newly created options."
msgstr ""

#: assets/js/tutor-course-builder.js:4884
msgid "Please select a correct answer."
msgstr ""

#: assets/js/tutor-course-builder.js:4895
msgid "Please add titles to all options."
msgstr ""

#: assets/js/tutor-course-builder.js:4905
msgid "Please add images to all options."
msgstr ""

#: assets/js/tutor-course-builder.js:4915
msgid "Please add matched text to all options."
msgstr ""

#: assets/js/tutor-course-builder.js:5001
msgid "Failed to get canvas context"
msgstr ""

#: assets/js/tutor-course-builder.js:5009
msgid "Unknown error occurred"
msgstr ""

#: assets/js/tutor-course-builder.js:5043
msgid "Thumbnail generation timed out"
msgstr ""

#: assets/js/tutor-course-builder.js:5058
msgid "Unsupported video source"
msgstr ""

#: assets/js/tutor-course-builder.js:5270
#: assets/js/tutor-course-builder.js:13600
msgid "Name is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5274
#: assets/js/tutor-course-builder.js:13604
msgid "Meeting Name"
msgstr ""

#: assets/js/tutor-course-builder.js:5275
#: assets/js/tutor-course-builder.js:13605
msgid "Enter meeting name"
msgstr ""

#: assets/js/tutor-course-builder.js:5282
#: assets/js/tutor-course-builder.js:13612
msgid "Summary is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5286
#: assets/js/tutor-course-builder.js:13616
msgid "Meeting Summary"
msgstr ""

#: assets/js/tutor-course-builder.js:5287
#: assets/js/tutor-course-builder.js:13617
msgid "Enter meeting summary"
msgstr ""

#: assets/js/tutor-course-builder.js:5296
msgid "Meeting Start Date"
msgstr ""

#: assets/js/tutor-course-builder.js:5302
msgid "Start date is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5307
msgid "Start date"
msgstr ""

#: assets/js/tutor-course-builder.js:5315
msgid "Start time is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5320
#: assets/js/tutor-course-builder.js:13646
#: assets/js/tutor-course-builder.js:28698
#: assets/js/tutor-course-builder.js:28911
msgid "Start time"
msgstr ""

#: assets/js/tutor-course-builder.js:5327
msgid "Meeting End Date"
msgstr ""

#: assets/js/tutor-course-builder.js:5333
msgid "End date is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5340
msgid "End date should be greater than start date"
msgstr ""

#: assets/js/tutor-course-builder.js:5349
msgid "End date"
msgstr ""

#: assets/js/tutor-course-builder.js:5357
msgid "End time is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5366
msgid "End time should be greater than start time"
msgstr ""

#: assets/js/tutor-course-builder.js:5375
msgid "End time"
msgstr ""

#: assets/js/tutor-course-builder.js:5382
#: assets/js/tutor-course-builder.js:13687
msgid "Timezone is required"
msgstr ""

#: assets/js/tutor-course-builder.js:5387
#: assets/js/tutor-course-builder.js:13692
msgid "Select timezone"
msgstr ""

#: assets/js/tutor-course-builder.js:5398
msgid "Add enrolled students as attendees"
msgstr ""

#: assets/js/tutor-course-builder.js:5415
#: assets/js/tutor-course-builder.js:13764
msgid "Update Meeting"
msgstr ""

#: assets/js/tutor-course-builder.js:5415
#: assets/js/tutor-course-builder.js:13764
msgid "Create Meeting"
msgstr ""

#: assets/js/tutor-course-builder.js:5782
#: assets/js/tutor-payment-settings.js:6039
msgid "Replace Image"
msgstr ""

#: assets/js/tutor-course-builder.js:6900
#: assets/js/tutor-course-builder.js:11550
#: assets/js/tutor-course-builder.js:28130
#: assets/js/tutor-course-builder.js:28468
#: assets/js/tutor-payment-settings.js:7607
msgid "None"
msgstr ""

#: assets/js/tutor-course-builder.js:6904
#: assets/js/tutor-payment-settings.js:7611
msgid "Photo"
msgstr ""

#: assets/js/tutor-course-builder.js:6908
#: assets/js/tutor-payment-settings.js:7615
msgid "Neon"
msgstr ""

#: assets/js/tutor-course-builder.js:6912
#: assets/js/tutor-payment-settings.js:7619
msgid "3D"
msgstr ""

#: assets/js/tutor-course-builder.js:6916
#: assets/js/tutor-payment-settings.js:7623
msgid "Painting"
msgstr ""

#: assets/js/tutor-course-builder.js:6920
#: assets/js/tutor-payment-settings.js:7627
msgid "Sketch"
msgstr ""

#: assets/js/tutor-course-builder.js:6924
#: assets/js/tutor-payment-settings.js:7631
msgid "Concept"
msgstr ""

#: assets/js/tutor-course-builder.js:6928
#: assets/js/tutor-payment-settings.js:7635
msgid "Illustration"
msgstr ""

#: assets/js/tutor-course-builder.js:6932
#: assets/js/tutor-payment-settings.js:7639
msgid "Dreamy"
msgstr ""

#: assets/js/tutor-course-builder.js:6936
#: assets/js/tutor-payment-settings.js:7643
msgid "Filmic"
msgstr ""

#: assets/js/tutor-course-builder.js:6940
#: assets/js/tutor-payment-settings.js:7647
msgid "Retro"
msgstr ""

#: assets/js/tutor-course-builder.js:6944
#: assets/js/tutor-payment-settings.js:7651
msgid "Black & White"
msgstr ""

#: assets/js/tutor-course-builder.js:7072
#: assets/js/tutor-payment-settings.js:7779
msgid "Visualize Your Course"
msgstr ""

#: assets/js/tutor-course-builder.js:7073
#: assets/js/tutor-payment-settings.js:7780
msgid "Describe the image you want for your course thumbnail"
msgstr ""

#: assets/js/tutor-course-builder.js:7098
#: assets/js/tutor-payment-settings.js:7805
msgid "Styles"
msgstr ""

#: assets/js/tutor-course-builder.js:7326
msgid "Schedule date is required."
msgstr ""

#: assets/js/tutor-course-builder.js:7331
msgid "Schedule date should be in the future."
msgstr ""

#: assets/js/tutor-course-builder.js:7337
msgid "Schedule date should be before enrollment start date."
msgstr ""

#: assets/js/tutor-course-builder.js:7347
msgid "Select date"
msgstr ""

#: assets/js/tutor-course-builder.js:7359
msgid "Schedule time is required."
msgstr ""

#: assets/js/tutor-course-builder.js:7364
msgid "Schedule time should be in the future."
msgstr ""

#: assets/js/tutor-course-builder.js:7370
msgid "Schedule time should be before enrollment start date."
msgstr ""

#: assets/js/tutor-course-builder.js:7389
msgid "Show coming soon in course list & details page"
msgstr ""

#: assets/js/tutor-course-builder.js:7410
msgid "Coming Soon Thumbnail"
msgstr ""

#. translators: %s is the maximum allowed upload file size (e.g., "2MB")
#: assets/js/tutor-course-builder.js:7413
#: assets/js/tutor-course-builder.js:11232
#: assets/js/tutor-course-builder.js:19762
msgid "JPEG, PNG, GIF, and WebP formats, up to %s"
msgstr ""

#: assets/js/tutor-course-builder.js:7421
msgid "Preview Course Curriculum"
msgstr ""

#: assets/js/tutor-course-builder.js:7442
msgid "Scheduled for"
msgstr ""

#: assets/js/tutor-course-builder.js:7442
msgid "Scheduled with coming soon"
msgstr ""

#. translators: %1$s is the date and %2$s is the time
#: assets/js/tutor-course-builder.js:7469
msgid "%1$s at %2$s"
msgstr ""

#: assets/js/tutor-course-builder.js:7676
msgid "Toggle options"
msgstr ""

#: assets/js/tutor-course-builder.js:7742
msgid "Clear selection"
msgstr ""

#: assets/js/tutor-course-builder.js:8006
msgid "Category name is required"
msgstr ""

#: assets/js/tutor-course-builder.js:8010
msgid "Category name"
msgstr ""

#: assets/js/tutor-course-builder.js:8019
msgid "Select parent"
msgstr ""

#: assets/js/tutor-course-builder.js:8154
msgid "Click to select user"
msgstr ""

#: assets/js/tutor-course-builder.js:8178
msgid "No user selected"
msgstr ""

#: assets/js/tutor-course-builder.js:8433
msgid "No user found"
msgstr ""

#: assets/js/tutor-course-builder.js:8829
msgid "No tag created yet."
msgstr ""

#: assets/js/tutor-course-builder.js:8983
msgid "Boost Revenue with Subscriptions"
msgstr ""

#: assets/js/tutor-course-builder.js:8985
msgid "Offer flexible subscription plans to maximize your earnings and provide students with affordable access to your courses."
msgstr ""

#: assets/js/tutor-course-builder.js:8995
#: assets/js/tutor-course-builder.js:10738
msgid "Add Subscription"
msgstr ""

#: assets/js/tutor-course-builder.js:9244
msgid "Offer sale price"
msgstr ""

#: assets/js/tutor-course-builder.js:9257
msgid "Sale price should be less than regular price"
msgstr ""

#: assets/js/tutor-course-builder.js:9260
msgid "Sale price should be greater than 0"
msgstr ""

#: assets/js/tutor-course-builder.js:9268
#: assets/js/tutor-course-builder.js:11043
msgid "Sale Price"
msgstr ""

#: assets/js/tutor-course-builder.js:9279
msgid "Schedule the sale price"
msgstr ""

#: assets/js/tutor-course-builder.js:9286
msgid "Sale starts from"
msgstr ""

#: assets/js/tutor-course-builder.js:9292
#: assets/js/tutor-course-builder.js:9322
msgid "Schedule date is required"
msgstr ""

#: assets/js/tutor-course-builder.js:9305
#: assets/js/tutor-course-builder.js:9346
msgid "Schedule time is required"
msgstr ""

#: assets/js/tutor-course-builder.js:9316
msgid "Sale ends to"
msgstr ""

#: assets/js/tutor-course-builder.js:9328
msgid "Sales End date should be greater than start date"
msgstr ""

#: assets/js/tutor-course-builder.js:9354
msgid "Sales End time should be greater than start time"
msgstr ""

#. translators: %s is the number of times
#: assets/js/tutor-course-builder.js:9865
msgid "%s times"
msgstr ""

#: assets/js/tutor-course-builder.js:9912
msgid "Featured"
msgstr ""

#: assets/js/tutor-course-builder.js:9978
msgid "Collapse/expand plan"
msgstr ""

#: assets/js/tutor-course-builder.js:9997
msgid "Enter plan name"
msgstr ""

#: assets/js/tutor-course-builder.js:9998
msgid "Plan Name"
msgstr ""

#: assets/js/tutor-course-builder.js:10009
#: assets/js/tutor-course-builder.js:11010
msgid "Price must be greater than 0"
msgstr ""

#: assets/js/tutor-course-builder.js:10017
msgid "Plan price"
msgstr ""

#: assets/js/tutor-course-builder.js:10029
msgid "This value must be equal to or greater than 1"
msgstr ""

#: assets/js/tutor-course-builder.js:10035
msgid "Billing Interval"
msgstr ""

#: assets/js/tutor-course-builder.js:10036
msgid "12"
msgstr ""

#: assets/js/tutor-course-builder.js:10048
msgid "Day(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:10051
msgid "Week(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:10054
msgid "Month(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:10057
msgid "Year(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:10072
msgid "Renew plan must be greater than 0"
msgstr ""

#: assets/js/tutor-course-builder.js:10079
#: assets/js/tutor-course-builder.js:10625
msgid "Billing Cycles"
msgstr ""

#: assets/js/tutor-course-builder.js:10080
msgid "Select or type times to renewing the plan"
msgstr ""

#: assets/js/tutor-course-builder.js:10081
msgid "Times"
msgstr ""

#: assets/js/tutor-course-builder.js:10093
msgid "Charge enrollment fee"
msgstr ""

#: assets/js/tutor-course-builder.js:10104
msgid "Enrollment fee must be greater than 0"
msgstr ""

#: assets/js/tutor-course-builder.js:10111
msgid "Enrollment fee"
msgstr ""

#: assets/js/tutor-course-builder.js:10113
msgid "Enter enrollment fee"
msgstr ""

#: assets/js/tutor-course-builder.js:10124
msgid "Do not provide certificate"
msgstr ""

#: assets/js/tutor-course-builder.js:10132
msgid "Mark as featured"
msgstr ""

#. translators: %s is the title of the item to be deleted
#: assets/js/tutor-course-builder.js:10144
#: assets/js/tutor-course-builder.js:26521
#: assets/js/tutor-course-builder.js:28770
#: assets/js/tutor-course-builder.js:28996
msgid "Delete \"%s\""
msgstr ""

#: assets/js/tutor-course-builder.js:10145
msgid "Are you sure you want to delete this plan? This cannot be undone."
msgstr ""

#: assets/js/tutor-course-builder.js:10420
#: assets/js/tutor-course-builder.js:18319
#: assets/js/tutor-course-builder.js:19650
#: assets/js/tutor-course-builder.js:24985
msgid "Unsaved Changes"
msgstr ""

#: assets/js/tutor-course-builder.js:10431
#: assets/js/tutor-course-builder.js:18334
#: assets/js/tutor-course-builder.js:19665
#: assets/js/tutor-course-builder.js:25023
msgid "Discard Changes"
msgstr ""

#: assets/js/tutor-course-builder.js:10460
msgid "Subscription Plans"
msgstr ""

#: assets/js/tutor-course-builder.js:10556
msgid "Add New Plan"
msgstr ""

#: assets/js/tutor-course-builder.js:10588
msgid "Months"
msgstr ""

#: assets/js/tutor-course-builder.js:10588
msgid "Month"
msgstr ""

#: assets/js/tutor-course-builder.js:10590
msgid "Years"
msgstr ""

#: assets/js/tutor-course-builder.js:10590
msgid "Year"
msgstr ""

#: assets/js/tutor-course-builder.js:10592
#: assets/js/tutor-course-builder.js:10626
msgid "Until Cancellation"
msgstr ""

#: assets/js/tutor-course-builder.js:10619
msgid "Lifetime"
msgstr ""

#. translators: %1$s is the number and the %2$s is the repeat unit (e.g., day, week, month)
#: assets/js/tutor-course-builder.js:10621
msgid "Renew every %1$s %2$s"
msgstr ""

#: assets/js/tutor-course-builder.js:10633
#: assets/js/tutor-course-builder.js:10726
msgid "Manage Subscription Plans"
msgstr ""

#: assets/js/tutor-course-builder.js:10697
msgid "Subscriptions"
msgstr ""

#: assets/js/tutor-course-builder.js:10832
msgid "One-time purchase only"
msgstr ""

#: assets/js/tutor-course-builder.js:10835
msgid "Subscription only"
msgstr ""

#: assets/js/tutor-course-builder.js:10838
msgid "Subscription & one-time purchase"
msgstr ""

#: assets/js/tutor-course-builder.js:10841
msgid "Membership only"
msgstr ""

#: assets/js/tutor-course-builder.js:10944
msgid "Pricing Model"
msgstr ""

#: assets/js/tutor-course-builder.js:10956
msgid "Purchase Options"
msgstr ""

#: assets/js/tutor-course-builder.js:10967
#: assets/js/tutor-course-builder.js:10987
msgid "Select product"
msgstr ""

#: assets/js/tutor-course-builder.js:10968
#: assets/js/tutor-course-builder.js:10970
#: assets/js/tutor-course-builder.js:10988
msgid "Select a product"
msgstr ""

#: assets/js/tutor-course-builder.js:10973
msgid "You can select an existing WooCommerce product, alternatively, a new WooCommerce product will be created for you."
msgstr ""

#: assets/js/tutor-course-builder.js:10973
msgid "You can select an existing WooCommerce product."
msgstr ""

#: assets/js/tutor-course-builder.js:10995
msgid "Sell your product, process by EDD"
msgstr ""

#: assets/js/tutor-course-builder.js:11017
msgid "Regular Price"
msgstr ""

#: assets/js/tutor-course-builder.js:11036
msgid "Sale price must be less than regular price"
msgstr ""

#: assets/js/tutor-course-builder.js:11183
msgid "Visibility"
msgstr ""

#: assets/js/tutor-course-builder.js:11184
msgid "Select visibility status"
msgstr ""

#. translators: %s is the last updated date
#: assets/js/tutor-course-builder.js:11203
msgid "Last updated on %s"
msgstr ""

#: assets/js/tutor-course-builder.js:11210
#: assets/js/tutor-course-builder.js:13724
msgid "Password is required"
msgstr ""

#: assets/js/tutor-course-builder.js:11215
msgid "Enter password"
msgstr ""

#: assets/js/tutor-course-builder.js:11228
#: assets/js/tutor-course-builder.js:19759
msgid "Featured Image"
msgstr ""

#: assets/js/tutor-course-builder.js:11243
msgid "Intro Video"
msgstr ""

#: assets/js/tutor-course-builder.js:11244
#: assets/js/tutor-course-builder.js:19772
msgid "Upload Video"
msgstr ""

#. translators: %s is the maximum allowed file size
#: assets/js/tutor-course-builder.js:11247
#: assets/js/tutor-course-builder.js:19775
msgid "MP4, and WebM formats, up to %s"
msgstr ""

#: assets/js/tutor-course-builder.js:11272
msgid "Add tags"
msgstr ""

#: assets/js/tutor-course-builder.js:11291
msgid "Search to add author"
msgstr ""

#: assets/js/tutor-course-builder.js:11311
msgid "Search to add instructor"
msgstr ""

#: assets/js/tutor-course-builder.js:11315
msgid "No instructors added."
msgstr ""

#: assets/js/tutor-course-builder.js:11469
msgid "No option available."
msgstr ""

#: assets/js/tutor-course-builder.js:11537
msgid "Schedule course content by date"
msgstr ""

#: assets/js/tutor-course-builder.js:11540
msgid "Content available after X days from enrollment"
msgstr ""

#: assets/js/tutor-course-builder.js:11543
msgid "Course content available sequentially"
msgstr ""

#: assets/js/tutor-course-builder.js:11546
msgid "Course content unlocked after finishing prerequisites"
msgstr ""

#: assets/js/tutor-course-builder.js:11562
msgid "Content Drip is a pro feature"
msgstr ""

#: assets/js/tutor-course-builder.js:11564
msgid "You can schedule your course content using  content drip options"
msgstr ""

#: assets/js/tutor-course-builder.js:11585
msgid "Activate the “Content Drip” addon to use this feature."
msgstr ""

#: assets/js/tutor-course-builder.js:11587
msgid "Control when students can access lessons and quizzes using the Content Drip feature."
msgstr ""

#: assets/js/tutor-course-builder.js:11597
msgid "Enable Content Drip Addon"
msgstr ""

#: assets/js/tutor-course-builder.js:11603
msgid "Content Drip Type"
msgstr ""

#: assets/js/tutor-course-builder.js:11605
msgid "You can schedule your course content using one of the following Content Drip options"
msgstr ""

#: assets/js/tutor-course-builder.js:11717
msgid "Maximum Student"
msgstr ""

#: assets/js/tutor-course-builder.js:11718
msgid "Number of students that can enrol in this course. Set 0 for no limits."
msgstr ""

#: assets/js/tutor-course-builder.js:11735
msgid "Enrollment Expiration"
msgstr ""

#: assets/js/tutor-course-builder.js:11738
msgid "Student's enrollment will be removed after this number of days. Set 0 for lifetime enrollment."
msgstr ""

#: assets/js/tutor-course-builder.js:11758
msgid "Course Enrollment Period"
msgstr ""

#: assets/js/tutor-course-builder.js:11775
#: assets/js/tutor-course-builder.js:11795
msgid "Start Date"
msgstr ""

#: assets/js/tutor-course-builder.js:11786
msgid "Start date should be after the schedule date"
msgstr ""

#: assets/js/tutor-course-builder.js:11807
msgid "Start time should be after the schedule time"
msgstr ""

#: assets/js/tutor-course-builder.js:11816
#: assets/js/tutor-course-builder.js:11884
msgid "hh:mm a"
msgstr ""

#: assets/js/tutor-course-builder.js:11828
msgid "Add End Date"
msgstr ""

#: assets/js/tutor-course-builder.js:11833
#: assets/js/tutor-course-builder.js:11862
msgid "End Date"
msgstr ""

#: assets/js/tutor-course-builder.js:11853
msgid "End date should be after the start date"
msgstr ""

#: assets/js/tutor-course-builder.js:11875
msgid "End time should be after the start time"
msgstr ""

#: assets/js/tutor-course-builder.js:11892
msgid "Pause Enrollment"
msgstr ""

#: assets/js/tutor-course-builder.js:11895
msgid "If you pause enrolment, students will no longer be able to enroll in the course."
msgstr ""

#: assets/js/tutor-course-builder.js:12046
msgid "Select Difficulty Level"
msgstr ""

#: assets/js/tutor-course-builder.js:12060
msgid "Public Course"
msgstr ""

#: assets/js/tutor-course-builder.js:12061
msgid "Make This Course Public. No Enrollment Required."
msgstr ""

#: assets/js/tutor-course-builder.js:12073
msgid "Enable Q&A section for your course"
msgstr ""

#: assets/js/tutor-course-builder.js:12084
msgid "Enable BuddyPress group activity feeds"
msgstr ""

#: assets/js/tutor-course-builder.js:12092
msgid "BuddyPress Groups"
msgstr ""

#: assets/js/tutor-course-builder.js:12093
msgid "Assign this course to BuddyPress Groups"
msgstr ""

#: assets/js/tutor-course-builder.js:12094
msgid "Search BuddyPress Groups"
msgstr ""

#: assets/js/tutor-course-builder.js:12333
msgid "ex. Learn Photoshop CS6 from scratch"
msgstr ""

#: assets/js/tutor-course-builder.js:12353
msgid "Course URL"
msgstr ""

#: assets/js/tutor-course-builder.js:13628
msgid "Date is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13632
msgid "Meeting Date"
msgstr ""

#: assets/js/tutor-course-builder.js:13633
msgid "Enter meeting date"
msgstr ""

#: assets/js/tutor-course-builder.js:13641
msgid "Time is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13655
msgid "Duration is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13659
msgid "Meeting Duration"
msgstr ""

#: assets/js/tutor-course-builder.js:13669
msgid "Duration unit is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13702
msgid "Auto recording is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13706
msgid "Auto Recording"
msgstr ""

#: assets/js/tutor-course-builder.js:13707
msgid "Select auto recording option"
msgstr ""

#: assets/js/tutor-course-builder.js:13709
msgid "No recordings"
msgstr ""

#: assets/js/tutor-course-builder.js:13712
msgid "Local"
msgstr ""

#: assets/js/tutor-course-builder.js:13715
msgid "Cloud"
msgstr ""

#: assets/js/tutor-course-builder.js:13728
msgid "Meeting Password"
msgstr ""

#: assets/js/tutor-course-builder.js:13729
msgid "Enter meeting password"
msgstr ""

#: assets/js/tutor-course-builder.js:13738
msgid "Meeting host is required"
msgstr ""

#: assets/js/tutor-course-builder.js:13742
msgid "Meeting Host"
msgstr ""

#: assets/js/tutor-course-builder.js:13743
msgid "Enter meeting host"
msgstr ""

#: assets/js/tutor-course-builder.js:14010
#: assets/js/tutor-payment-settings.js:8407
msgid "Revert to Original"
msgstr ""

#: assets/js/tutor-course-builder.js:14097
#: assets/js/tutor-payment-settings.js:8494
msgid "Describe the Fill"
msgstr ""

#: assets/js/tutor-course-builder.js:14098
#: assets/js/tutor-payment-settings.js:8495
msgid "Write 5 words to describe..."
msgstr ""

#: assets/js/tutor-course-builder.js:14114
#: assets/js/tutor-payment-settings.js:8511
msgid "Generative Erase"
msgstr ""

#: assets/js/tutor-course-builder.js:14146
#: assets/js/tutor-payment-settings.js:8543
msgid "Use Image"
msgstr ""

#: assets/js/tutor-course-builder.js:17502
#: assets/js/tutor-import-export.js:8594
msgid "Invalid file type"
msgstr ""

#: assets/js/tutor-course-builder.js:17504
#: assets/js/tutor-import-export.js:8596
msgid "Maximum upload size exceeded"
msgstr ""

#: assets/js/tutor-course-builder.js:17980
#: assets/js/tutor-course-builder.js:27741
msgid "Illustration of a no course selected"
msgstr ""

#: assets/js/tutor-course-builder.js:17981
msgid "No topic content selected"
msgstr ""

#: assets/js/tutor-course-builder.js:17982
msgid "Select a topic content to add as a prerequisite"
msgstr ""

#. translators: %d is the number of questions
#: assets/js/tutor-course-builder.js:18013
#: assets/js/tutor-course-builder.js:18090
msgid "(%d questions)"
msgstr ""

#: assets/js/tutor-course-builder.js:18044
msgid "No topics content found"
msgstr ""

#: assets/js/tutor-course-builder.js:18352
msgid "Assignment title is required"
msgstr ""

#: assets/js/tutor-course-builder.js:18357
msgid "Enter Assignment Title"
msgstr ""

#: assets/js/tutor-course-builder.js:18367
#: assets/js/tutor-course-builder.js:19706
msgid "Content"
msgstr ""

#: assets/js/tutor-course-builder.js:18368
msgid "Enter Assignment Content"
msgstr ""

#: assets/js/tutor-course-builder.js:18383
#: assets/js/tutor-course-builder.js:19916
#: assets/js/tutor-course-builder.js:29409
msgid "Upload Attachment"
msgstr ""

#: assets/js/tutor-course-builder.js:18403
#: assets/js/tutor-course-builder.js:19853
#: assets/js/tutor-course-builder.js:24609
msgid "Available after days"
msgstr ""

#: assets/js/tutor-course-builder.js:18404
msgid "This assignment will be available after the given number of days."
msgstr ""

#: assets/js/tutor-course-builder.js:18422
#: assets/js/tutor-course-builder.js:19872
#: assets/js/tutor-course-builder.js:24628
msgid "Unlock Date"
msgstr ""

#: assets/js/tutor-course-builder.js:18423
#: assets/js/tutor-course-builder.js:19873
#: assets/js/tutor-course-builder.js:24629
msgid "Select Unlock Date"
msgstr ""

#: assets/js/tutor-course-builder.js:18426
msgid "This assignment will be available from the given date. Leave empty to make it available immediately."
msgstr ""

#: assets/js/tutor-course-builder.js:18442
#: assets/js/tutor-course-builder.js:19892
#: assets/js/tutor-course-builder.js:24648
msgid "Prerequisites"
msgstr ""

#: assets/js/tutor-course-builder.js:18443
#: assets/js/tutor-course-builder.js:19893
#: assets/js/tutor-course-builder.js:24649
msgid "Select Prerequisite"
msgstr ""

#: assets/js/tutor-course-builder.js:18457
#: assets/js/tutor-course-builder.js:19907
#: assets/js/tutor-course-builder.js:24659
msgid "Select items that should be complete before this item"
msgstr ""

#: assets/js/tutor-course-builder.js:18468
#: assets/js/tutor-course-builder.js:24461
msgid "Time Limit"
msgstr ""

#: assets/js/tutor-course-builder.js:18489
msgid "Set Deadline From Assignment Start Time"
msgstr ""

#: assets/js/tutor-course-builder.js:18490
msgid "Each student will get their own deadline based on when they start the assignment."
msgstr ""

#: assets/js/tutor-course-builder.js:18510
msgid "Pass mark cannot be greater than total mark"
msgstr ""

#: assets/js/tutor-course-builder.js:18518
msgid "Minimum Pass Points"
msgstr ""

#: assets/js/tutor-course-builder.js:18530
msgid "File Upload Limit"
msgstr ""

#: assets/js/tutor-course-builder.js:18533
msgid "Define the number of files that a student can upload in this assignment. Input 0 to disable the option to upload."
msgstr ""

#: assets/js/tutor-course-builder.js:18543
msgid "Maximum File Size Limit"
msgstr ""

#: assets/js/tutor-course-builder.js:19050
msgid "Quiz imported successfully"
msgstr ""

#: assets/js/tutor-course-builder.js:19081
msgid "Something went wrong."
msgstr ""

#: assets/js/tutor-course-builder.js:19348
msgid "Content Type"
msgstr ""

#: assets/js/tutor-course-builder.js:19357
msgid "Created At"
msgstr ""

#. translators: %s is the number of selected items
#: assets/js/tutor-course-builder.js:19372
#: assets/js/tutor-import-export.js:6700
msgid "%s selected"
msgstr ""

#: assets/js/tutor-course-builder.js:19386
msgid "Search by title"
msgstr ""

#: assets/js/tutor-course-builder.js:19632
#: assets/js/tutor-course-builder.js:24132
msgid "Select H5P Content"
msgstr ""

#: assets/js/tutor-course-builder.js:19687
msgid "Lesson title is required."
msgstr ""

#: assets/js/tutor-course-builder.js:19692
msgid "Enter Lesson Name"
msgstr ""

#: assets/js/tutor-course-builder.js:19709
msgid "Save the lesson first to use the WP Editor."
msgstr ""

#: assets/js/tutor-course-builder.js:19725
msgid "WP Editor"
msgstr ""

#: assets/js/tutor-course-builder.js:19726
msgid "Enter Lesson Description"
msgstr ""

#: assets/js/tutor-course-builder.js:19743
#: assets/js/tutor-course-builder.js:19749
msgid "Add H5P Content"
msgstr ""

#: assets/js/tutor-course-builder.js:19790
msgid "Video Playback Time"
msgstr ""

#: assets/js/tutor-course-builder.js:19815
msgid "min"
msgstr ""

#: assets/js/tutor-course-builder.js:19830
msgid "sec"
msgstr ""

#: assets/js/tutor-course-builder.js:19854
msgid "This lesson will be available after the given number of days."
msgstr ""

#: assets/js/tutor-course-builder.js:19876
msgid "This lesson will be available from the given date. Leave empty to make it available immediately."
msgstr ""

#: assets/js/tutor-course-builder.js:19933
msgid "Lesson Preview"
msgstr ""

#: assets/js/tutor-course-builder.js:19939
msgid "If checked, any user/guest can view this lesson without enrolling in the course."
msgstr ""

#: assets/js/tutor-course-builder.js:19949
msgid "This lesson is now available for preview. Users and guests can view it without enrolling in the course."
msgstr ""

#: assets/js/tutor-course-builder.js:20104
#: assets/js/tutor-course-builder.js:23914
msgid "Open Ended/Essay"
msgstr ""

#: assets/js/tutor-course-builder.js:20108
#: assets/js/tutor-course-builder.js:23919
msgid "Fill in the Blanks"
msgstr ""

#: assets/js/tutor-course-builder.js:20149
msgid "Create/Select a question to view details"
msgstr ""

#: assets/js/tutor-course-builder.js:20157
msgid "Question Type"
msgstr ""

#: assets/js/tutor-course-builder.js:20165
msgid "Conditions:"
msgstr ""

#: assets/js/tutor-course-builder.js:20174
msgid "Multiple Correct Answer"
msgstr ""

#: assets/js/tutor-course-builder.js:20214
msgid "Answer Required"
msgstr ""

#: assets/js/tutor-course-builder.js:20229
msgid "Randomize Choice"
msgstr ""

#: assets/js/tutor-course-builder.js:20245
msgid "Point For This Question"
msgstr ""

#: assets/js/tutor-course-builder.js:20262
msgid "Display Points"
msgstr ""

#: assets/js/tutor-course-builder.js:20582
msgid "Edit description"
msgstr ""

#: assets/js/tutor-course-builder.js:20582
msgid "Add description"
msgstr ""

#: assets/js/tutor-course-builder.js:20800
msgid "Edit question title"
msgstr ""

#: assets/js/tutor-course-builder.js:20814
msgid "Save question title"
msgstr ""

#: assets/js/tutor-course-builder.js:20824
msgid "Cancel question title"
msgstr ""

#: assets/js/tutor-course-builder.js:20940
msgid "Fill in the blanks"
msgstr ""

#: assets/js/tutor-course-builder.js:20969
#: assets/js/tutor-course-builder.js:20989
msgid "Question title..."
msgstr ""

#: assets/js/tutor-course-builder.js:20981
msgid "Correct Answer(s)..."
msgstr ""

#: assets/js/tutor-course-builder.js:21031
msgid "Please make sure to use the variable {dash} in your question title to show the blanks in your question. You can use multiple {dash} variables in one question."
msgstr ""

#: assets/js/tutor-course-builder.js:21080
msgid "Match the number of answers to the number of blanks {dash} in your question."
msgstr ""

#: assets/js/tutor-course-builder.js:21088
msgid "Separate multiple answers by a vertical bar |. 1 answer per {dash} variable is defined in the question. Example: Apple | Banana | Orange"
msgstr ""

#: assets/js/tutor-course-builder.js:21455
#: assets/js/tutor-course-builder.js:22718
msgid "Write answer option..."
msgstr ""

#: assets/js/tutor-course-builder.js:21465
#: assets/js/tutor-course-builder.js:22070
msgid "Standard Size: 700x430 pixels"
msgstr ""

#: assets/js/tutor-course-builder.js:21475
msgid "Input answer here"
msgstr ""

#: assets/js/tutor-course-builder.js:21519
msgid "Students need to type their answers exactly as you write them here. Use "
msgstr ""

#: assets/js/tutor-course-builder.js:21523
msgid "small caps"
msgstr ""

#: assets/js/tutor-course-builder.js:21523
msgid " when writing the answer."
msgstr ""

#: assets/js/tutor-course-builder.js:21791
#: assets/js/tutor-course-builder.js:22404
#: assets/js/tutor-course-builder.js:23097
msgid "Add Option"
msgstr ""

#: assets/js/tutor-course-builder.js:22040
msgid "Answer title..."
msgstr ""

#: assets/js/tutor-course-builder.js:22078
msgid "Image matched text.."
msgstr ""

#: assets/js/tutor-course-builder.js:22116
msgid "Matched option.."
msgstr ""

#: assets/js/tutor-course-builder.js:22620
msgid "Remove Image"
msgstr ""

#: assets/js/tutor-course-builder.js:22632
msgid "Add Image"
msgstr ""

#: assets/js/tutor-course-builder.js:22730
msgid "Size: 700x430 pixels"
msgstr ""

#: assets/js/tutor-course-builder.js:22737
msgid "Write option..."
msgstr ""

#: assets/js/tutor-course-builder.js:23122
msgid "No options are necessary for this question type"
msgstr ""

#: assets/js/tutor-course-builder.js:23526
msgid "Enter a quiz title to begin. Choose from a variety of question types to keep things interesting!"
msgstr ""

#: assets/js/tutor-course-builder.js:23540
msgid "Question title is required"
msgstr ""

#: assets/js/tutor-course-builder.js:23546
msgid "Write your question here.."
msgstr ""

#: assets/js/tutor-course-builder.js:23566
msgid "Description (optional)"
msgstr ""

#: assets/js/tutor-course-builder.js:23597
msgid "Answer Explanation"
msgstr ""

#: assets/js/tutor-course-builder.js:23598
msgid "Write answer explanation..."
msgstr ""

#. translators: %d is the question number
#: assets/js/tutor-course-builder.js:24003
msgid "Question %d"
msgstr ""

#: assets/js/tutor-course-builder.js:24157
msgid "No questions added yet."
msgstr ""

#: assets/js/tutor-course-builder.js:24217
msgid "Select Question Type"
msgstr ""

#: assets/js/tutor-course-builder.js:24446
msgid "Basic Settings"
msgstr ""

#: assets/js/tutor-course-builder.js:24462
msgid "Set a time limit for this quiz. A value of “0” indicates no time limit"
msgstr ""

#: assets/js/tutor-course-builder.js:24497
msgid "Hide Quiz Time"
msgstr ""

#: assets/js/tutor-course-builder.js:24505
msgid "Feedback Mode"
msgstr ""

#: assets/js/tutor-course-builder.js:24514
msgid "Answers are shown after finishing the quiz."
msgstr ""

#: assets/js/tutor-course-builder.js:24518
msgid "Show answer after attempting the question."
msgstr ""

#: assets/js/tutor-course-builder.js:24520
msgid "Retry"
msgstr ""

#: assets/js/tutor-course-builder.js:24522
msgid "Allows students to retake the quiz after their first attempt."
msgstr ""

#: assets/js/tutor-course-builder.js:24536
msgid "Allowed attempts must be between 0 and 20"
msgstr ""

#: assets/js/tutor-course-builder.js:24545
msgid "Define how many times a student can retake this quiz. Setting it to \"0\" allows unlimited attempts"
msgstr ""

#: assets/js/tutor-course-builder.js:24557
msgid "Passing is Required"
msgstr ""

#: assets/js/tutor-course-builder.js:24560
msgid "By enabling this option, the student must have to pass it to access the next quiz"
msgstr ""

#: assets/js/tutor-course-builder.js:24571
msgid "Set the minimum score percentage required to pass this quiz"
msgstr ""

#: assets/js/tutor-course-builder.js:24586
msgid "Max Question Allowed to Answer"
msgstr ""

#: assets/js/tutor-course-builder.js:24589
msgid "Set the number of quiz questions randomly from your question pool. If the set number exceeds available questions, all questions will be included"
msgstr ""

#: assets/js/tutor-course-builder.js:24610
msgid "This quiz will be available after the given number of days."
msgstr ""

#: assets/js/tutor-course-builder.js:24632
msgid "This quiz will be available from the given date. Leave empty to make it available immediately."
msgstr ""

#: assets/js/tutor-course-builder.js:24672
msgid "Quiz Auto Start"
msgstr ""

#: assets/js/tutor-course-builder.js:24673
msgid "When enabled, the quiz begins immediately as soon as the page loads"
msgstr ""

#: assets/js/tutor-course-builder.js:24685
msgid "Question Layout"
msgstr ""

#: assets/js/tutor-course-builder.js:24686
#: assets/js/tutor-course-builder.js:24705
msgid "Select an option"
msgstr ""

#: assets/js/tutor-course-builder.js:24688
msgid "Single question"
msgstr ""

#: assets/js/tutor-course-builder.js:24691
msgid "Question pagination"
msgstr ""

#: assets/js/tutor-course-builder.js:24704
msgid "Question Order"
msgstr ""

#: assets/js/tutor-course-builder.js:24728
msgid "Hide Question Number"
msgstr ""

#: assets/js/tutor-course-builder.js:24737
msgid "Set Character Limit for Short Answers"
msgstr ""

#: assets/js/tutor-course-builder.js:24747
msgid "Set Character Limit for Open-Ended/Essay Answers"
msgstr ""

#: assets/js/tutor-course-builder.js:24914
msgid "Please add a question"
msgstr ""

#: assets/js/tutor-course-builder.js:24992
msgid "Question Details"
msgstr ""

#: assets/js/tutor-course-builder.js:25023
msgid "Discard"
msgstr ""

#: assets/js/tutor-course-builder.js:25064
msgid "Quiz title is required"
msgstr ""

#: assets/js/tutor-course-builder.js:25068
msgid "Add quiz title"
msgstr ""

#: assets/js/tutor-course-builder.js:25083
#: assets/js/tutor-course-builder.js:25980
msgid "Add a summary"
msgstr ""

#: assets/js/tutor-course-builder.js:25135
msgid "Your quiz has unsaved changes. If you cancel, you will lose your progress."
msgstr ""

#: assets/js/tutor-course-builder.js:25136
msgid "Are you sure you want to continue?"
msgstr ""

#. translators: %s is the topic title
#: assets/js/tutor-course-builder.js:25328
#: assets/js/tutor-course-builder.js:25357
#: assets/js/tutor-course-builder.js:25391
#: assets/js/tutor-course-builder.js:25438
#: assets/js/tutor-course-builder.js:26266
msgid "Topic: %s"
msgstr ""

#: assets/js/tutor-course-builder.js:25384
#: assets/js/tutor-course-builder.js:25397
#: assets/js/tutor-course-builder.js:25409
#: assets/js/tutor-course-builder.js:26187
msgid "Interactive Quiz"
msgstr ""

#: assets/js/tutor-course-builder.js:25471
#: assets/js/tutor-course-builder.js:25489
#: assets/js/tutor-course-builder.js:25555
msgid "Import Quiz"
msgstr ""

#: assets/js/tutor-course-builder.js:25512
msgid "Meet live lesson"
msgstr ""

#: assets/js/tutor-course-builder.js:25534
msgid "Zoom live lesson"
msgstr ""

#: assets/js/tutor-course-builder.js:25858
msgid "Title is required"
msgstr ""

#: assets/js/tutor-course-builder.js:25862
msgid "Add a title"
msgstr ""

#. translators: %s is the topic title
#: assets/js/tutor-course-builder.js:26013
msgid "Delete topic \"%s\""
msgstr ""

#: assets/js/tutor-course-builder.js:26014
#: assets/js/tutor-course-builder.js:26522
msgid "Are you sure you want to delete this content from your course? This cannot be undone."
msgstr ""

#: assets/js/tutor-course-builder.js:26169
msgid "Are you sure you want to delete this assignment? All existing assignment submissions will be permanently deleted."
msgstr ""

#: assets/js/tutor-course-builder.js:26172
msgid "Are you sure you want to delete this quiz? All existing quiz attempts will be permanently deleted."
msgstr ""

#: assets/js/tutor-course-builder.js:26175
msgid "Are you sure you want to delete this interactive quiz? All existing quiz attempts will be permanently deleted."
msgstr ""

#. translators: %s is the total number of questions
#: assets/js/tutor-course-builder.js:26391
msgid "%s Questions"
msgstr ""

#: assets/js/tutor-course-builder.js:26397
msgid "Export Quiz"
msgstr ""

#: assets/js/tutor-course-builder.js:27130
#: assets/js/tutor-course-builder.js:37576
msgid "Curriculum"
msgstr ""

#: assets/js/tutor-course-builder.js:27144
msgid "Expand All"
msgstr ""

#: assets/js/tutor-course-builder.js:27144
msgid "Collapse All"
msgstr ""

#: assets/js/tutor-course-builder.js:27152
msgid "Empty State Illustration"
msgstr ""

#: assets/js/tutor-course-builder.js:27153
msgid "Start building your course!"
msgstr ""

#: assets/js/tutor-course-builder.js:27154
msgid "Add Topics, Lessons, and Quizzes to get started."
msgstr ""

#: assets/js/tutor-course-builder.js:27175
#: assets/js/tutor-course-builder.js:27277
msgid "Add Topic"
msgstr ""

#: assets/js/tutor-course-builder.js:27742
msgid "No course selected"
msgstr ""

#: assets/js/tutor-course-builder.js:27743
msgid "Select a course to add as a prerequisite."
msgstr ""

#: assets/js/tutor-course-builder.js:27798
msgid "No courses found"
msgstr ""

#: assets/js/tutor-course-builder.js:27985
msgid "Close"
msgstr ""

#: assets/js/tutor-course-builder.js:28004
msgid "Edit in Certificate Builder"
msgstr ""

#: assets/js/tutor-course-builder.js:28040
#: assets/js/tutor-import-export.js:8813
msgid "Selected"
msgstr ""

#: assets/js/tutor-course-builder.js:28161
#: assets/js/tutor-course-builder.js:30947
#: assets/js/tutor-course-builder.js:31068
msgid "Preview"
msgstr ""

#: assets/js/tutor-course-builder.js:28226
#: assets/js/tutor-course-builder.js:28559
msgid "Pro Placeholder"
msgstr ""

#: assets/js/tutor-course-builder.js:28231
msgid "Award Students with Custom Certificates"
msgstr ""

#: assets/js/tutor-course-builder.js:28235
msgid "Celebrate success with personalized certificates. Recognize student achievements with unique designs that inspire and motivate students."
msgstr ""

#: assets/js/tutor-course-builder.js:28246
msgid "Design personalized certificates that highlight their accomplishments and boost their confidence."
msgstr ""

#: assets/js/tutor-course-builder.js:28253
msgid "Inspire them with a touch of credibility and recognition tailored just for them."
msgstr ""

#: assets/js/tutor-course-builder.js:28404
msgid "Templates"
msgstr ""

#: assets/js/tutor-course-builder.js:28407
msgid "Custom Certificates"
msgstr ""

#: assets/js/tutor-course-builder.js:28407
msgid "Certificates"
msgstr ""

#: assets/js/tutor-course-builder.js:28485
msgid "Not Found"
msgstr ""

#: assets/js/tutor-course-builder.js:28490
msgid "You didn’t create any certificate yet!"
msgstr ""

#: assets/js/tutor-course-builder.js:28564
msgid "Guide Students with Course Prerequisites"
msgstr ""

#: assets/js/tutor-course-builder.js:28575
msgid "Easily set prerequisites to structure your courses and guide student progress."
msgstr ""

#: assets/js/tutor-course-builder.js:28582
msgid "Offer customized learning journeys by setting multiple prerequisites for any course."
msgstr ""

#: assets/js/tutor-course-builder.js:28724
#: assets/js/tutor-course-builder.js:28949
msgid "Start Meeting"
msgstr ""

#: assets/js/tutor-course-builder.js:28771
#: assets/js/tutor-course-builder.js:28997
msgid "Are you sure you want to delete this meeting? This cannot be undone."
msgstr ""

#: assets/js/tutor-course-builder.js:28932
msgid "Meeting Token"
msgstr ""

#: assets/js/tutor-course-builder.js:29116
msgid "Schedule Live Class"
msgstr ""

#: assets/js/tutor-course-builder.js:29125
msgid "Tutor LMS PRO"
msgstr ""

#: assets/js/tutor-course-builder.js:29126
msgid "Bring your courses to life and engage students with interactive live classes."
msgstr ""

#: assets/js/tutor-course-builder.js:29176
msgid "Create a Zoom Meeting"
msgstr ""

#: assets/js/tutor-course-builder.js:29210
msgid "Create a Google Meet Link"
msgstr ""

#: assets/js/tutor-course-builder.js:29368
msgid "Course Prerequisites"
msgstr ""

#: assets/js/tutor-course-builder.js:29379
msgid "Search courses for prerequisites"
msgstr ""

#: assets/js/tutor-course-builder.js:29402
msgid "Provide additional resources like downloadable files and reference materials."
msgstr ""

#: assets/js/tutor-course-builder.js:29427
#: assets/js/tutor-course-builder.js:37583
msgid "Additional"
msgstr ""

#: assets/js/tutor-course-builder.js:29439
msgid "Provide essential course information to attract and inform potential students"
msgstr ""

#: assets/js/tutor-course-builder.js:29446
msgid "What Will I Learn?"
msgstr ""

#: assets/js/tutor-course-builder.js:29447
msgid "Define the key takeaways from this course (list one benefit per line)"
msgstr ""

#: assets/js/tutor-course-builder.js:29462
msgid "Specify the target audience that will benefit the most from the course. (One Line Per target audience)"
msgstr ""

#: assets/js/tutor-course-builder.js:29479
msgid "Total Course Duration"
msgstr ""

#: assets/js/tutor-course-builder.js:29482
msgid "hour(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:29495
msgid "min(s)"
msgstr ""

#: assets/js/tutor-course-builder.js:29505
msgid "Materials Included"
msgstr ""

#: assets/js/tutor-course-builder.js:29508
msgid "A list of assets you will be providing for the students in this course (One Per Line)"
msgstr ""

#: assets/js/tutor-course-builder.js:29520
msgid "Requirements/Instructions"
msgstr ""

#: assets/js/tutor-course-builder.js:29523
msgid "Additional requirements or special instructions for the students (One Per Line)"
msgstr ""

#: assets/js/tutor-course-builder.js:29538
msgid "Certificate"
msgstr ""

#: assets/js/tutor-course-builder.js:29544
msgid "Select a certificate to award your learners."
msgstr ""

#. translators: %s is the file title
#: assets/js/tutor-course-builder.js:30279
#: assets/js/tutor-payment-settings.js:10770
msgid "%s size exceeds the maximum allowed size"
msgstr ""

#. translators: %d is the maximum number of files allowed
#: assets/js/tutor-course-builder.js:30300
#: assets/js/tutor-payment-settings.js:10791
msgid "Cannot select more than %d files"
msgstr ""

#: assets/js/tutor-course-builder.js:30872
#: assets/js/tutor-course-builder.js:30876
msgid "Course submitted for review"
msgstr ""

#: assets/js/tutor-course-builder.js:30873
msgid "Thank you for submitting your course. It will be reviewed by our team shortly."
msgstr ""

#: assets/js/tutor-course-builder.js:30889
msgid "Back to Courses"
msgstr ""

#: assets/js/tutor-course-builder.js:30959
msgid "Move to Trash"
msgstr ""

#: assets/js/tutor-course-builder.js:30993
msgid "Switch to Draft"
msgstr ""

#: assets/js/tutor-course-builder.js:31003
msgid "Legacy Mode"
msgstr ""

#: assets/js/tutor-course-builder.js:31016
msgid "Publish Immediately"
msgstr ""

#: assets/js/tutor-course-builder.js:31085
msgid "Save as Draft"
msgstr ""

#: assets/js/tutor-course-builder.js:31865
msgid "Enter a brief overview of your course topics and structure"
msgstr ""

#: assets/js/tutor-course-builder.js:32036
msgid "Contents"
msgstr ""

#: assets/js/tutor-course-builder.js:32417
msgid "Now generating course title..."
msgstr ""

#: assets/js/tutor-course-builder.js:32418
msgid "Course title generated."
msgstr ""

#: assets/js/tutor-course-builder.js:32419
msgid "Error generating course title."
msgstr ""

#: assets/js/tutor-course-builder.js:32424
msgid "Now generating course description..."
msgstr ""

#: assets/js/tutor-course-builder.js:32425
msgid "Course description generated."
msgstr ""

#: assets/js/tutor-course-builder.js:32426
msgid "Error generating course description."
msgstr ""

#: assets/js/tutor-course-builder.js:32431
msgid "Now generating topic names..."
msgstr ""

#: assets/js/tutor-course-builder.js:32432
msgid "Course topics generated."
msgstr ""

#: assets/js/tutor-course-builder.js:32433
msgid "Error generating topics."
msgstr ""

#: assets/js/tutor-course-builder.js:32438
msgid "Now generating course contents..."
msgstr ""

#: assets/js/tutor-course-builder.js:32439
msgid "Course contents generated."
msgstr ""

#: assets/js/tutor-course-builder.js:32440
msgid "Error generating course contents."
msgstr ""

#: assets/js/tutor-course-builder.js:32445
msgid "Now generating quiz questions..."
msgstr ""

#: assets/js/tutor-course-builder.js:32446
msgid "Quiz questions generated."
msgstr ""

#: assets/js/tutor-course-builder.js:32447
msgid "Error generating quiz questions."
msgstr ""

#: assets/js/tutor-course-builder.js:32556
msgid "Ai Studio Error"
msgstr ""

#: assets/js/tutor-course-builder.js:32559
msgid "An error occurred while generating course content. Please try again."
msgstr ""

#: assets/js/tutor-course-builder.js:32669
msgid "Generating course content"
msgstr ""

#: assets/js/tutor-course-builder.js:32669
msgid "Generation aborted"
msgstr ""

#: assets/js/tutor-course-builder.js:32669
msgid "Error generating course content"
msgstr ""

#: assets/js/tutor-course-builder.js:32669
msgid "Generated course content"
msgstr ""

#. translators: %d is the total number of topics
#: assets/js/tutor-course-builder.js:32705
msgid "%d Topics in total"
msgstr ""

#. translators: %d is the number of lessons
#: assets/js/tutor-course-builder.js:32719
msgid "%d Lessons"
msgstr ""

#: assets/js/tutor-course-builder.js:32725
msgid "Error generating lessons."
msgstr ""

#. translators: %d is the number of quizzes
#: assets/js/tutor-course-builder.js:32733
msgid "%d Quizzes"
msgstr ""

#: assets/js/tutor-course-builder.js:32739
msgid "Error generating quizzes."
msgstr ""

#. translators: %d is the number of assignments
#: assets/js/tutor-course-builder.js:32749
msgid "%d Assignments"
msgstr ""

#: assets/js/tutor-course-builder.js:32755
msgid "Error generating assignments."
msgstr ""

#: assets/js/tutor-course-builder.js:32795
msgid "Generate a New Course"
msgstr ""

#: assets/js/tutor-course-builder.js:32809
msgid "Regenerate Course"
msgstr ""

#: assets/js/tutor-course-builder.js:32828
msgid "Failed to generate course content. Try again."
msgstr ""

#: assets/js/tutor-course-builder.js:32854
msgid "Type your desired course topic. e.g. Learning piano, Cooking 101.."
msgstr ""

#: assets/js/tutor-course-builder.js:32885
msgid "Stop Generation"
msgstr ""

#: assets/js/tutor-course-builder.js:32896
msgid "Course content stored into a local file."
msgstr ""

#: assets/js/tutor-course-builder.js:32899
msgid "Append the Course"
msgstr ""

#: assets/js/tutor-course-builder.js:33126
msgid "Create with AI"
msgstr ""

#: assets/js/tutor-course-builder.js:33141
msgid "Do you want to exit without saving?"
msgstr ""

#: assets/js/tutor-course-builder.js:33142
msgid "You’re about to leave the course creation process without saving your changes."
msgstr ""

#: assets/js/tutor-course-builder.js:33143
msgid "Yes, exit without saving"
msgstr ""

#: assets/js/tutor-course-builder.js:33145
msgid "Continue editing"
msgstr ""

#: assets/js/tutor-course-builder.js:33188
msgid "Generate with AI"
msgstr ""

#: assets/js/tutor-course-builder.js:33196
msgid "Exit"
msgstr ""

#: assets/js/tutor-course-builder.js:33758
#: assets/js/tutor-course-builder.js:33771
msgid "Notebook"
msgstr ""

#: assets/js/tutor-course-builder.js:34644
#: assets/js/tutor-payment-settings.js:11059
msgid "Order By"
msgstr ""

#: assets/js/tutor-course-builder.js:37569
msgid "Basics"
msgstr ""

#: assets/js/tutor-course-builder.js:37978
#: assets/js/tutor-payment-settings.js:5795
#: assets/js/tutor-tax-settings.js:7960
msgid "Once you perform this action this can’t be undone."
msgstr ""

#: assets/js/tutor-front.js:151
msgid "Profile Photo Changed Successfully!"
msgstr ""

#: assets/js/tutor-front.js:154
msgid "Cover Photo Changed Successfully!"
msgstr ""

#: assets/js/tutor-front.js:216
msgid "Maximum file size exceeded!"
msgstr ""

#: assets/js/tutor-front.js:271
msgid "Invalid"
msgstr ""

#: assets/js/tutor-front.js:271
msgid "Invalid phone number"
msgstr ""

#: assets/js/tutor-front.js:414
msgid "Your time limit for this quiz has expired, please reattempt the quiz. Attempts remaining:"
msgstr ""

#: assets/js/tutor-front.js:428
msgid "Unfortunately, you are out of time and quiz attempts. "
msgstr ""

#: assets/js/tutor-front.js:665
msgid "Override Previous Progress"
msgstr ""

#: assets/js/tutor-front.js:666
msgid "Before continue, please decide whether to keep progress or reset."
msgstr ""

#: assets/js/tutor-front.js:669
msgid "Reset Data"
msgstr ""

#: assets/js/tutor-front.js:696
msgid "Keep Data"
msgstr ""

#: assets/js/tutor-front.js:736
msgid "Blank comment is not allowed."
msgstr ""

#: assets/js/tutor-front.js:1047
msgid "Please select an option to answer"
msgstr ""

#: assets/js/tutor-front.js:1052
msgid "Please select at least one option to answer."
msgstr ""

#: assets/js/tutor-front.js:1059
#: assets/js/tutor-front.js:1067
#: assets/js/tutor-front.js:1075
#: assets/js/tutor-front.js:1082
msgid "The answer for this question is required"
msgstr ""

#: assets/js/tutor-front.js:1272
msgid "Abandon Quiz?"
msgstr ""

#: assets/js/tutor-front.js:1273
msgid "Do you want to abandon this quiz? The quiz will be submitted partially up to this question if you leave this page."
msgstr ""

#: assets/js/tutor-front.js:1277
msgid "Yes, leave quiz"
msgstr ""

#: assets/js/tutor-front.js:1287
msgid "Leaving..."
msgstr ""

#: assets/js/tutor-front.js:1304
msgid "Stay here"
msgstr ""

#: assets/js/tutor-front.js:1523
msgid "Request Error"
msgstr ""

#: assets/js/tutor-front.js:1625
msgid "star"
msgstr ""

#: assets/js/tutor-front.js:1627
msgid "stars"
msgstr ""

#: assets/js/tutor-front.js:2368
msgid "Could not load courses"
msgstr ""

#: assets/js/tutor-front.js:2817
msgid "Attachment remove failed"
msgstr ""

#: assets/js/tutor-front.js:2938
#: assets/js/tutor-front.js:3103
#: assets/js/tutor-front.js:3268
#: assets/js/tutor-setup.js:1490
#: assets/js/tutor.js:2519
#: assets/js/tutor.js:3107
msgid "Something went wrong, please try again"
msgstr ""

#: assets/js/tutor-front.js:3248
#: assets/js/tutor-front.js:3256
#: assets/js/tutor-setup.js:1284
#: assets/js/tutor-setup.js:1374
#: assets/js/tutor-setup.js:1378
#: assets/js/tutor.js:260
#: assets/js/tutor.js:411
#: assets/js/tutor.js:414
#: assets/js/tutor.js:866
#: assets/js/tutor.js:875
#: assets/js/tutor.js:2313
#: assets/js/tutor.js:2403
#: assets/js/tutor.js:2407
#: assets/js/tutor.js:2627
#: assets/js/tutor.js:2682
#: assets/js/tutor.js:2780
msgid "Error!"
msgstr ""

#: assets/js/tutor-front.js:3248
msgid "Cart page is not configured."
msgstr ""

#: assets/js/tutor-front.js:3256
msgid "Checkout page is not configured."
msgstr ""

#: assets/js/tutor-front.js:3500
msgid "Please add a coupon code."
msgstr ""

#: assets/js/tutor-front.js:3603
msgid "Please select a payment method."
msgstr ""

#: assets/js/tutor-front.js:3838
msgid "Forward seeking is disabled"
msgstr ""

#. translators: %s is the required watch percentage (e.g., 80)
#: assets/js/tutor-front.js:3979
msgid "Watch at least %s% to complete the lesson."
msgstr ""

#: assets/js/tutor-front.js:4173
msgid "Request Successful"
msgstr ""

#: assets/js/tutor-front.js:4173
msgid "Your request has been submitted. Please wait for the administrator's response."
msgstr ""

#: assets/js/tutor-front.js:4271
msgid "Assignment answer is required."
msgstr ""

#: assets/js/tutor-gutenberg.js:19
msgid "Edit with Frontend Course Builder"
msgstr ""

#: assets/js/tutor-import-export.js:6560
msgid "Select all items"
msgstr ""

#: assets/js/tutor-import-export.js:6576
msgid "Course item"
msgstr ""

#. translators: %d is the total number of courses
#: assets/js/tutor-import-export.js:6582
msgid "Total Courses: %d"
msgstr ""

#: assets/js/tutor-import-export.js:6588
msgid "Loading"
msgstr ""

#: assets/js/tutor-import-export.js:7132
msgid "What do you want to export"
msgstr ""

#: assets/js/tutor-import-export.js:7145
msgid "Keep Media Files"
msgstr ""

#: assets/js/tutor-import-export.js:7149
msgid "If checked, course media files will also be exported with the course data."
msgstr ""

#: assets/js/tutor-import-export.js:7237
msgid "Import Successful"
msgstr ""

#: assets/js/tutor-import-export.js:7238
msgid "Import Failed"
msgstr ""

#: assets/js/tutor-import-export.js:7241
msgid "Import Complete!"
msgstr ""

#: assets/js/tutor-import-export.js:7242
msgid "Import Failed!"
msgstr ""

#: assets/js/tutor-import-export.js:7247
msgid "Your Tutor LMS data was successfully imported. However, some items couldn't be imported. Here's the list:"
msgstr ""

#: assets/js/tutor-import-export.js:7249
msgid "Your Tutor LMS data has been successfully imported."
msgstr ""

#: assets/js/tutor-import-export.js:7250
#: assets/js/tutor-import-export.js:9012
#: assets/js/tutor-import-export.js:9063
msgid "Something went wrong during import. Please try again!"
msgstr ""

#: assets/js/tutor-import-export.js:7253
msgid "Successfully Imported"
msgstr ""

#: assets/js/tutor-import-export.js:7254
msgid "Failed to Import"
msgstr ""

#: assets/js/tutor-import-export.js:7263
msgid "Export Successful"
msgstr ""

#: assets/js/tutor-import-export.js:7264
msgid "Export Failed!"
msgstr ""

#: assets/js/tutor-import-export.js:7267
msgid "Your Data is Ready to Download!"
msgstr ""

#: assets/js/tutor-import-export.js:7268
msgid "Export Failed"
msgstr ""

#: assets/js/tutor-import-export.js:7273
msgid "The export process has finished. However, certain items could not be exported. Check the summary below:"
msgstr ""

#: assets/js/tutor-import-export.js:7275
msgid "Download the JSON file and use it to import your data into another Tutor LMS website."
msgstr ""

#: assets/js/tutor-import-export.js:7276
msgid "Something went wrong during export. Please try again!"
msgstr ""

#: assets/js/tutor-import-export.js:7279
msgid "Successfully Exported"
msgstr ""

#: assets/js/tutor-import-export.js:7280
msgid "Failed to Export"
msgstr ""

#. translators: %d is the number of courses
#: assets/js/tutor-import-export.js:7287
msgid "Course (%d)"
msgid_plural "Courses (%d)"
msgstr[0] ""
msgstr[1] ""

#. translators: %d is the number of bundles
#: assets/js/tutor-import-export.js:7290
msgid "Bundle (%d)"
msgid_plural "Bundles (%d)"
msgstr[0] ""
msgstr[1] ""

#: assets/js/tutor-import-export.js:7340
#: assets/js/tutor-import-export.js:7447
msgid "Okay"
msgstr ""

#. translators: %d is the number of course IDs
#: assets/js/tutor-import-export.js:7388
msgid "Course ID (%d)"
msgid_plural "Course IDs (%d)"
msgstr[0] ""
msgstr[1] ""

#. translators: %d is the number of bundle IDs
#: assets/js/tutor-import-export.js:7402
msgid "Bundle ID (%d)"
msgid_plural "Bundle IDs (%d)"
msgstr[0] ""
msgstr[1] ""

#: assets/js/tutor-import-export.js:7490
#: assets/js/tutor-import-export.js:7491
msgid "Importing..."
msgstr ""

#: assets/js/tutor-import-export.js:7495
msgid "Exporting..."
msgstr ""

#: assets/js/tutor-import-export.js:7496
msgid "Exporting your data..."
msgstr ""

#: assets/js/tutor-import-export.js:8023
msgid "Select Courses"
msgstr ""

#: assets/js/tutor-import-export.js:8028
msgid "Edit Selected Courses"
msgstr ""

#: assets/js/tutor-import-export.js:8028
msgid "Select Specific Courses"
msgstr ""

#: assets/js/tutor-import-export.js:8034
msgid "Select Bundles"
msgstr ""

#: assets/js/tutor-import-export.js:8039
msgid "Edit Selected Bundles"
msgstr ""

#: assets/js/tutor-import-export.js:8039
msgid "Select Specific Bundles"
msgstr ""

#: assets/js/tutor-import-export.js:8101
msgid "Exporter"
msgstr ""

#: assets/js/tutor-import-export.js:8119
#: assets/js/tutor-import-export.js:8315
msgid "Export"
msgstr ""

#: assets/js/tutor-import-export.js:8134
msgid "Export in progress..."
msgstr ""

#: assets/js/tutor-import-export.js:8134
msgid "Import in progress..."
msgstr ""

#: assets/js/tutor-import-export.js:8135
msgid "exported"
msgstr ""

#: assets/js/tutor-import-export.js:8135
msgid "imported"
msgstr ""

#: assets/js/tutor-import-export.js:8136
msgid "failed"
msgstr ""

#. translators: %d is the number of courses
#: assets/js/tutor-import-export.js:8155
msgid "%d Course"
msgid_plural "%d Courses"
msgstr[0] ""
msgstr[1] ""

#. translators: %d is the number of bundles
#: assets/js/tutor-import-export.js:8158
msgid "%d Bundle"
msgid_plural "%d Bundles"
msgstr[0] ""
msgstr[1] ""

#: assets/js/tutor-import-export.js:8321
msgid "Export Data"
msgstr ""

#: assets/js/tutor-import-export.js:8323
msgid "Easily export your courses, lessons, quizzes, assignments, global settings, etc."
msgstr ""

#: assets/js/tutor-import-export.js:8344
msgid "Initiate Export"
msgstr ""

#: assets/js/tutor-import-export.js:8416
msgid "Imported"
msgstr ""

#: assets/js/tutor-import-export.js:8416
msgid "Exported"
msgstr ""

#. translators: %d is the number of courses
#: assets/js/tutor-import-export.js:8434
msgid "Courses (%d)"
msgstr ""

#: assets/js/tutor-import-export.js:8437
msgid "Bundle"
msgstr ""

#. translators: %d is the number of bundles
#: assets/js/tutor-import-export.js:8441
msgid "Bundles (%d)"
msgstr ""

#: assets/js/tutor-import-export.js:8737
msgid "Invalid JSON file format"
msgstr ""

#: assets/js/tutor-import-export.js:8741
msgid "Failed to read file"
msgstr ""

#: assets/js/tutor-import-export.js:8813
msgid "Reading file..."
msgstr ""

#: assets/js/tutor-import-export.js:8819
msgid "Ready to import"
msgstr ""

#: assets/js/tutor-import-export.js:8822
msgid "Please wait..."
msgstr ""

#: assets/js/tutor-import-export.js:8845
msgid "Replace"
msgstr ""

#: assets/js/tutor-import-export.js:8855
msgid "WARNING! Invalid file. Please upload a valid JSON file and try again."
msgstr ""

#: assets/js/tutor-import-export.js:8920
msgid "Import File"
msgstr ""

#: assets/js/tutor-import-export.js:8997
msgid "Import in progress"
msgstr ""

#: assets/js/tutor-import-export.js:9122
msgid "Choose a File"
msgstr ""

#: assets/js/tutor-import-export.js:9124
msgid "Supported format: .JSON"
msgstr ""

#: assets/js/tutor-order-details.js:6232
msgid "You can see all the activities against this order chronologically."
msgstr ""

#: assets/js/tutor-order-details.js:6233
msgid "Order activities"
msgstr ""

#: assets/js/tutor-order-details.js:6256
msgid "Add a comment (Only admin can see comments.)"
msgstr ""

#: assets/js/tutor-order-details.js:6257
msgid "Write a comment for this order..."
msgstr ""

#: assets/js/tutor-order-details.js:6267
msgid "Post"
msgstr ""

#: assets/js/tutor-order-details.js:6314
msgid "Notes"
msgstr ""

#: assets/js/tutor-order-details.js:6316
msgid "No notes"
msgstr ""

#: assets/js/tutor-order-details.js:9285
msgid "Percentage"
msgstr ""

#: assets/js/tutor-order-details.js:9347
msgid "Select discount type"
msgstr ""

#: assets/js/tutor-order-details.js:9359
msgid "Should not be more than 100%."
msgstr ""

#: assets/js/tutor-order-details.js:9362
msgid "Discount should not exceed the total price."
msgstr ""

#: assets/js/tutor-order-details.js:9378
msgid "Price after the discount: "
msgstr ""

#: assets/js/tutor-order-details.js:9386
msgid "Discount Reason"
msgstr ""

#: assets/js/tutor-order-details.js:9387
msgid "Enter the reason of this discount"
msgstr ""

#: assets/js/tutor-order-details.js:9486
msgid "This will create an order. Mark this as paid if you have manually received "
msgstr ""

#: assets/js/tutor-order-details.js:9486
msgid " manually."
msgstr ""

#: assets/js/tutor-order-details.js:9491
#: assets/js/tutor-order-details.js:10576
msgid "Note"
msgstr ""

#: assets/js/tutor-order-details.js:9493
msgid "Write some note against this action."
msgstr ""

#: assets/js/tutor-order-details.js:9511
#: assets/js/tutor-order-details.js:10145
msgid "Mark as Paid"
msgstr ""

#: assets/js/tutor-order-details.js:9765
msgid "Refund amount must be greater than zero."
msgstr ""

#: assets/js/tutor-order-details.js:9768
msgid "Entered amount exceeds course payment."
msgstr ""

#: assets/js/tutor-order-details.js:9785
msgid "Available"
msgstr ""

#: assets/js/tutor-order-details.js:9785
msgid "for refund"
msgstr ""

#: assets/js/tutor-order-details.js:9791
msgid "Reason"
msgstr ""

#: assets/js/tutor-order-details.js:9792
msgid "Enter the reason of this refund"
msgstr ""

#: assets/js/tutor-order-details.js:9802
msgid "Remove the student from enrollment"
msgstr ""

#: assets/js/tutor-order-details.js:9810
msgid "Cancel the subscription"
msgstr ""

#: assets/js/tutor-order-details.js:9820
msgid "Note: Refund won't be processed automatically. You are required to process the refund manually via the payment gateway."
msgstr ""

#: assets/js/tutor-order-details.js:9914
msgid "Partially refunded"
msgstr ""

#: assets/js/tutor-order-details.js:9998
msgid "Payment"
msgstr ""

#: assets/js/tutor-order-details.js:10016
msgid "Item(s)"
msgstr ""

#: assets/js/tutor-order-details.js:10023
msgid "Coupon"
msgstr ""

#: assets/js/tutor-order-details.js:10040
#: assets/js/tutor-order-details.js:10052
#: assets/js/tutor-order-details.js:10065
msgid "Add discount"
msgstr ""

#: assets/js/tutor-order-details.js:10091
msgid "Estimated tax"
msgstr ""

#: assets/js/tutor-order-details.js:10095
msgid "Total Paid"
msgstr ""

#. translators: %s is the tax amount formatted as a price
#: assets/js/tutor-order-details.js:10100
msgid "Incl. tax %s"
msgstr ""

#. translators: %s is the refund reason or '-' if none
#: assets/js/tutor-order-details.js:10117
msgid "Reason: %s"
msgstr ""

#: assets/js/tutor-order-details.js:10123
msgid "Net payment"
msgstr ""

#. translators: %s is the payment gateway name
#: assets/js/tutor-order-details.js:10193
msgid "Gateway: %s"
msgstr ""

#: assets/js/tutor-order-details.js:10193
msgid "Manual"
msgstr ""

#: assets/js/tutor-order-details.js:10285
msgid "Contact information"
msgstr ""

#: assets/js/tutor-order-details.js:10377
msgid "Order Summary"
msgstr ""

#: assets/js/tutor-order-details.js:10384
msgid "No course added."
msgstr ""

#: assets/js/tutor-order-details.js:10464
msgid "Customer changed or canceled order"
msgstr ""

#: assets/js/tutor-order-details.js:10468
msgid "The customer has modified or canceled their order. This action indicates that the customer has either updated their order details or decided to cancel their order entirely. Please review the order history for specific changes or cancellation details."
msgstr ""

#: assets/js/tutor-order-details.js:10470
msgid "Payment declined"
msgstr ""

#: assets/js/tutor-order-details.js:10472
msgid "Payment is declined by the gateway."
msgstr ""

#: assets/js/tutor-order-details.js:10474
msgid "Fraudulent order"
msgstr ""

#: assets/js/tutor-order-details.js:10478
msgid "The order has been flagged as fraudulent. This action indicates that the order has been identified as potentially fraudulent and requires immediate attention. Please investigate the order details and take appropriate measures to prevent any unauthorized transactions."
msgstr ""

#: assets/js/tutor-order-details.js:10480
msgid "Courses unavailable"
msgstr ""

#: assets/js/tutor-order-details.js:10482
msgid "Unfortunately the courses selected on this order is not anymore available."
msgstr ""

#: assets/js/tutor-order-details.js:10505
msgid "Please select a reason for the order cancellation. Your input is valuable for understanding the cause."
msgstr ""

#: assets/js/tutor-order-details.js:10559
msgid "Reason for Cancellation"
msgstr ""

#: assets/js/tutor-order-details.js:10561
msgid "Select a reason"
msgstr ""

#: assets/js/tutor-order-details.js:10577
msgid "Write a note for this action."
msgstr ""

#: assets/js/tutor-order-details.js:10592
msgid "Keep order"
msgstr ""

#: assets/js/tutor-order-details.js:10597
msgid "Cancel order"
msgstr ""

#. translators: %s is the order number or ID
#: assets/js/tutor-order-details.js:10690
msgid "Order #%s"
msgstr ""

#. translators: %s is the order number
#: assets/js/tutor-order-details.js:10718
msgid "Cancel order #%s"
msgstr ""

#: assets/js/tutor-order-details.js:10724
msgid "Cancel Order"
msgstr ""

#: assets/js/tutor-payment-settings.js:5328
#: assets/js/tutor-payment-settings.js:11832
msgid "Icon"
msgstr ""

#: assets/js/tutor-payment-settings.js:5337
msgid "Payment Instructions"
msgstr ""

#: assets/js/tutor-payment-settings.js:5339
msgid "Provide clear, step-by-step instructions on how to complete the payment."
msgstr ""

#: assets/js/tutor-payment-settings.js:11686
msgid "Copied to clipboard"
msgstr ""

#: assets/js/tutor-payment-settings.js:11695
msgid "Failed to copy: "
msgstr ""

#: assets/js/tutor-payment-settings.js:11841
#: assets/js/tutor-payment-settings.js:12641
msgid "Supports Subscriptions"
msgstr ""

#. translators: %s is the label of the item to remove
#: assets/js/tutor-payment-settings.js:11996
msgid "Remove %s"
msgstr ""

#: assets/js/tutor-payment-settings.js:11997
msgid "Are you sure you want to remove this payment method?"
msgstr ""

#: assets/js/tutor-payment-settings.js:12059
msgid "Update Available"
msgstr ""

#: assets/js/tutor-payment-settings.js:12091
msgid "Update Now"
msgstr ""

#: assets/js/tutor-payment-settings.js:12100
msgid "Plugin Not Installed"
msgstr ""

#: assets/js/tutor-payment-settings.js:12109
msgid "Plugin Not Activated"
msgstr ""

#: assets/js/tutor-payment-settings.js:12178
msgid "Necessary plugin is not installed to display options!"
msgstr ""

#: assets/js/tutor-payment-settings.js:12222
#: assets/js/tutor-payment-settings.js:12498
msgid "Recommended size: 48x48"
msgstr ""

#: assets/js/tutor-payment-settings.js:12347
msgid "Supported payment methods"
msgstr ""

#: assets/js/tutor-payment-settings.js:12479
msgid "e.g. Bank Transfer"
msgstr ""

#: assets/js/tutor-payment-settings.js:12645
msgid "Installed"
msgstr ""

#: assets/js/tutor-payment-settings.js:12651
msgid "Install"
msgstr ""

#: assets/js/tutor-payment-settings.js:12847
msgid "Payment gateways"
msgstr ""

#: assets/js/tutor-payment-settings.js:12853
#: assets/js/tutor-payment-settings.js:12864
msgid "Add New Gateway"
msgstr ""

#: assets/js/tutor-payment-settings.js:12877
msgid "Set up manual payment method"
msgstr ""

#: assets/js/tutor-payment-settings.js:12883
msgid "Add Manual Payment"
msgstr ""

#: assets/js/tutor-setup.js:22
#: assets/js/tutor.js:22
msgid "Show Less"
msgstr ""

#: assets/js/tutor-setup.js:78
#: assets/js/tutor.js:78
msgid "weak"
msgstr ""

#: assets/js/tutor-setup.js:84
#: assets/js/tutor.js:84
msgid "medium"
msgstr ""

#: assets/js/tutor-setup.js:97
#: assets/js/tutor.js:97
msgid "strong"
msgstr ""

#: assets/js/tutor-setup.js:1225
#: assets/js/tutor-setup.js:1258
#: assets/js/tutor.js:2254
#: assets/js/tutor.js:2287
msgid "Are you sure to delete?"
msgstr ""

#: assets/js/tutor-setup.js:1339
#: assets/js/tutor.js:250
#: assets/js/tutor.js:859
#: assets/js/tutor.js:2368
msgid "Updating..."
msgstr ""

#: assets/js/tutor-tax-settings.js:7597
#: assets/js/tutor-tax-settings.js:7694
#: assets/js/tutor-tax-settings.js:10757
msgid "Region"
msgstr ""

#: assets/js/tutor-tax-settings.js:7599
msgid "Select state"
msgstr ""

#: assets/js/tutor-tax-settings.js:7707
#: assets/js/tutor-tax-settings.js:10778
msgid "Tax rate"
msgstr ""

#: assets/js/tutor-tax-settings.js:7752
msgid "VAT on sales"
msgstr ""

#: assets/js/tutor-tax-settings.js:7753
msgid "Add region you want to collect tax & their tax rates"
msgstr ""

#: assets/js/tutor-tax-settings.js:7761
msgid "VAT registration type"
msgstr ""

#: assets/js/tutor-tax-settings.js:7762
msgid "Select VAT registration type"
msgstr ""

#: assets/js/tutor-tax-settings.js:7764
msgid "One-Stop Shop registration"
msgstr ""

#: assets/js/tutor-tax-settings.js:7767
msgid "Micro-business exemption"
msgstr ""

#: assets/js/tutor-tax-settings.js:7779
#: assets/js/tutor-tax-settings.js:7793
msgid "Add region & VAT rate"
msgstr ""

#: assets/js/tutor-tax-settings.js:7782
#: assets/js/tutor-tax-settings.js:7796
msgid "Add Country & Tax rate"
msgstr ""

#: assets/js/tutor-tax-settings.js:8240
msgid "Delete Tax Rate"
msgstr ""

#: assets/js/tutor-tax-settings.js:8442
msgid "Tax is already included in my prices"
msgstr ""

#: assets/js/tutor-tax-settings.js:8445
msgid "Tax should be calculated and displayed on the checkout page"
msgstr ""

#: assets/js/tutor-tax-settings.js:8449
msgid "Global Tax Settings"
msgstr ""

#: assets/js/tutor-tax-settings.js:8450
msgid "Set how taxes are displayed and applied to your courses."
msgstr ""

#: assets/js/tutor-tax-settings.js:8469
msgid "Display prices inclusive tax"
msgstr ""

#: assets/js/tutor-tax-settings.js:8475
msgid "Show prices with tax included, so customers see the final amount they’ll pay upfront."
msgstr ""

#: assets/js/tutor-tax-settings.js:10563
msgid "Search region"
msgstr ""

#: assets/js/tutor-tax-settings.js:10564
msgid "e.g. Arizona"
msgstr ""

#: assets/js/tutor-tax-settings.js:10757
msgid "Countries"
msgstr ""

#: assets/js/tutor-tax-settings.js:10937
#: assets/js/tutor-tax-settings.js:11102
#: assets/js/tutor-tax-settings.js:11106
msgid "Add tax region"
msgstr ""

#: assets/js/tutor-tax-settings.js:10940
msgid "Add Region"
msgstr ""

#: assets/js/tutor-tax-settings.js:10943
msgid "Tax Regions & Rates"
msgstr ""

#: assets/js/tutor-tax-settings.js:10944
msgid "Specify regions and their applicable tax rates."
msgstr ""

#: assets/js/tutor-tax-settings.js:10950
msgid "Apply single tax rate for entire country"
msgstr ""

#: assets/js/tutor-tax-settings.js:10974
msgid "Add State & VAT Rate"
msgstr ""

#: assets/js/tutor-tax-settings.js:10977
msgid "Add State"
msgstr ""

#: assets/js/tutor-tax-settings.js:11099
msgid "Tax Banner"
msgstr ""

#: assets/js/tutor-tax-settings.js:11100
msgid "Configure Tax Rates"
msgstr ""

#: assets/js/tutor-tax-settings.js:11101
msgid "Start configuring the tax settings to set up and manage the tax rates."
msgstr ""

#: assets/js/tutor.js:241
msgid "Rating and review required"
msgstr ""

#: assets/js/tutor.js:265
msgid "Updated successfully!"
msgstr ""

#: assets/js/tutor.js:265
msgid "Thank You for Rating The Course!"
msgstr ""

#: assets/js/tutor.js:265
msgid "Updated rating will now be visible in the course page"
msgstr ""

#: assets/js/tutor.js:265
msgid "Your rating will now be visible in the course page"
msgstr ""

#: assets/js/tutor.js:371
msgid "Copied!"
msgstr ""

#: assets/js/tutor.js:394
msgid "Deleting..."
msgstr ""

#: assets/js/tutor.js:1363
msgid "January"
msgstr ""

#: assets/js/tutor.js:1363
msgid "February"
msgstr ""

#: assets/js/tutor.js:1363
msgid "March"
msgstr ""

#: assets/js/tutor.js:1363
msgid "April"
msgstr ""

#: assets/js/tutor.js:1363
msgid "May"
msgstr ""

#: assets/js/tutor.js:1363
msgid "June"
msgstr ""

#: assets/js/tutor.js:1363
msgid "July"
msgstr ""

#: assets/js/tutor.js:1363
msgid "August"
msgstr ""

#: assets/js/tutor.js:1363
msgid "September"
msgstr ""

#: assets/js/tutor.js:1363
msgid "October"
msgstr ""

#: assets/js/tutor.js:1363
msgid "November"
msgstr ""

#: assets/js/tutor.js:1363
msgid "December"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Sun"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Mon"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Tue"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Wed"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Thu"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Fri"
msgstr ""

#: assets/js/tutor.js:1367
msgid "Sat"
msgstr ""

#: assets/js/tutor.js:1741
msgid "Time"
msgstr ""

#. translators: %d is the number of days selected
#: assets/js/tutor.js:1882
msgid "%d day selected"
msgid_plural "%d days selected"
msgstr[0] ""
msgstr[1] ""

#: assets/js/tutor.js:1882
msgid "0 day selected"
msgstr ""

#: assets/js/tutor.js:2754
#: assets/js/tutor.js:2759
msgid "Empty Content not Allowed"
msgstr ""

#: assets/js/tutor.js:3040
msgid "Course status updated"
msgstr ""

#: assets/js/tutor.js:3048
msgid "Course status update failed"
msgstr ""

<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita91407656d66b1ad0a0c99a5567d747f
{
    public static $prefixLengthsPsr4 = array (
        'T' => 
        array (
            'TutorLMSDroip\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'TutorLMSDroip\\' => 
        array (
            0 => __DIR__ . '/../..' . '/backend',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita91407656d66b1ad0a0c99a5567d747f::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita91407656d66b1ad0a0c99a5567d747f::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita91407656d66b1ad0a0c99a5567d747f::$classMap;

        }, null, ClassLoader::class);
    }
}

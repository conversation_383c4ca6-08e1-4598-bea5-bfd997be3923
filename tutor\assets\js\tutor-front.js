(()=>{var t={23880:()=>{function t(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=e(t))||r&&t&&"number"==typeof t.length){n&&(t=n);var o=0,i=function t(){};return{s:i,n:function e(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,c=!1;return{s:function e(){n=n.call(t)},n:function t(){var e=n.next();return u=e.done,e},e:function t(e){c=!0,a=e},f:function t(){try{u||null==n["return"]||n["return"]()}finally{if(c)throw a}}}}function e(t,e){if(t){if("string"==typeof t)return r(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}document.addEventListener("DOMContentLoaded",(function(){var e=wp.i18n,r=e.__,n=e._x,o=e._n,i=e._nx;var a=document.querySelectorAll(".tutor-export-purchase-history");var u=t(a),c;try{for(u.s();!(c=u.n()).done;){var s=c.value;if(s){s.onclick=function(t){var e=t.currentTarget;var r="order-".concat(e.dataset.order,"-purchase-history.csv");var n=[{"Order ID ":e.dataset.order,"Course Name":e.dataset.courseName,Price:e.dataset.price,Date:e.dataset.date,Status:e.dataset.status}];l(n,r)}}}}catch(t){u.e(t)}finally{u.f()}function l(t,e){var r=Object.keys(t[0]);var n=[r.join(","),t.map((function(t){return r.map((function(e){return t[e]})).join(",")})).join("\n")].join("\n");var o=new Blob([n],{type:"text/csv;charset=utf-8"});var i=URL.createObjectURL(o);var a=document.createElement("a");a.setAttribute("href",i);a.setAttribute("download",e);a.style.visibility="hidden";document.body.appendChild(a);a.click();document.body.removeChild(a)}}))},24081:()=>{window.jQuery(document).ready((function(t){t("div.tutor-lesson-wrapper [data-mce-style]").each((function(){t(this).attr("style",t(this).attr("data-mce-style"));t(this).removeAttr("data-mce-style")}));t(document).on("click",'.tutor-single-course-lesson-comments button[type="submit"]',(function(e){e.preventDefault();var r=wp.i18n.__;var n=t(this);var o=n.closest("form");var i=o.serialize();var a=o.find('textarea[name="comment"]').val();if(a.trim().length===0){tutor_toast(r("Warning","tutor"),r("Blank comment is not allowed.","tutor"),"error");return}t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:i,beforeSend:function t(){n.addClass("is-loading").prop("disabled",true)},complete:function t(){n.removeClass("is-loading");n.removeAttr("disabled")},success:function e(r){var n=o.attr("tutor-comment-reply");if(typeof n!=="undefined"&&n!==false){o.before(r.data.html)}else{var i=document.querySelector(".tutor-course-spotlight-comments");i.innerHTML=r.data.html}t(".tutor-comment-line").css("height","calc(100% - 308px)");t("textarea").val("")},error:function t(e){n.removeClass("is-loading").prop("disabled",false)}})}))}))},31162:()=>{window.jQuery(document).ready((function(t){var e=window.wp.i18n.__;if(t(".tutor-quiz-wrap").length){if(!t(".tutor-table-quiz-attempts").length&&!t(".tutor-quiz-attempt-details").length){t(".tutor-course-topic-single-footer").remove()}}var r=t("#tutor-quiz-time-update");if(r.length){var n=JSON.parse(r.attr("data-attempt-settings"));var o=JSON.parse(r.attr("data-attempt-meta"));if(o.time_limit.time_limit_seconds>0){var i,a;var u=new Date((i=n.attempt_started_at)===null||i===void 0?void 0:i.replaceAll("-","/")).getTime()+o.time_limit.time_limit_seconds*1e3;var c=new Date((a=o.date_time_now)===null||a===void 0?void 0:a.replaceAll("-","/")).getTime();var s=setInterval((function(){var n=u-c;var i=Math.floor(n/(1e3*60*60*24));var a=Math.floor(n%(1e3*60*60*24)/(1e3*60*60));var l=Math.floor(n%(1e3*60*60)/(1e3*60));var f=Math.floor(n%(1e3*60)/1e3);var d="";i?d+=i+"d ":0;d+=(a||0)+"h ";d+=(l||0)+"m ";d+=(f||0)+"s ";if(n<0){clearInterval(s);r.toggleClass("tutor-quiz-time-expired");d="EXPIRED";if(_tutorobject.quiz_options.quiz_when_time_expires==="auto_submit"){t("form#tutor-answering-quiz").submit()}else{t(".tutor-quiz-answer-next-btn, .tutor-quiz-submit-btn, .tutor-quiz-answer-previous-btn").prop("disabled",true);t("button[name='quiz_answer_submit_btn']").prop("disabled",true);t(".time-remaining span").css("color","#F44337");t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{quiz_id:t("#tutor_quiz_id").val(),action:"tutor_quiz_timeout"},success:function n(o){var i=t("#tutor-quiz-time-expire-wrapper").data("attempt-allowed");var a=t("#tutor-quiz-time-expire-wrapper").data("attempt-remaining");var u="#tutor-quiz-time-expire-wrapper";t(u).addClass("tutor-alert-show");if(a>0){t("".concat(u," .tutor-quiz-alert-text")).html(e("Your time limit for this quiz has expired, please reattempt the quiz. Attempts remaining:","tutor")+" "+a+"/"+i)}else{if(t(u).hasClass("time-remaining-warning")){t(u).removeClass("time-remaining-warning");t(u).addClass("time-over")}if(t("".concat(u," .flash-info span:first-child")).hasClass("tutor-icon-circle-info")){t("".concat(u," .flash-info span:first-child")).removeClass("tutor-icon-circle-info");t("".concat(u," .flash-info span:first-child")).addClass("tutor-icon-circle-times-line")}r.toggleClass("tutor-quiz-time-expired");t("#tutor-start-quiz").hide();t("".concat(u," .tutor-quiz-alert-text")).html("".concat(e("Unfortunately, you are out of time and quiz attempts. ","tutor")));window.location.reload(true)}},complete:function t(){}})}}c=c+1e3;r.html(d);if(d=="EXPIRED"){r.addClass("color-text-error")}if(n){var h=n/1e3;var p=o.time_limit.time_limit_seconds;var v=Math.ceil(h*100/p);var m=document.querySelector(".quiz-time-remaining-progress-circle");var y=document.querySelector(".quiz-time-remaining-progress-circle svg");if(y&&m){var g=44-44*(v/100);if(v<=0){v=0;m.innerHTML='<svg viewBox="0 0 50 50" width="50" height="50">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<circle cx="0" cy="0" r="11"></circle>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>';m.setAttribute("class","quiz-time-remaining-expired-circle")}y.setAttribute("style","stroke-dashoffset: ".concat(g,";"))}}}),1e3)}else{r.html(e("No Limit","tutor"))}}var l=t("form#tutor-start-quiz");if(l.length){if(_tutorobject.quiz_options.quiz_auto_start==1){l.submit()}}}))},32924:()=>{document.addEventListener("DOMContentLoaded",(function(){var t=window.jQuery;t('.tutor-dashboard-setting-withdraw input[name="tutor_selected_withdraw_method"]').on("change",(function(e){var r=t(this);var n=r.closest("form");n.find(".withdraw-method-form").hide();n.find(".withdraw-method-form").hide().filter('[data-withdraw-form="'+r.val()+'"]').show()}))}))},37942:(t,e,r)=>{var n=r(44510),o=n.get_response_message;window.jQuery(document).ready((function(t){var e=wp.i18n.__;t('.tutor-settings-pass-field [name="confirm_new_password"]').on("input",(function(){var e=t('[name="new_password"]');var r=(e.val()||"").trim();var n=r&&t(this).val()===r;t(this).parent().find(".tutor-validation-icon")[n?"show":"hide"]()}));t(".tutor-profile-password-reset").click((function(r){r.preventDefault();var n=t(this);var i=n.closest("form");var a=i.serializeObject();a.action="tutor_profile_password_reset";t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:a,beforeSend:function t(){n.addClass("is-loading")},success:function t(r){var t=r.success;if(t){window.tutor_toast(e("Success","tutor"),o(r),"success");window.location.reload()}else{window.tutor_toast(e("Error","tutor"),o(r),"error")}},complete:function t(){n.removeClass("is-loading")}})}))}))},44510:(t,e,r)=>{"use strict";r.r(e);r.d(e,{get_response_message:()=>n});var n=function t(e,r){var n=wp.i18n.__;var o=e||{},i=o.data,a=i===void 0?{}:i;var u=a.message,c=u===void 0?r||n("Something Went Wrong!","tutor"):u;return c}},48159:()=>{window.jQuery(document).ready((function(t){var e=window.wp.i18n.__;var r=_tutorobject.quiz_options;var n=new Map;t(".tutor-sortable-list").on("sortchange",o);function o(e,r){var o=parseInt(t(this).closest(".quiz-attempt-single-question").attr("id").match(/\d+/)[0],10);if(!n.get(o)){n.set(o,true)}}function i(){return Number(_tutorobject.quiz_answer_display_time)||2e3}function a(){return"reveal"===r.feedback_mode}function u(){return _tutorobject.quiz_options.question_layout_view}function c(t){return'<span class="tutor-quiz-answer-single-info tutor-color-success tutor-mt-8">\n            <i class="tutor-icon-mark tutor-color-success" area-hidden="true"></i>\n            '.concat(t,"\n        </span>")}function s(r){var n=false;var o=JSON.parse(window.tutor_quiz_context.split("").reverse().join(""));!Array.isArray(o)?o=[]:0;if(u()!=="question_below_each_other"){t(".tutor-quiz-answer-single-info").remove()}t(".tutor-quiz-answer-single").removeClass("tutor-quiz-answer-single-correct tutor-quiz-answer-single-incorrect");var i=true;var s=r.find("input");var l=r.find('input[type="radio"]:checked, input[type="checkbox"]:checked');if(a()){l.each((function(){var e=t(this);var r=o.indexOf(e.val())>-1;if(!r){i=false}}));s.each((function(){var r=t(this);var a=r.attr("type");if(a==="radio"||a==="checkbox"){var u=o.indexOf(r.val())>-1;var s=r.is(":checked");if(u){r.closest(".tutor-quiz-answer-single").addClass("tutor-quiz-answer-single-correct").append(c(e("Correct Answer","tutor"))).find(".tutor-quiz-answer-single-info:eq(1)").remove()}else{if(r.prop("checked")){r.closest(".tutor-quiz-answer-single").addClass("tutor-quiz-answer-single-incorrect")}}if(u&&!s){r.attr("disabled","disabled");i=false;n=true}}}))}if(i){n=true}return n}function l(e){var r=true;var n=e[0];var o=t(n).find(".tutor-dropzone");if(o.length>0){Object.values(o).forEach((function(e){if(e instanceof Element&&e.classList.contains("tutor-dropzone")){if(t(e).has("input").length===0){r=false}}}))}return r}function f(r,o){var i=r.find(".quiz-answer-required");if(i.length){var a=parseInt(r.attr("id").match(/\d+/)[0],10);var u=n.get(a);var c=r.find(".tutor-draggable");var s=r.find(".ui-sortable");var f=i.find("input");if(f.length){var d=f.attr("type");if(d==="radio"){if(i.find('input[type="radio"]:checked').length==0){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("Please select an option to answer","tutor"),"</p>"));o=false}}else if(d==="checkbox"){if(i.find('input[type="checkbox"]:checked').length==0){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("Please select at least one option to answer.","tutor"),"</p>"));o=false}}else if(d==="text"){f.each((function(n,i){if(!t(i).val().trim().length){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("The answer for this question is required","tutor"),"</p>"));o=false}}))}}if(i.find("textarea").length){if(i.find("textarea").val().trim().length<1){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("The answer for this question is required","tutor"),"</p>"));o=false}}if(c.length){var h=l(i);if(!h){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("The answer for this question is required","tutor"),"</p>"));o=false}}if(u===undefined&&s.length){r.find(".answer-help-block").html('<p style="color: #dc3545">'.concat(e("The answer for this question is required","tutor"),"</p>"));o=false}}return o}t(".tutor-quiz-next-btn-all").prop("disabled",false);t(".quiz-attempt-single-question input").filter('[type="radio"], [type="checkbox"]').change((function(){t(".tutor-quiz-next-btn-all").prop("disabled",false)}));t(document).on("click",".tutor-quiz-answer-next-btn, .tutor-quiz-answer-previous-btn",(function(e){e.preventDefault();var r=t(".tutor-quiz-question-counter>span:first-child");var n=parseInt(t(this).closest("[data-question_index]").data("question_index"));if(t(this).hasClass("tutor-quiz-answer-previous-btn")){t(this).closest(".quiz-attempt-single-question").hide().prev().show();r.text(n-1);return}var o=t(this);var u=o.closest(".quiz-attempt-single-question");var c=parseInt(o.closest(".quiz-attempt-single-question").attr("id").match(/\d+/)[0],10);var l=o.closest(".quiz-attempt-single-question").attr("data-next-question-id");var d=true;d=f(u,d);if(!d){return}var h=s(u);if(!a()){if(!h){return}}if(l){var p=t(l);if(p&&p.length){if(a()){setTimeout((function(){t(".quiz-attempt-single-question").hide();p.show()}),i())}else{t(".quiz-attempt-single-question").hide();p.show()}if(t(".tutor-quiz-questions-pagination").length){t(".tutor-quiz-question-paginate-item").removeClass("active");t('.tutor-quiz-questions-pagination a[href="'+l+'"]').addClass("active")}r.text(n+1)}}}));t(document).on("click",".tutor-quiz-question-paginate-item",(function(e){e.preventDefault();var r=t(this);var n=t(r.attr("href"));t(".quiz-attempt-single-question").hide();n.show();t(".tutor-quiz-question-paginate-item").removeClass("active");r.addClass("active")}));t(document).on("keyup","textarea.question_type_short_answer, textarea.question_type_open_ended",(function(e){var r=t(this);var n=r.val();var o=r.hasClass("question_type_short_answer")?_tutorobject.quiz_options.short_answer_characters_limit:_tutorobject.quiz_options.open_ended_answer_characters_limit;if(!o){return}var i=o-n.length;if(i<1){r.val(n.substr(0,o));i=0}r.closest(".quiz-attempt-single-question").find(".characters_remaining").html(i)}));t(document).on("submit","#tutor-answering-quiz",(function(e){e.preventDefault();var r=t(".quiz-attempt-single-question");var n=document.querySelector(".tutor-quiz-submit-btn");var o=t(e.target);var c=true;var l=true;if(r.length){r.each((function(e,r){c=f(t(r),c);l=s(t(r))}))}if(_tutorobject.quiz_options.quiz_when_time_expires==="auto_submit"&&t("#tutor-quiz-time-update").hasClass("tutor-quiz-time-expired")){c=true;l=true}if(c&&l){var d=500;if(a()&&u()==="question_below_each_other"){d=i();o.find(":submit").addClass("is-loading").attr("disabled","disabled")}setTimeout((function(){e.target.submit()}),d)}else{if(n){n.classList.remove("is-loading");n.disabled=false}}}));t(".tutor-quiz-submit-btn").click((function(e){var r=this;e.preventDefault();if(a()){var n=t(".quiz-attempt-single-question");var o=true;if(n.length){n.each((function(e,r){o=f(t(r));o=s(t(r))}))}t(this).attr("disabled","disabled");setTimeout((function(){t(r).addClass("is-loading");t("#tutor-answering-quiz").submit()}),i())}else{t(this).attr("disabled","disabled").addClass("is-loading");t("#tutor-answering-quiz").submit()}}));var d=t("#tutor-quiz-time-update");t(document).on("click","a",(function(r){if(r.target.classList.contains("sidebar-ask-new-qna-btn")||r.target.classList.contains("tutor-quiz-question-paginate-item")){return}if(d.length>0&&d.text()!="EXPIRED"){r.preventDefault();r.stopImmediatePropagation();var n;var o={title:e("Abandon Quiz?","tutor"),description:e("Do you want to abandon this quiz? The quiz will be submitted partially up to this question if you leave this page.","tutor"),buttons:{keep:{title:e("Yes, leave quiz","tutor"),id:"leave",class:"tutor-btn tutor-btn-outline-primary",callback:function r(){var o=t("form#tutor-answering-quiz").serialize()+"&action="+"tutor_quiz_abandon";t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:o,beforeSend:function t(){document.querySelector("#tutor-popup-leave").innerHTML=e("Leaving...","tutor")},success:function t(r){if(r.success){location.reload(true)}else{alert(e("Something went wrong","tutor"))}},error:function t(){alert(e("Something went wrong","tutor"));n.find("[data-tutor-modal-close]").click()}})}},reset:{title:e("Stay here","tutor"),id:"reset",class:"tutor-btn tutor-btn-primary tutor-ml-20",callback:function t(){n.find("[data-tutor-modal-close]").click()}}}};n=new window.tutor_popup(t,"").popup(o)}}));t("body").on("submit","form#tutor-start-quiz",(function(){t(this).find("button").prop("disabled",true)}))}))},51019:()=>{window.readyState_complete=function(t){var e=function t(e){return e()};document.addEventListener("readystatechange",(function(r){return r.target.readyState==="complete"?typeof t=="function"?setTimeout((function(){return e(t)})):"":""}))};window.addBodyClass=function(t){var e=new URL(t);var r=e.searchParams.get("tab_page");var n=e.searchParams.get("edit")&&"_edit";document.body.classList.add(r);document.body.classList.add(r+n)};window.selectorById=function(t){return document.getElementById(t)};window.selectorByClass=function(t){return document.getElementsByClassName(t)};window.json_download=function(t,e){var r=new Blob([t],{type:"application/json"});var n=document.createElement("a");n.href=URL.createObjectURL(r);n.download=e;n.click()}},51379:()=>{document.addEventListener("DOMContentLoaded",(function(){var t=document.getElementById("tutor-course-save-draft");if(t){t.onclick=function(e){e.preventDefault();t.setAttribute("disabled","disabled");t.classList.add("is-loading");document.getElementById("tutor-frontend-course-builder").submit()}}var e=jQuery(".tutor-table-responsive .tutor-table .tutor-dropdown");if(e.length){var r=jQuery(".tutor-table-responsive .tutor-table").height();jQuery(".tutor-table-responsive").css("min-height",r+110)}}))},56093:()=>{document.addEventListener("DOMContentLoaded",(function(){var t=window.jQuery;t(".tutor-dashboard .tutor-dashboard-menu-toggler").click((function(){var e=t(".tutor-dashboard-left-menu");e.closest(".tutor-dashboard").toggleClass("is-sidebar-expanded");if(e.css("display")!=="none"){e.get(0).scrollIntoView({block:"start"})}}))}))},64901:()=>{window.jQuery(document).ready((function(t){t(document).on("added_to_cart",(function(t,e,r,n){n.removeClass("is-loading");n.siblings("a.added_to_cart").addClass("tutor-btn tutor-btn-outline-primary tutor-btn-md tutor-btn-block").prepend('<span class="tutor-icon-cart-line tutor-mr-8"></span>')}));t(document).on("adding_to_cart",(function(t,e){e.addClass("is-loading");setTimeout((function(){e.removeClass("is-loading")}),4e3)}))}))},79211:()=>{window.selectSearchField=function(t){var e=document.querySelectorAll(t);(function(){e.forEach((function(t){if(t&&!t.classList.contains("tutor-js-form-select")&&!t.hasAttribute("noDropdown")&&!t.classList.contains("no-tutor-dropdown")){var e=t.hasAttribute("data-searchable");var o=t.options[t.selectedIndex];t.style.display="none";var i,a,u,c,s,l,f,d;t.insertAdjacentHTML("afterend",n(t.options,t.value,e));i=t.nextElementSibling;a=i.querySelector(".tutor-form-select-search");u=a&&a.querySelector("input");d=i.querySelector(".tutor-form-select-dropdown");var h=i.querySelector(".tutor-form-select-label");h.innerText=o&&o.text;i.onclick=function(t){t.stopPropagation();r(document.querySelectorAll(".tutor-js-form-select"),i);i.classList.toggle("is-active");if(u){setTimeout((function(){u.focus()}),100)}d.onclick=function(t){t.stopPropagation()}};r(document.querySelectorAll(".tutor-js-form-select"));s=i.querySelector(".tutor-form-select-options");l=s&&s.querySelectorAll(".tutor-form-select-option");if(l){l.forEach((function(e){e.onclick=function(r){r.stopPropagation();var n=Array.from(t.options);n.forEach((function(n,o){if(n.value===r.target.dataset.key){var a;(a=s.querySelector(".is-active"))===null||a===void 0||a.classList.remove("is-active");e.classList.add("is-active");i.classList.remove("is-active");h.innerText=r.target.innerText;h.dataset.value=n.value;t.value=n.value;var u=document.getElementById("save_tutor_option");if(u){u.disabled=false}}}));var o=new Event("change",{bubbles:true});t.dispatchEvent(o)}}))}var p=function t(e){var r=0;e.forEach((function(t){if(t.style.display!=="none"){r+=1}}));return r};if(u){u.oninput=function(t){var e,r=false;c=t.target.value.toUpperCase();l.forEach((function(t){f=t.querySelector("[tutor-dropdown-item]");e=f.textContent||f.innerText;if(e.toUpperCase().indexOf(c)>-1){t.style.display="";r="false"}else{r="true";t.style.display="none"}}));var n='\n\t\t\t\t\t\t\t<div class="tutor-form-select-option noItem tutor-text-center tutor-fs-7">\n\t\t\t\t\t\t\t\t'.concat(window.wp.i18n.__("No item found","tutor"),"\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t");var o=d.querySelector(".tutor-form-select-options");if(0==p(l)){var i=false;o.querySelectorAll(".tutor-form-select-option").forEach((function(t){if(t.classList.contains("noItem")==true){i=true}}));if(false==i){o.insertAdjacentHTML("beforeend",n);i=true}}else{if(null!==d.querySelector(".noItem")){d.querySelector(".noItem").remove()}}}}}}));var t=document.querySelectorAll(".tutor-js-form-select");t.forEach((function(t){if(t.nextElementSibling){if(t.nextElementSibling.classList.contains("tutor-js-form-select")){t.nextElementSibling.remove()}}}));var o=document.querySelectorAll(".tutor-js-form-select");document.onclick=function(t){r(o)}})();function r(t){var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;if(t){t.forEach((function(t){if(t!==e){t.classList.remove("is-active")}}))}}function n(t,e,r){var n="";Array.from(t).forEach((function(t){n+='\n            <div class="tutor-form-select-option '.concat(e===t.value?"is-active":"",'">\n\t\t\t\t<span tutor-dropdown-item data-key="').concat(tutor_esc_attr(t.value),'" class="tutor-nowrap-ellipsis" title="').concat(tutor_esc_attr(t.text),'">').concat(tutor_esc_html(t.text),"</span>\n            </div>\n            ")}));var o="";if(r){o='\n\t\t\t\t<div class="tutor-form-select-search tutor-pt-8 tutor-px-8">\n\t\t\t\t\t<div class="tutor-form-wrap">\n\t\t\t\t\t\t<span class="tutor-form-icon">\n\t\t\t\t\t\t\t<i class="tutor-icon-search" area-hidden="true"></i>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<input type="search" class="tutor-form-control" placeholder="'.concat(window.wp.i18n.__("Search ...","tutor"),'" />\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t')}var i='\n\t\t\t<div class="tutor-form-control tutor-form-select tutor-js-form-select">\n\t\t\t\t<span class="tutor-form-select-label" tutor-dropdown-label>'.concat(window.wp.i18n.__("Select","tutor"),'</span>\n\t\t\t\t<div class="tutor-form-select-dropdown">\n\t\t\t\t\t').concat(o,'\n\t\t\t\t\t<div class="tutor-form-select-options">\n\t\t\t\t\t\t').concat(n,"\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n        ");return i}};selectSearchField(".tutor-form-select")},79988:()=>{function t(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=e(t))||r&&t&&"number"==typeof t.length){n&&(t=n);var o=0,i=function t(){};return{s:i,n:function e(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function t(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,c=!1;return{s:function e(){n=n.call(t)},n:function t(){var e=n.next();return u=e.done,e},e:function t(e){c=!0,a=e},f:function t(){try{u||null==n["return"]||n["return"]()}finally{if(c)throw a}}}}function e(t,e){if(t){if("string"==typeof t)return r(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}jQuery(document).ready((function(e){var r=wp.i18n,n=r.__,o=r._x,i=r._n,a=r._nx;e("[tutor-instructors]").each((function(){var r=e(this);var o={};var i;var a=document.querySelector(".tutor-ratings-stars i.is-active");var u=0;if(a){u=a.dataset.value}function s(t,i,a){var u=r.find("[tutor-instructors-content]");var c=u.html();var s=r.data();s.current_page=a||1;t?o[t]=i:o={};o.attributes=s;o.action="load_filtered_instructor";u.html('<div class="tutor-spinner-wrap"><span class="tutor-spinner" area-hidden="true"></span></div>');e.ajax({url:window._tutorobject.ajaxurl,data:o,type:"POST",success:function t(e){u.html((e.data||{}).html)},error:function t(){u.html(c);tutor_toast(n("Failed","tutor"),n("Request Error","tutor"),"error")}})}r.on("change",'[tutor-instructors-filter-category] [type="checkbox"]',(function(){var t={};e(this).closest("[tutor-instructors-filter-category]").find("input:checked").each((function(){t[e(this).val()]=e(this).parent().text()}));var r=Object.keys(t);s(e(this).attr("name"),r)})).on("click","[tutor-instructors-filter-rating]",(function(t){var e=t.target.dataset.value;if(e!=u){s("rating_filter",e)}u=e})).on("change","[tutor-instructors-filter-sort]",(function(t){var e=t.target.value;s("short_by",e)})).on("input","[tutor-instructors-filter-search]",(function(){var t=e(this).val();i?window.clearTimeout(i):0;i=window.setTimeout((function(){s("keyword",t);i=null}),500)})).on("click","[data-page_number]",(function(t){t.preventDefault();s(null,null,e(this).data("page_number"))})).on("click","[tutor-instructors-filter-clear]",(function(){var r=e(this).closest("[tutor-instructors-filters]");r.find('input[type="checkbox"]').prop("checked",false);r.find("[tutor-instructors-filter-search]").val("");var n=document.querySelectorAll("[tutor-instructors-filter-rating]");var o=t(n),i;try{for(o.s();!(i=o.n()).done;){var a=i.value;if(a.classList.contains("active")){a.classList.remove("active")}if(a.classList.contains("tutor-icon-star-bold")){a.classList.remove("tutor-icon-star-bold");a.classList.add("tutor-icon-star-line")}}}catch(t){o.e(t)}finally{o.f()}c.innerHTML="";s()}))}));var u=document.querySelectorAll("[tutor-instructors-filter-rating]");var c=document.querySelector("[tutor-instructors-filter-rating-count]");var s=t(u),l;try{for(s.s();!(l=s.n()).done;){var f=l.value;f.onclick=function(e){var r=e.currentTarget;var o=t(u),i;try{for(o.s();!(i=o.n()).done;){var a=i.value;if(a.classList.contains("is-active")){a.classList.remove("is-active")}if(a.classList.contains("tutor-icon-star-bold")){a.classList.remove("tutor-icon-star-bold");a.classList.add("tutor-icon-star-line")}}}catch(t){o.e(t)}finally{o.f()}var s=Number(e.target.dataset.value);var l=n("star","tutor");if(s>1){l=n("stars","tutor")}if(!r.classList.contains("is-active")){r.classList.add("is-active")}if(!r.classList.contains("tutor-icon-star-bold")){r.classList.remove("tutor-icon-star-line");r.classList.add("tutor-icon-star-bold")}c.innerHTML="".concat(s," ").concat(l)}}}catch(t){s.e(t)}finally{s.f()}}))},81040:(t,e,r)=>{var n=r(44510),o=n.get_response_message;var i=function t(e){var r=new RegExp("^(https?:\\/\\/)?"+"((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|"+"((\\d{1,3}\\.){3}\\d{1,3}))"+"(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*"+"(\\?[;&a-z\\d%_.~+=-]*)?"+"(\\#[-a-z\\d_]*)?$","i");return!!r.test(e)};var a=function t(e,r,n){var o=new FileReader;o.addEventListener("load",(function(){var t=new Image;t.addEventListener("load",(function(){var o=t.width,i=t.height;var a=0;var u=0;var c=o;var s=i;if(r.width==r.height){a=o>i?(o-i)/2:0;u=i>o?(i-o)/2:0;c=o>i?i:o;s=i>o?o:i}r.height=r.height||i/o*r.width;var l=r.width>o?o:r.width;var f=r.width>o?i:r.height;var d=document.createElement("canvas");d.width=l;d.height=f;var h=d.getContext("2d");h.drawImage(t,a,u,c,s,0,0,d.width,d.height);d.toBlob((function(t){t.name=e.name;t.lastModified=e.lastModified;var r=new FileReader;r.addEventListener("load",(function(){n(t,r.result)}));r.readAsDataURL(t)}),"image/jpeg")}));t.src=o.result}));o.readAsDataURL(e)};window.jQuery(document).ready((function(t){var e=wp.i18n.__;var r=function r(n){this.dialogue_box=n.find("#tutor_photo_dialogue_box");this.open_dialogue_box=function(t){this.dialogue_box.attr("name",t);this.dialogue_box.trigger("click")};this.upload_selected_image=function(r,n){var o=tutor_get_nonce_data(true);var i=this;i.toggle_loader(r,true);var a=new FormData;a.append("action","tutor_user_photo_upload");a.append("photo_type",r);a.append("photo_file",n,n.name);a.append(o.key,o.value);var u=this;t.ajax({url:window._tutorobject.ajaxurl,data:a,type:"POST",processData:false,contentType:false,error:i.error_alert,success:function t(){var n=u.title_capitalize(r.replace("_"," "));var o=e("Success","tutor");var i=n+" Changed Successfully!";if("Profile Photo"===n){i=e("Profile Photo Changed Successfully!","tutor")}if("Cover Photo"===n){i=e("Cover Photo Changed Successfully!","tutor")}tutor_toast(o,i,"success")},complete:function t(){i.toggle_loader(r,false)}})};this.title_capitalize=function(t){var e=t.split(" ");for(var r=0;r<e.length;r++){e[r]=e[r].charAt(0).toUpperCase()+e[r].slice(1)}return e.join(" ")};this.accept_upload_image=function(e,r){var n=r.currentTarget.files[0]||null;e.update_preview(r.currentTarget.name,n);a(n,{width:1200},(function(t){e.upload_selected_image(r.currentTarget.name,t)}));t(r.currentTarget).val("")};this.delete_image=function(e){var r=this;r.toggle_loader(e,true);t.ajax({url:window._tutorobject.ajaxurl,data:{action:"tutor_user_photo_remove",photo_type:e},type:"POST",error:r.error_alert,complete:function t(){r.toggle_loader(e,false)}})};this.update_preview=function(t,e){var r=n.find(t=="cover_photo"?"#tutor_cover_area":"#tutor_profile_area");if(!e){r.css("background-image","url("+r.data("fallback")+")");this.delete_image(t);return}var o=new FileReader;o.onload=function(t){r.css("background-image","url("+t.target.result+")")};o.readAsDataURL(e)};this.toggle_profile_pic_action=function(t){var e=t===undefined?"toggleClass":t?"addClass":"removeClass";n[e]("pop-up-opened")};this.error_alert=function(){tutor_toast(e("Error","tutor"),e("Maximum file size exceeded!","tutor"),"error")};this.toggle_loader=function(t,e){n.find("#tutor_photo_meta_area .loader-area").css("display",e?"block":"none")};this.initialize=function(){var t=this;this.dialogue_box.change((function(e){t.accept_upload_image(t,e)}));n.find("#tutor_profile_area .tutor_overlay, #tutor_pp_option>div:last-child").click((function(){t.toggle_profile_pic_action()}));n.find(".tutor_cover_uploader").click((function(){t.open_dialogue_box("cover_photo")}));n.find(".tutor_pp_uploader").click((function(){t.open_dialogue_box("profile_photo")}));n.find(".tutor_cover_deleter").click((function(){t.update_preview("cover_photo",null)}));n.find(".tutor_pp_deleter").click((function(){t.update_preview("profile_photo",null)}))}};var n=t("#tutor_profile_cover_photo_editor");n.length>0?new r(n).initialize():0;t(".tutor-profile-settings-save").click((function(r){r.preventDefault();var n=t(this);var i=n.closest("form");var a=i.serializeObject();var u=document.querySelector("[name=phone_number]");if(window.tinyMCE!==undefined){var c=tinyMCE.get("tutor_profile_bio");a.tutor_profile_bio=c.getContent({format:"html"})}if(a.phone_number&&!a.phone_number.match(/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im)){u.classList.add("invalid");tutor_toast(e("Invalid","tutor"),e("Invalid phone number","tutor"),"error");u.focus();return false}else{u.classList.remove("invalid")}a.action="tutor_update_profile";t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:a,beforeSend:function t(){n.addClass("is-loading")},success:function t(r){var t=r.success;if(t){window.tutor_toast(e("Success","tutor"),o(r),"success")}else{window.tutor_toast(e("Error","tutor"),o(r),"error")}},complete:function t(){n.removeClass("is-loading")}})}));t("#user_social_form").submit((function(r){r.preventDefault();var n=t(this).find("button[type=submit]");var i=_tutorobject.ajaxurl;var a=t(this).serializeObject();t.ajax({url:i,type:"POST",data:a,beforeSend:function t(){n.addClass("is-loading")},success:function t(r){var t=r.success;var n=o(r);if(t){window.tutor_toast(e("Success","tutor"),n,"success")}else{window.tutor_toast(e("Error","tutor"),n,"error")}},complete:function t(){n.removeClass("is-loading")}})}))}))},87686:()=>{window.jQuery(document).ready((function(t){var e=wp.i18n.__;t(document).on("click",".tutor-course-wishlist-btn",(function(e){e.preventDefault();var r=t(this);var n=r.attr("data-course-id");t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:{course_id:n,action:"tutor_course_add_to_wishlist"},beforeSend:function t(){r.attr("disabled","disabled").addClass("is-loading")},success:function e(n){if(n.success){if(n.data.status==="added"){r.find("i").addClass("tutor-icon-bookmark-bold").removeClass("tutor-icon-bookmark-line")}else{r.find("i").addClass("tutor-icon-bookmark-line").removeClass("tutor-icon-bookmark-bold")}}else{t(".tutor-login-modal").addClass("tutor-is-active")}},complete:function t(){r.removeAttr("disabled").removeClass("is-loading")}})}))}))},93188:()=>{window.jQuery(document).ready((function(t){if(t.fn.ShareLink){var e=t(".tutor-social-share-wrap");if(e.length){var r=JSON.parse(e.attr("data-social-share-config"));e.find(".tutor_share").ShareLink({title:r.title,text:r.text,image:r.image,class_prefix:"s_",width:640,height:480})}}}))},94004:()=>{window.jQuery(document).ready((function(t){var e=window.wp.i18n.__;t(".tutor-course-retake-button").prop("disabled",false).click((function(r){r.preventDefault();var n=t(this).attr("href");var o=t(this).data("course_id");var i={title:e("Override Previous Progress","tutor"),description:e("Before continue, please decide whether to keep progress or reset.","tutor"),buttons:{reset:{title:e("Reset Data","tutor"),class:"tutor-btn tutor-btn-primary",callback:function r(n){t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:{action:"tutor_reset_course_progress",course_id:o},beforeSend:function t(){n.prop("disabled",true).addClass("is-loading")},success:function t(r){if(r.success){window.location.assign(r.data.redirect_to)}else{alert((r.data||{}).message||e("Something went wrong","tutor"))}},complete:function t(){n.prop("disabled",false).removeClass("is-loading")}})}},keep:{title:e("Keep Data","tutor"),class:"tutor-btn tutor-btn-outline-primary tutor-ml-20",attr:"data-tutor-modal-close",callback:function t(){window.location.assign(n)}}}};new window.tutor_popup(t,"icon-gear").popup(i)}))}));readyState_complete((function(){var t=document.querySelector(".tutor-video-player .loading-spinner");if(null!==t){t.remove()}}))}};var e={};function r(n){var o=e[n];if(o!==undefined){return o.exports}var i=e[n]={exports:{}};t[n](i,i.exports,r);return i.exports}(()=>{r.d=(t,e)=>{for(var n in e){if(r.o(e,n)&&!r.o(t,n)){Object.defineProperty(t,n,{enumerable:true,get:e[n]})}}}})();(()=>{r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})();(()=>{r.r=t=>{if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(t,"__esModule",{value:true})}})();var n={};(()=>{"use strict";function t(e){"@babel/helpers - typeof";return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function t(){return n};var r,n={},o=Object.prototype,i=o.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",l=u.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(r){f=function t(e,r,n){return e[r]=n}}function d(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),u=new A(n||[]);return a(i,"_invoke",{value:q(t,r,u)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=d;var p="suspendedStart",v="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function _(){}var x={};f(x,c,(function(){return this}));var L=Object.getPrototypeOf,E=L&&L(L(C([])));E&&E!==o&&i.call(E,c)&&(x=E);var S=_.prototype=b.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(e,r){function n(o,a,u,c){var s=h(e[o],e,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function t(e,i){function a(){return new r((function(t,r){n(e,i,t,r)}))}return o=o?o.then(a,a):a()}})}function q(t,e,n){var o=p;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:r,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=O(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var s=h(t,e,n);if("normal"===s.type){if(o=n.done?y:v,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function O(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator["return"]&&(e.method="return",e.arg=r,O(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=h(o,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,g;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function C(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(i.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+" is not iterable")}return w.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,l,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},n.awrap=function(t){return{__await:t}},k(j.prototype),f(j.prototype,s,(function(){return this})),n.AsyncIterator=j,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new j(d(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(S),f(S,l,"Generator"),f(S,c,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=C,A.prototype={constructor:A,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(P),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=r)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,o){return c.type="throw",c.arg=e,n.next=t,o&&(n.method="next",n.arg=r),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return o("end");if(u.tryLoc<=this.prev){var s=i.call(u,"catchLoc"),l=i.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return o(u.catchLoc,!0);if(this.prev<u.finallyLoc)return o(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return o(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return o(u.finallyLoc)}}}},abrupt:function t(e,r){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),g},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),g}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;P(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function t(e,n,o){return this.delegate={iterator:C(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=r),g}},n}function n(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var a=t.apply(e,r);function u(t){n(a,o,i,u,c,"next",t)}function c(t){n(a,o,i,u,c,"throw",t)}u(void 0)}))}}function i(t){return a.apply(this,arguments)}function a(){a=o(e().mark((function t(r){var n;return e().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.prev=0;e.next=3;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:r});case 3:n=e.sent;return e.abrupt("return",n);case 7:e.prev=7;e.t0=e["catch"](0);tutor_toast(__("Operation failed","tutor"),e.t0,"error");case 10:case"end":return e.stop()}}),t,null,[[0,7]])})));return a.apply(this,arguments)}function u(t){"@babel/helpers - typeof";return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function t(e,r,n){return e[r]=n}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new A(n||[]);return o(a,"_invoke",{value:q(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",v="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function _(){}var x={};f(x,a,(function(){return this}));var L=Object.getPrototypeOf,E=L&&L(L(C([])));E&&E!==r&&n.call(E,a)&&(x=E);var S=_.prototype=b.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var s=h(t[o],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,c)}))}c(s.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function q(e,r,n){var o=p;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=O(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var s=h(e,r,n);if("normal"===s.type){if(o=n.done?y:v,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function O(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=_,o(S,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},k(j.prototype),f(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(S),f(S,l,"Generator"),f(S,a,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=C,A.prototype={constructor:A,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),g},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),g}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;P(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:C(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),g}},e}function s(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,u,"next",t)}function u(t){s(i,n,o,a,u,"throw",t)}a(void 0)}))}}function f(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=d(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function t(){};return{s:o,n:function e(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();return a=e.done,e},e:function t(e){u=!0,i=e},f:function t(){try{a||null==r["return"]||r["return"]()}finally{if(u)throw i}}}}function d(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}document.addEventListener("DOMContentLoaded",(function(){var t=wp.i18n,e=t.__,r=t._x,n=t._n,o=t._nx;var a=document.getElementById("tutor-common-confirmation-modal");var s=document.getElementById("tutor-common-confirmation-form");var d=document.querySelectorAll(".tutor-filter-select");d.forEach((function(t){t.addEventListener("change",(function(t){var e=t.target.name;var r=t.target.value;if(r.length){window.location=E(e,r)}else{window.location=S(e)}}),{once:true})}));var h=document.querySelectorAll(".tutor-admin-dashboard-filter-form");h.forEach((function(t){t.addEventListener("submit",(function(t){t.preventDefault();var e=new FormData(t.target);var r=Object.fromEntries(e);var n=new URL(window.location.href);var o=n.searchParams;o.set("paged",1);for(var i in r){var a=r[i];if(a){o.set(i,a)}else{o["delete"](i)}}window.location=n}))}));var p=document.getElementById("tutor-backend-filter-course");if(p){p.addEventListener("change",(function(t){window.location=E("course-id",t.target.value)}),{once:true})}var v=document.getElementById("tutor-backend-filter-category");if(v){v.addEventListener("change",(function(t){window.location=E("category",t.target.value)}),{once:true})}var m=document.getElementById("tutor-backend-filter-order");if(m){m.addEventListener("change",(function(t){window.location=E("order",t.target.value)}),{once:true})}var y=document.getElementById("tutor-backend-filter-payment-status");y===null||y===void 0||y.addEventListener("change",(function(t){window.location=E("payment-status",t.target.value)}),{once:true});var g=document.getElementById("tutor-admin-search-filter-form");var b=document.getElementById("tutor-backend-filter-search");if(g){b.addEventListener("search",(function(t){var e=t.currentTarget||{},r=e.value;if(/\S+/.test(r)==false){window.location=E("search","")}}));g.onsubmit=function(t){t.preventDefault();var e=b.value;window.location=E("search",e)}}var w=document.getElementById("tutor-admin-bulk-action-btn");var _=document.querySelector(".tutor-bulk-modal-disabled");if(w){w.onclick=function(){var t=[];var r=document.querySelectorAll(".tutor-bulk-checkbox");var n=f(r),o;try{for(n.s();!(o=n.n()).done;){var i=o.value;if(i.checked){t.push(i.value)}}}catch(t){n.e(t)}finally{n.f()}if(t.length){_.setAttribute("id","tutor-bulk-confirm-popup")}else{tutor_toast(e("Warning","tutor"),e("Nothing was selected for bulk action.","tutor"),"error");if(_.hasAttribute("id")){_.removeAttribute("id")}}}}var x=document.getElementById("tutor-admin-bulk-action-form");if(x){x.onsubmit=function(){var t=l(c().mark((function t(r){var n,o,i,a,u,s,l,d,h,p,v,m;return c().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:r.preventDefault();r.stopPropagation();n=new FormData(x);o=[];i=document.querySelectorAll(".tutor-bulk-checkbox");a=f(i);try{for(a.s();!(u=a.n()).done;){s=u.value;if(s.checked){o.push(s.value)}}}catch(t){a.e(t)}finally{a.f()}if(o.length){c.next=10;break}alert(e("Select checkbox for action","tutor"));return c.abrupt("return");case 10:n.set("bulk-ids",o);n.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);c.prev=12;l=document.querySelector("#tutor-confirm-bulk-action[data-tutor-modal-submit]");l.classList.add("is-loading");c.next=17;return fetch(window._tutorobject.ajaxurl,{method:"POST",body:n});case 17:d=c.sent;l.classList.remove("is-loading");if(!d.ok){c.next=24;break}c.next=22;return d.json();case 22:h=c.sent;if(h.success||200===(h===null||h===void 0?void 0:h.status_code)){location.reload()}else{p=h.data||{},v=p.message,m=v===void 0?e("Something went wrong, please try again ","tutor"):v;tutor_toast(e("Failed","tutor"),m,"error")}case 24:c.next=29;break;case 26:c.prev=26;c.t0=c["catch"](12);console.log(c.t0);case 29:case"end":return c.stop()}}),t,null,[[12,26]])})));return function(e){return t.apply(this,arguments)}}()}var L=document.getElementById("tutor-confirm-bulk-action");if(L){L.onclick=function(){var t=document.createElement("input");t.type="submit";x.appendChild(t);t.click();t.remove()}}function E(t,e){var r=new URL(window.location.href);var n=r.searchParams;n.set(t,e);n.set("paged",1);return r}function S(t){var e=new URL(window.location.href);var r=e.searchParams;r["delete"](t);return e}var k=document.querySelector("#tutor-bulk-checkbox-all");if(k){k.addEventListener("click",(function(){var t=document.querySelectorAll(".tutor-bulk-checkbox");t.forEach((function(t){if(k.checked){t.checked=true}else{t.checked=false}}))}))}var j=document.querySelectorAll(".tutor-admin-course-delete");var q=f(j),O;try{for(q.s();!(O=q.n()).done;){var T=O.value;T.onclick=function(t){var e=t.currentTarget.dataset.id;if(s){s.elements.action.value="tutor_course_delete";s.elements.id.value=e}}}}catch(t){q.e(t)}finally{q.f()}var P=document.querySelectorAll(".tutor-delete-permanently");var A=f(P),C;try{for(A.s();!(C=A.n()).done;){var z=C.value;z.onclick=function(t){var e=t.currentTarget.dataset.id;var r=t.currentTarget.dataset.action;if(s){s.elements.action.value=r;s.elements.id.value=e}}}}catch(t){A.e(t)}finally{A.f()}if(s){s.onsubmit=function(){var t=l(c().mark((function t(r){var n,o,l,f;return c().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:r.preventDefault();n=new FormData(s);o=s.querySelector("[data-tutor-modal-submit]");o.classList.add("is-loading");c.next=6;return i(n);case 6:l=c.sent;if(a.classList.contains("tutor-is-active")){a.classList.remove("tutor-is-active")}if(!l.ok){c.next=14;break}c.next=11;return l.json();case 11:f=c.sent;o.classList.remove("is-loading");if(f){if(u(f)==="object"&&f.success){tutor_toast(e("Delete","tutor"),f.data,"success");location.reload(true)}else if(u(f)==="object"&&f.success===false){tutor_toast(e("Failed","tutor"),f.data,"error")}else{tutor_toast(e("Delete","tutor"),e("Successfully deleted ","tutor"),"success");location.reload()}}else{tutor_toast(e("Failed","tutor"),e("Delete failed ","tutor"),"error")}case 14:case"end":return c.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}}));var p=r(51019);function v(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++){r[n-1]=arguments[n]}return t.replace(/%s/g,(function(){return r.shift()}))}const m=v;var y=r(79211);function g(t){"@babel/helpers - typeof";return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function b(t,e){return E(t)||L(t,e)||_(t,e)||w()}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t,e){if(t){if("string"==typeof t)return x(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?x(t,e):void 0}}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(s)throw o}}return u}}function E(t){if(Array.isArray(t))return t}function S(t,e,r){return(e=k(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function k(t){var e=j(t,"string");return"symbol"==g(e)?e:e+""}function j(t,e){if("object"!=g(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}if(!window.tutor_get_nonce_data){window.tutor_get_nonce_data=function(t){var e=window._tutorobject||{};var r=e.nonce_key||"";var n=e[r]||"";if(t){return{key:r,value:n}}return S({},r,n)}}function q(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];var e=new FormData;t.forEach((function(t){for(var r=0,n=Object.entries(t);r<n.length;r++){var o=b(n[r],2),i=o[0],a=o[1];e.set(i,a)}}));e.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);return e}const O=q;function T(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */T=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new P(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(A([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==G(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(G(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,P.prototype={constructor:P,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:A(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function P(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function A(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){P(i,n,o,a,u,"next",t)}function u(t){P(i,n,o,a,u,"throw",t)}a(void 0)}))}}function C(t){return D(t)||N(t)||I(t)||z()}function z(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function I(t,e){if(t){if("string"==typeof t)return F(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?F(t,e):void 0}}function N(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function D(t){if(Array.isArray(t))return F(t)}function F(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function G(t){"@babel/helpers - typeof";return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},G(t)}var M=["keyword","course_order","tutor-course-filter-type","tutor-course-filter-level","tutor-course-filter-tag","tutor-course-filter-category","tutor-course-filter-price","course_filter","supported_filters","current_page","action"];var B=function t(e){var r=new URL(window.location.origin+window.location.pathname);var n=R();for(var o in n){if(M.indexOf(o)==-1){r.searchParams.append(o,n[o])}}var i=function t(){var n=Array.isArray(e[a]);var o=n?a+"[]":a;var i=n?e[a]:[e[a]];i.forEach((function(t){if(G(t)!="object"){r.searchParams.append(o,t)}}))};for(var a in e){i()}window.history.pushState({},"",r)};var R=function t(){var e={};new URL(window.location).searchParams.forEach((function(t,r){if(r.slice(-2)=="[]"){var n=r.slice(0,-2);!e[n]?e[n]=[]:0;!Array.isArray(e[n])?e[n]=[e[n]]:0;e[n].push(t)}else{e[r]=t}}));return e};var Q=function t(e){var r=R();e.find('[type="checkbox"]').prop("checked",false);e.find('[type="text"], select').val("");var n=function t(){var n=r[o];var i=e.find('[name="'+o+'"]');if(i.eq(0).attr("type")=="checkbox"){var a=!Array.isArray(n)?[n]:n;i.each((function(){var t=a.indexOf(window.jQuery(this).attr("value"))>-1;window.jQuery(this).prop("checked",t)}))}else{i.val(n)}};for(var o in r){n()}};window.jQuery(document).ready((function(t){var e=window.wp.i18n.__;var r=t("[tutor-course-filter] form");if(!r.length){return}var n=t("[tutor-course-list-container]");var o=t(".tutor-courses-wrap").data("tutor_courses_meta")||{};var a={};r.on("submit",(function(t){t.preventDefault()})).find("input,select").on("change",(function(t){u()}));Q(r);window.addEventListener("popstate",(function(){Q(r);u(false,true)}));var u=function i(){var u=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var c=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var s=R();var l=Object.assign(r.serializeObject(),a,o);l.current_page=c&&s.current_page?s.current_page:1;l.action="tutor_course_filter_ajax";if(u){B(l)}n.html('<div class="tutor-spinner-wrap"><span class="tutor-spinner" area-hidden="true"></span></div>');r.find("[action-tutor-clear-filter]").closest(".tutor-widget-course-filter").removeClass("tutor-d-none");if(!("category"in l.supported_filters)){var f="tutor-course-filter-category";var d=Object.keys(s).filter((function(t){return t.includes(f)}));if(d.length>0){var h=[];d.forEach((function(t){h.push(s[t])}));l["tutor-course-filter-category"]=C(new Set(h))}else{l["tutor-course-filter-category"]=JSON.parse(t("#course_filter_categories").val())}}var p="tutor-course-filter-exclude-ids";var v=Object.keys(s).filter((function(t){return t.includes(p)}));var m=[];if(v.length>0){v.forEach((function(t){m.push(s[t])}));l["tutor-course-filter-exclude-ids"]=C(new Set(m))}else{if(t("#course_filter_exclude_ids").length){l["tutor-course-filter-exclude-ids"]=JSON.parse(t("#course_filter_exclude_ids").val())}}var y="tutor-course-filter-post-ids";var g=Object.keys(s).filter((function(t){return t.includes(y)}));var b=[];if(g.length>0){g.forEach((function(t){b.push(s[t])}));l["tutor-course-filter-post-ids"]=C(new Set(b))}else{if(t("#course_filter_post_ids").length){l["tutor-course-filter-post-ids"]=JSON.parse(t("#course_filter_post_ids").val())}}t.ajax({url:window._tutorobject.ajaxurl,type:"POST",data:l,success:function t(r){if(!r.success){n.html(e("Could not load courses","tutor"));return}n.html(r.data.html).find("nav").css("display","flex");window.dispatchEvent(new Event(_tutorobject.content_change_event))}})};t("[tutor-toggle-course-filter]").on("click",(function(e){e.preventDefault();t("body").toggleClass("tutor-course-filter-open");if(t(".tutor-course-filter-backdrop").length==0){t("body").append(t('<div class="tutor-course-filter-backdrop" area-hidden="true"></div>').hide().fadeIn(150))}}));t("[tutor-hide-course-filter]").on("click",(function(e){e.preventDefault();t("body").removeClass("tutor-course-filter-open")}));var c=document.querySelectorAll(".tutor-course-list-enroll");c.forEach((function(t){t.onclick=function(){var t=A(T().mark((function t(r){var n,o,a,u,c,s,l,f;return T().wrap((function t(d){while(1)switch(d.prev=d.next){case 0:r.preventDefault();n=e("Something went wrong, please try again!","tutor");o=r.target;a=[{action:"tutor_course_enrollment"},{course_id:o.dataset.courseId}];if(o.dataset.subscriptionEnrollment){a.push({tutor_subscription_enrollment:true})}u=O(a);o.classList.add("is-loading");o.setAttribute("disabled",true);d.next=10;return i(u);case 10:c=d.sent;if(!c.ok){d.next=19;break}d.next=14;return c.json();case 14:s=d.sent;l=s.success,f=s.data;if(l){tutor_toast(e("Success","tutor"),f,"success");window.location.href=o.href}else{tutor_toast(e("Failed","tutor"),f?f:n,"error")}d.next=20;break;case 19:tutor_toast(e("Error","tutor"),e(n),"error");case 20:o.classList.remove("is-loading");o.removeAttribute("disabled");case 22:case"end":return d.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))}));var U=r(24081);var H=r(93188);function Y(t){"@babel/helpers - typeof";return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}function W(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */W=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Y(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Y(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function J(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function $(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){J(i,n,o,a,u,"next",t)}function u(t){J(i,n,o,a,u,"throw",t)}a(void 0)}))}}function X(t){return tt(t)||Z(t)||K(t)||V()}function V(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function K(t,e){if(t){if("string"==typeof t)return et(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?et(t,e):void 0}}function Z(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function tt(t){if(Array.isArray(t))return et(t)}function et(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}jQuery(document).ready((function(t){t(".tutor-sortable-list").sortable()}));document.addEventListener("DOMContentLoaded",(function(t){var e=wp.i18n,r=e.__,n=e._x,o=e._n,a=e._nx;var u=document.querySelector(".tutor-lesson-sidebar.tutor-desktop-sidebar");var c=document.querySelector(".tutor-sidebar-toggle-anchor");if(u&&c){c.addEventListener("click",(function(){if(getComputedStyle(u).flex==="0 0 400px"){u.style.flex="0 0 0px";u.style.display="none"}else{u.style.display="block";u.style.flex="0 0 400px"}}))}var s=document.querySelector(".tutor-sidebar-tabs-content");if(s){var l=s.getBoundingClientRect().top;s.style.height="calc(100vh - ".concat(l,"px)")}var f=function t(e){var r=document.querySelector(".tutor-desktop-sidebar-area");if(null!==r&&r.children.length<2){return}e.forEach((function(t){t.addEventListener("click",(function(t){var e=t.currentTarget.parentNode.nextElementSibling;n(e);t.currentTarget.classList.add("active");var r=t.currentTarget.getAttribute("data-sidebar-tab");var o=e.querySelector("#"+r);o.classList.add("active");var i=document.querySelector(".tutor-lessons-tab-area");var a=i.offsetHeight;if(r=="sidebar-qna-tab-content"){o.style.height="calc(100% - ".concat(a,"px)")}}))}));var n=function t(r){for(var n=0;n<e.length;n++){e[n].classList.remove("active")}var o=r.querySelectorAll(".tutor-lesson-sidebar-tab-item");for(var i=0;i<o.length;i++){o[i].classList.remove("active")}}};var d=document.querySelectorAll(".tutor-desktop-sidebar-area .tutor-sidebar-tab-item");var h=document.querySelectorAll(".tutor-mobile-sidebar-area .tutor-sidebar-tab-item");if(d){f(d)}if(h){f(h)}var p=document.querySelectorAll(".tutor-comment-textarea textarea");if(p){p.forEach((function(t){t.addEventListener("focus",(function(){t.parentElement.classList.add("is-focused")}));t.addEventListener("blur",(function(){t.parentElement.classList.remove("is-focused")}))}))}function v(){var t=document.querySelectorAll(".tutor-comments-list.tutor-parent-comment");var e=document.querySelector(".tutor-comment-box.tutor-reply-box");if(t){X(t).forEach((function(t){var r=t.querySelectorAll(".tutor-comments-list.tutor-child-comment");var n=t.querySelector(".tutor-comment-line");var o=r.length;if(r[o-1]){var i=r[o-1].clientHeight;var a=i+e.clientHeight+20-25+50;n.style.setProperty("height","calc(100% - ".concat(a,"px)"))}}))}}v();window.addEventListener(_tutorobject.content_change_event,v);var m=document.querySelectorAll(".tutor-draggable > div");var y=document.querySelectorAll(".tutor-dropzone");m.forEach((function(t){t.addEventListener("dragstart",E);t.addEventListener("dragend",S)}));m.forEach((function(t){["touchstart","touchmove","touchend"].forEach((function(e){t.addEventListener(e,w)}))}));y.forEach((function(t){t.addEventListener("dragover",k);t.addEventListener("dragenter",j);t.addEventListener("dragleave",q);t.addEventListener("drop",O)}));var g=null;var b=0;function w(t){t.preventDefault();var e=t.type;if(e==="touchstart"){this.classList.add("tutor-dragging");x()}else if(e==="touchmove"){var r=t.target.closest(".tutor-dragging");var n=document.querySelector(".tutor-drag-copy");if(r){var o=r.getBoundingClientRect();var i=t.touches[0].clientY;var a=t.touches[0].clientX;var u=100;var c=40;var s=window.innerHeight;var l=s-i;var f=i;b=0;if(l<u){b=L(u,l,c)}else if(f<u){b=-L(u,f,c)}if(!n){n=r.cloneNode(true);n.classList.add("tutor-drag-copy");r.parentNode.appendChild(n)}n.style.position="fixed";n.style.left=a-n.clientWidth/2+"px";n.style.top=i-n.clientHeight/2+"px";n.style.zIndex="9999";n.style.opacity="0.5";n.style.width=o.width+"px";n.style.height=o.height+"px"}}else if(e==="touchend"){var d=document.querySelector(".tutor-drag-copy");if(d){d.remove();var h=typeof t.originalEvent==="undefined"?t:t.originalEvent;var p=h.touches[0]||h.changedTouches[0];var v=[p.clientX,p.clientY],m=v[0],y=v[1];var g=document.elementFromPoint(m,y);if(g.classList.contains("tutor-dropzone")||g.closest(".tutor-dropzone")){if(!g.classList.contains("tutor-dropzone")){g=g.closest(".tutor-dropzone")}var w=d.querySelector("input");var E=w.dataset.name;var S=document.createElement("input");S.type="text";S.setAttribute("value",w.value);S.setAttribute("name",E);var k=g.querySelector("input");if(k){k.remove()}g.appendChild(S);var j=d.querySelector(".tutor-dragging-text-conent").textContent;g.querySelector(".tutor-dragging-text-conent").textContent=j;g.querySelector(".tutor-dragging-text-conent").classList.add("tutor-color-black");this.classList.remove("tutor-dragging")}}_()}}function _(){clearInterval(g);g=null}function x(){if(!g){g=setInterval((function(){window.scrollBy(0,b)}),60)}}function L(t,e,r){var n=(t-e)/t*r;return Math.max(n,0)}function E(){this.classList.add("tutor-dragging")}function S(){this.classList.remove("tutor-dragging")}function k(t){this.classList.add("tutor-drop-over");t.preventDefault()}function j(){}function q(){this.classList.remove("tutor-drop-over")}function O(){var t=document.querySelector(".tutor-quiz-border-box.tutor-dragging");if(this.querySelector("input")){this.querySelector("input").remove()}var e=t.querySelector("input");var r=e.dataset.name;var n=document.createElement("input");n.type="text";n.setAttribute("value",e.value);n.setAttribute("name",r);this.appendChild(n);var o=t.querySelector(".tutor-dragging-text-conent").textContent;this.querySelector(".tutor-dragging-text-conent").textContent=o;this.querySelector(".tutor-dragging-text-conent").classList.add("tutor-color-black");this.classList.remove("tutor-drop-over")}var T=document.getElementById("tutor-assignment-file-upload");if(T){T.addEventListener("change",P)}function P(){var t;var e=X(T.files).reduce((function(t,e){return t+e.size}),0);var n=parseInt((t=document.querySelector('input[name="tutor_assignment_upload_limit"]'))===null||t===void 0?void 0:t.value)||0;var o="";var i=window._tutorobject.assignment_max_file_allowed;var a=document.querySelectorAll("#tutor-student-assignment-edit-file-preview .tutor-instructor-card").length;var u=i-a;if(T.files.length>u){T.value=null;tutor_toast(r("Warning","tutor"),r("Max ".concat(i," file allowed to upload"),"tutor"),"error");return}if(e>n){T.value=null;tutor_toast(r("Warning","tutor"),r("File size exceeds maximum limit ".concat(Math.floor(n/1e6)," MB."),"tutor"),"error");return}if("files"in T){if(T&&T.files.length==0){o="Select one or more files."}else{if(T.files.length>u){tutor_toast(r("Warning","tutor"),r("Max ".concat(i," file allowed to upload"),"tutor"),"error")}var c="";var s=document.querySelector(".tutor-asisgnment-upload-file-preview");var l=document.getElementById("tutor-student-assignment-edit-file-preview");for(var f=0;f<u;f++){var d=T.files[f];if(!d){continue}var h=l?"tutor-col-sm-5 tutor-py-16 tutor-mr-16":"";c+='<div class="tutor-instructor-card '.concat(h,'">\n                                    <div class="tutor-icard-content">\n                                        <div class="tutor-fs-6 tutor-color-secondary">\n                                            ').concat(d.name,'\n                                        </div>\n                                        <div class="tutor-fs-7">Size: ').concat(d.size,'</div>\n                                    </div>\n                                    <div onclick="(() => {\n\t\t\t\t\t\t\t\t\t\tthis.closest(\'.tutor-instructor-card\').remove();\n\t\t\t\t\t\t\t\t\t})()" class="tutor-attachment-file-close tutor-iconic-btn tutor-iconic-btn-outline flex-center">\n                                        <span class="tutor-icon-times"></span>\n                                    </div>\n                                </div>')}if(s){s.innerHTML=c}if(l){l.insertAdjacentHTML("beforeend",c)}}}}var A=document.querySelectorAll(".tutor-attachment-file-close a");A.forEach((function(t){t.onclick=function(){var t=$(W().mark((function t(e){var n,o,a,u,c,s,l;return W().wrap((function t(f){while(1)switch(f.prev=f.next){case 0:e.preventDefault();n=e.currentTarget;o=n.dataset.name;a=n.dataset.id;u=new FormData;u.set("action","tutor_remove_assignment_attachment");u.set("assignment_comment_id",a);u.set("file_name",o);u.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);c=n.querySelector("span");e.target.classList.add("is-loading");f.next=13;return i(u);case 13:s=f.sent;if(!s.ok){f.next=21;break}f.next=17;return s.json();case 17:l=f.sent;if(!l){tutor_toast(r("Warning","tutor"),r("Attachment remove failed","tutor"),"error")}else{n.closest(".tutor-instructor-card").remove()}f.next=23;break;case 21:alert(s.statusText);e.target.classList.remove("is-loading");case 23:case"end":return f.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))}));var rt=r(48159);var nt=r(31162);var ot=r(87686);var it=r(64901);window.jQuery(document).ready((function(t){t(document).on("click",".tutor-course-entry-box-login button, .tutor-course-entry-box-login a, .tutor-open-login-modal",(function(e){e.preventDefault();var r=t(this).data("login_url")||t(this).closest(".tutor-course-entry-box-login").data("login_url");if(r){window.location.assign(r)}else{t(".tutor-login-modal").addClass("tutor-is-active")}}));var e=document.querySelector(".tutor-password-protected-course");if(e){var r=document.querySelector("body");r.style.overflow="hidden";var n=e.querySelector('input[type="password"]');var o=e.querySelector('input[type="checkbox"]');o.addEventListener("change",(function(){if(o.checked){n.type="text"}else{n.type="password"}}))}function i(){var t=document.querySelectorAll(".tutor-utc-date-time");if(t.length>0&&wp.date){var e=wp.date.getSettings();var r=e.formats.date;var n=e.formats.time;var o="".concat(r,", ").concat(n);t.forEach((function(t){try{var e=t.textContent.trim();var r=new Date("".concat(e," UTC"));if(!isNaN(r)){t.textContent=wp.date.dateI18n(o,r,Intl.DateTimeFormat().resolvedOptions().timeZone)}else{console.warn('Invalid UTC date: "'.concat(e,'"'))}}catch(t){console.log(t)}}))}}i();window.addEventListener("tutor_content_changed_event",(function(){i()}))}));var at=r(56093);var ut=r(32924);var ct=r(81040);var st=r(37942);function lt(t){"@babel/helpers - typeof";return lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},lt(t)}function ft(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ft=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==lt(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(lt(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function dt(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function ht(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){dt(i,n,o,a,u,"next",t)}function u(t){dt(i,n,o,a,u,"throw",t)}a(void 0)}))}}document.addEventListener("DOMContentLoaded",(function(){var t=wp.i18n.__;var e=t("Something went wrong, please try again","tutor");var r=document.querySelector("#user_billing_form");if(r){var n=r.querySelector('button[type="submit"]');r.addEventListener("submit",function(){var r=ht(ft().mark((function r(o){var a,u,c,s,l,f;return ft().wrap((function r(d){while(1)switch(d.prev=d.next){case 0:o.preventDefault();a=new FormData(o.target);d.prev=2;n.setAttribute("disabled","disabled");n.classList.add("is-loading");d.next=7;return i(a);case 7:u=d.sent;d.next=10;return u.json();case 10:c=d.sent;s=c.status_code;l=c.message;f=l===void 0?e:l;if(s===200){tutor_toast(t("Success","tutor"),f,"success")}else{tutor_toast(t("Failed","tutor"),f,"error")}d.next=20;break;case 17:d.prev=17;d.t0=d["catch"](2);tutor_toast(t("Failed","tutor"),e,"error");case 20:d.prev=20;n.removeAttribute("disabled");n.classList.remove("is-loading");return d.finish(20);case 24:case"end":return d.stop()}}),r,null,[[2,17,20,24]])})));return function(t){return r.apply(this,arguments)}}())}}));var pt=r(51379);var vt=r(23880);function mt(t){"@babel/helpers - typeof";return mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mt(t)}function yt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */yt=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==mt(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(mt(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function gt(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function bt(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){gt(i,n,o,a,u,"next",t)}function u(t){gt(i,n,o,a,u,"throw",t)}a(void 0)}))}}function wt(){return _t.apply(this,arguments)}function _t(){_t=bt(yt().mark((function t(){var e;return yt().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;r.next=3;return fetch("".concat(_tutorobject.tutor_url,"/assets/json/countries.json"));case 3:e=r.sent;if(e.ok){r.next=6;break}throw new Error("Failed to fetch countries: ".concat(e.status," ").concat(e.statusText));case 6:r.next=8;return e.json();case 8:return r.abrupt("return",r.sent);case 11:r.prev=11;r.t0=r["catch"](0);console.error("Error fetching countries:",r.t0);return r.abrupt("return",[]);case 15:case"end":return r.stop()}}),t,null,[[0,11]])})));return _t.apply(this,arguments)}function xt(t){"@babel/helpers - typeof";return xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xt(t)}function Lt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Lt=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==xt(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(xt(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function Et(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function St(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Et(i,n,o,a,u,"next",t)}function u(t){Et(i,n,o,a,u,"throw",t)}a(void 0)}))}}document.addEventListener("DOMContentLoaded",St(Lt().mark((function t(){var e,r,n;return Lt().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:e=wp.i18n.__;r=document.querySelector("[name=billing_country]");if(!r){o.next=7;break}o.next=5;return wt();case 5:n=o.sent;r.addEventListener("change",(function(t){var r;var o=t.target.value;var i=(r=n.find((function(t){return t.name===o})))===null||r===void 0?void 0:r.states;var a=document.querySelector("[name=billing_state]");if(i&&i.length>0){a.innerHTML="";i.forEach((function(t){var e=document.createElement("option");e.value=t.name;e.textContent=t.name;a.appendChild(e)}))}else{a.innerHTML='<option value="">'.concat(e("N/A","tutor"),"</option>")}}));case 7:case"end":return o.stop()}}),t)}))));function kt(t){"@babel/helpers - typeof";return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kt(t)}function jt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */jt=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==kt(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(kt(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function qt(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Ot(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){qt(i,n,o,a,u,"next",t)}function u(t){qt(i,n,o,a,u,"throw",t)}a(void 0)}))}}document.addEventListener("DOMContentLoaded",(function(){var t=wp.i18n.__;var e=t("Something went wrong, please try again","tutor");document.addEventListener("click",function(){var r=Ot(jt().mark((function r(n){var o,a,u,c,s,l,f,d,h,p,v,m;return jt().wrap((function r(y){while(1)switch(y.prev=y.next){case 0:o=n.target.closest(".tutor-native-add-to-cart");if(!o){y.next=27;break}a=O([{action:"tutor_add_course_to_cart",course_id:o.dataset.courseId}]);u=document.body.classList.contains("single-courses")||document.body.classList.contains("single-course-bundle");y.prev=4;o.setAttribute("disabled","disabled");o.classList.add("is-loading");y.next=9;return i(a);case 9:c=y.sent;y.next=12;return c.json();case 12:s=y.sent;l=s.status_code;f=s.data;d=s.message;h=d===void 0?e:d;if(l===201){tutor_toast(t("Success","tutor"),h,"success");v='<a data-cy="tutor-native-view-cart" href="'.concat((p=f===null||f===void 0?void 0:f.cart_page_url)!==null&&p!==void 0?p:"#",'" class="tutor-btn tutor-btn-outline-primary ').concat(u?"tutor-btn-lg tutor-btn-block":"tutor-btn-md"," ").concat(!(f!==null&&f!==void 0&&f.cart_page_url)?"tutor-cart-page-not-configured":"",'">').concat(t("View Cart","tutor"),"</a>");o.parentElement.innerHTML=v;m=new CustomEvent("tutorAddToCartEvent",{detail:{cart_count:f===null||f===void 0?void 0:f.cart_count}});document.dispatchEvent(m)}else{tutor_toast(t("Failed","tutor"),h,"error")}y.next=23;break;case 20:y.prev=20;y.t0=y["catch"](4);tutor_toast(t("Failed","tutor"),e,"error");case 23:y.prev=23;o.removeAttribute("disabled");o.classList.remove("is-loading");return y.finish(23);case 27:case"end":return y.stop()}}),r,null,[[4,20,23,27]])})));return function(t){return r.apply(this,arguments)}}());var r=document.querySelector(".tutor-cart-page");if(r){document.addEventListener("click",function(){var r=Ot(jt().mark((function r(n){var o,a,u,c,s,l,f,d,h;return jt().wrap((function r(p){while(1)switch(p.prev=p.next){case 0:o=n.target.closest(".tutor-cart-remove-button");if(!o){p.next=26;break}a=O([{action:"tutor_delete_course_from_cart",course_id:o.dataset.courseId}]);p.prev=3;o.setAttribute("disabled","disabled");o.classList.add("is-loading");p.next=8;return i(a);case 8:u=p.sent;p.next=11;return u.json();case 11:c=p.sent;s=c.status_code;l=c.data;f=c.message;d=f===void 0?e:f;if(s===200){document.querySelector(".tutor-cart-page-wrapper").parentElement.innerHTML=l===null||l===void 0?void 0:l.cart_template;tutor_toast(t("Success","tutor"),d,"success");h=new CustomEvent("tutorRemoveCartEvent",{detail:{cart_count:l===null||l===void 0?void 0:l.cart_count}});document.dispatchEvent(h)}else{tutor_toast(t("Failed","tutor"),d,"error")}p.next=22;break;case 19:p.prev=19;p.t0=p["catch"](3);tutor_toast(t("Failed","tutor"),e,"error");case 22:p.prev=22;o.removeAttribute("disabled");o.classList.remove("is-loading");return p.finish(22);case 26:case"end":return p.stop()}}),r,null,[[3,19,22,26]])})));return function(t){return r.apply(this,arguments)}}())}document.addEventListener("click",(function(e){if(e.target.classList.contains("tutor-cart-page-not-configured")){e.preventDefault();tutor_toast(t("Error!","tutor"),t("Cart page is not configured.","tutor"),"error")}}));document.addEventListener("click",(function(e){if(e.target.classList.contains("tutor-checkout-page-not-configured")){e.preventDefault();tutor_toast(t("Error!","tutor"),t("Checkout page is not configured.","tutor"),"error")}}))}));function Tt(t){"@babel/helpers - typeof";return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tt(t)}function Pt(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Pt=function t(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function t(e,r,n){return e[r]=n}}function l(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:k(t,r,u)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};s(w,a,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==r&&n.call(x,a)&&(w=x);var L=b.prototype=y.prototype=Object.create(w);function E(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=f(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==Tt(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function t(n,o){function a(){return new e((function(t,e){r(n,o,t,e)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var o=d;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=j(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?v:h,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator["return"]&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Tt(e)+" is not iterable")}return g.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},E(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(L),s(L,c,"Generator"),s(L,a,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function e(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var o in this)"t"===o.charAt(0)&&n.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function e(r){if(this.done)throw r;var o=this;function i(e,n){return c.type="throw",c.arg=r,o.next=e,n&&(o.method="next",o.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],c=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var s=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(s&&l){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function t(e,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function e(r,n,o){return this.delegate={iterator:P(r),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),m}},e}function At(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Ct(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){At(i,n,o,a,u,"next",t)}function u(t){At(i,n,o,a,u,"throw",t)}a(void 0)}))}}document.addEventListener("DOMContentLoaded",(function(){var t=wp.i18n.__;var e=t("Something went wrong, please try again","tutor");var r=document.querySelector(".tutor-checkout-page");if(r){var n=function t(){var e;var r=(e=document.querySelector("#checkout_data"))===null||e===void 0?void 0:e.value;return JSON.parse(r)};var o=function t(e){var r=["paddle"];return!r.includes(e.toLowerCase())};var a=function t(e){var r=document.querySelector(".tutor-checkout-tax-amount");var i=document.querySelector(".tutor-checkout-incl-tax-label");var a=document.querySelector(".tutor-checkout-grand-total");var u=n();if(o(e)){a.innerHTML=u.formatted_total_price;i===null||i===void 0||i.classList.remove("tutor-d-none");r===null||r===void 0||r.classList.remove("tutor-d-none")}else{a.innerHTML=u.formatted_total_price_without_tax;i===null||i===void 0||i.classList.add("tutor-d-none");r===null||r===void 0||r.classList.add("tutor-d-none")}};var u=function(){var t=Ct(Pt().mark((function t(e){var r,n,o,a,u,c,s,l,f,d=arguments;return Pt().wrap((function t(h){while(1)switch(h.prev=h.next){case 0:r=d.length>1&&d[1]!==undefined?d[1]:null;n=d.length>2&&d[2]!==undefined?d[2]:null;o=new URL(window.location.href);a=o.searchParams.get("plan");u=o.searchParams.get("course_id");c=new FormData;c.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);c.set("action","tutor_get_checkout_html");c.set("coupon_code",e);if(r){c.set("billing_country",r)}if(n){c.set("billing_state",n)}if(a){c.set("plan",a)}if(u){c.set("course_id",u)}h.next=15;return i(c);case 15:s=h.sent;h.next=18;return s.json();case 18:l=h.sent;f=document.querySelector("[tutor-checkout-details]");if(f){f.innerHTML=l.data}case 21:case"end":return h.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();var c=function(){var t=Ct(Pt().mark((function t(e){var r,n;return Pt().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:e.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);e.set("action","tutor_save_billing_info");o.next=4;return i(e);case 4:r=o.sent;o.next=7;return r.json();case 7:n=o.sent;return o.abrupt("return",n);case 9:case"end":return o.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();var s=function t(){var e=n();if(!e.payment_method_required){return}if(d.length===1){d[0].classList.add("active");d[0].querySelector("input[name=payment_method]").checked=true;h.value=d[0].dataset.paymentType;a(d[0].firstElementChild.value);var r=d[0].dataset.paymentInstruction;if(r){document.querySelector(".tutor-payment-instructions").classList.remove("tutor-d-none");document.querySelector(".tutor-payment-instructions").innerHTML=atob(r)}}};var l=document.querySelector(".tutor-payment-method-wrapper");var f=document.querySelector("#tutor-checkout-pay-now-button");var d=document.querySelectorAll(".tutor-checkout-payment-item");var h=document.querySelector("input[name=payment_type]");document.addEventListener("click",function(){var r=Ct(Pt().mark((function r(o){var c,d,p,v,m,y,g,b,w,_,x,L,E,S,k,j,q,O,T,P,A,C,z,I;return Pt().wrap((function r(N){while(1)switch(N.prev=N.next){case 0:if(o.target.closest(".tutor-checkout-payment-options")){c=document.querySelector(".tutor-checkout-payment-options");d=c.querySelectorAll("label");d.forEach((function(t){return t.classList.remove("active")}));p=o.target.closest("label");p.classList.add("active");h.value=p.dataset.paymentType;v=p.firstElementChild.value;a(v);m=p.dataset.paymentInstruction;if(m){document.querySelector(".tutor-payment-instructions").classList.remove("tutor-d-none");document.querySelector(".tutor-payment-instructions").innerHTML=atob(m)}else{document.querySelector(".tutor-payment-instructions").classList.add("tutor-d-none")}}if(o.target.closest("#tutor-toggle-coupon-button")){y=document.querySelector(".tutor-apply-coupon-form");g=y===null||y===void 0?void 0:y.querySelector("input");if(y.classList.contains("tutor-d-none")){y.classList.remove("tutor-d-none");g.focus()}else{y.classList.add("tutor-d-none")}}if(!o.target.closest("#tutor-apply-coupon-button")){N.next=46;break}w=new URL(window.location.href);_=w.searchParams.get("plan");x=(b=document.querySelector(".tutor-apply-coupon-form input"))===null||b===void 0?void 0:b.value;L=document.querySelector(".tutor-apply-coupon-form button");if(!(x.length===0)){N.next=10;break}tutor_toast(t("Failed","tutor"),t("Please add a coupon code.","tutor"),"error");return N.abrupt("return");case 10:E=new FormData;E.set(window.tutor_get_nonce_data(true).key,window.tutor_get_nonce_data(true).value);E.set("action","tutor_apply_coupon");E.set("coupon_code",x);E.set("object_ids",L.dataset.objectIds);if(_){E.set("plan",_)}N.prev=16;L.setAttribute("disabled","disabled");L.classList.add("is-loading");N.next=21;return i(E);case 21:S=N.sent;N.next=24;return S.json();case 24:k=N.sent;j=k.status_code;q=k.data;O=k.message;T=O===void 0?e:O;if(!(j===200)){N.next=36;break}tutor_toast(t("Success","tutor"),T,"success");N.next=33;return u(x,null,null);case 33:if(!q.total_price&&q.order_type==="single_order"){l.classList.add("tutor-d-none");f.innerHTML=(P=n())===null||P===void 0?void 0:P.pay_now_btn_text;l.insertAdjacentHTML("beforeend","<input type='hidden' name='payment_method' value='free' id=\"tutor-temp-payment-method\"/>")}N.next=37;break;case 36:tutor_toast(t("Failed","tutor"),T,"error");case 37:N.next=42;break;case 39:N.prev=39;N.t0=N["catch"](16);tutor_toast(t("Failed","tutor"),e,"error");case 42:N.prev=42;L.removeAttribute("disabled");L.classList.remove("is-loading");return N.finish(42);case 46:if(!o.target.closest("#tutor-checkout-remove-coupon")){N.next=57;break}document.querySelector("input[name=coupon_code]").value="-1";document.querySelector("#tutor-checkout-remove-coupon").classList.add("is-loading");N.next=51;return u("-1",null,null);case 51:I=(A=document.querySelector("input[name=payment_method]:checked"))===null||A===void 0?void 0:A.value;if(I){a(I)}l.classList.remove("tutor-d-none");f.innerHTML=(C=n())===null||C===void 0?void 0:C.pay_now_btn_text;(z=document.getElementById("tutor-temp-payment-method"))===null||z===void 0||z.remove();s();case 57:case"end":return N.stop()}}),r,null,[[16,39,42,46]])})));return function(t){return r.apply(this,arguments)}}());s();document.addEventListener("keydown",(function(t){if(t.key==="Enter"&&t.target.closest("input[name=coupon_code]")){t.preventDefault();var e=t.target.parentNode.querySelector("#tutor-apply-coupon-button");e===null||e===void 0||e.click()}}));var p=document.getElementById("tutor-checkout-form");p===null||p===void 0||p.addEventListener("submit",(function(e){e.preventDefault();var r=n();var o=new FormData(e.target);if(r.payment_method_required&&!o.get("payment_method")){tutor_toast(t("Error","tutor"),t("Please select a payment method.","tutor"),"error");return}var i=document.getElementById("tutor-checkout-pay-now-button");i.classList.add("is-loading");i.textContent=t("Processing","tutor");i.setAttribute("disabled",true);this.submit()}));var v=document.querySelector("[name=billing_country]");var m=document.querySelector("[name=billing_state]");var y='<span class="tutor-btn is-loading tutor-checkout-spinner"></span>';var g=function t(e,r){if("show"===r){var n;e===null||e===void 0||e.setAttribute("disabled","disabled");e===null||e===void 0||(n=e.closest(".tutor-position-relative"))===null||n===void 0||n.insertAdjacentHTML("beforeend",y)}else{var o;e===null||e===void 0||e.removeAttribute("disabled");e===null||e===void 0||(o=e.closest(".tutor-position-relative"))===null||o===void 0||(o=o.querySelector(".tutor-checkout-spinner"))===null||o===void 0||o.remove()}};v===null||v===void 0||v.addEventListener("change",function(){var t=Ct(Pt().mark((function t(e){var r,n,o,i;return Pt().wrap((function t(a){while(1)switch(a.prev=a.next){case 0:r=document.querySelector("[name=coupon_code]");n=e.target.value;o=r!==null&&r!==void 0&&r.value?r.value:"";if(!n){a.next=12;break}g(e.target,"show");i=new FormData;i.set("billing_country",n);a.next=9;return c(i);case 9:a.next=11;return u(o,v.value,m.value);case 11:g(e.target,"hide");case 12:case"end":return a.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());m===null||m===void 0||m.addEventListener("change",function(){var t=Ct(Pt().mark((function t(e){var r,n,o,i,a;return Pt().wrap((function t(s){while(1)switch(s.prev=s.next){case 0:r=document.querySelector("[name=coupon_code]");n=v.value;o=e.target.value;i=r!==null&&r!==void 0&&r.value?r.value:"";if(!o){s.next=14;break}g(e.target,"show");a=new FormData;a.set("billing_country",n);a.set("billing_state",o);s.next=11;return c(a);case 11:s.next=13;return u(i,v.value,m.value);case 13:g(e.target,"hide");case 14:case"end":return s.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}));var zt=r(94004);var It=r(79988);function Nt(t){"@babel/helpers - typeof";return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nt(t)}function Dt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ft(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Dt(Object(r),!0).forEach((function(e){Gt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Gt(t,e,r){return(e=Mt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mt(t){var e=Bt(t,"string");return"symbol"==Nt(e)?e:e+""}function Bt(t,e){if("object"!=Nt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}readyState_complete((function(){Object.entries(document.getElementsByTagName("a")).forEach((function(t){var e=t[1].getAttribute("href");if(e!==null&&e!==void 0&&e.includes("/logout")||e!==null&&e!==void 0&&e.includes("logout")){t[1].setAttribute("data-no-instant","")}}))}));jQuery(document).ready((function(t){"use strict";var e=wp.i18n,r=e.__,n=e._x,o=e._n,i=e._nx;if(jQuery().select2){t(".tutor_select2").select2({escapeMarkup:function t(e){return e}})}
/*!
   * jQuery UI Touch Punch 0.2.3
   *
   * Copyright 2011–2014, Dave Furfero
   * Dual licensed under the MIT or GPL Version 2 licenses.
   *
   * Depends:
   *  jquery.ui.widget.js
   *  jquery.ui.mouse.js
   */!function(t){function e(t,e){if(!(t.originalEvent.touches.length>1)){t.preventDefault();var r=t.originalEvent.changedTouches[0],n=document.createEvent("MouseEvents");n.initMouseEvent(e,!0,!0,window,1,r.screenX,r.screenY,r.clientX,r.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(n)}}if(t.support.touch="ontouchend"in document,t.support.touch){var r,n=t.ui.mouse.prototype,o=n._mouseInit,i=n._mouseDestroy;n._touchStart=function(t){var n=this;!r&&n._mouseCapture(t.originalEvent.changedTouches[0])&&(r=!0,n._touchMoved=!1,e(t,"mouseover"),e(t,"mousemove"),e(t,"mousedown"))},n._touchMove=function(t){r&&(this._touchMoved=!0,e(t,"mousemove"))},n._touchEnd=function(t){r&&(e(t,"mouseup"),e(t,"mouseout"),this._touchMoved||e(t,"click"),r=!1)},n._mouseInit=function(){var e=this;e.element.bind({touchstart:t.proxy(e,"_touchStart"),touchmove:t.proxy(e,"_touchMove"),touchend:t.proxy(e,"_touchEnd")}),o.call(e)},n._mouseDestroy=function(){var e=this;e.element.unbind({touchstart:t.proxy(e,"_touchStart"),touchmove:t.proxy(e,"_touchMove"),touchend:t.proxy(e,"_touchEnd")}),i.call(e)}}}(jQuery);var a={ajaxurl:window._tutorobject.ajaxurl,nonce_key:window._tutorobject.nonce_key,played_once:false,max_seek_time:0,video_data:function e(){var r=t("#tutor_video_tracking_information").val();return r?JSON.parse(r):{}},track_player:function e(){var n=this;if(typeof Plyr!=="undefined"){var o;var i=n.video_data();var a=new Plyr(this.player_DOM,{keyboard:{focused:n.isRequiredPercentage()?false:true,global:false},listeners:Ft({},n.isRequiredPercentage()&&{seek:function t(e){var o=n.getTargetTime(a,e);var i=a.currentTime;var u=i>n.max_seek_time?i:n.max_seek_time;if(o>u){e.preventDefault();tutor_toast(r("Warning","tutor"),r("Forward seeking is disabled","tutor"),"error");return false}return true}})});a.on("ready",(function(t){var e=t.detail.plyr;var r=i||{},o=r.best_watch_time,u=o===void 0?0:o;if(_tutorobject.tutor_pro_url&&u>0){var c=Math.floor(u);var s=setTimeout((function(){if(a.playing!==true&&a.currentTime!==c){if(e.provider==="youtube"){e.embed.seekTo(u)}else{e.media.currentTime=c}}else{clearTimeout(s)}}))}n.sync_time(e)}));a.on("play",(function(e){n.played_once=true;var r=10;var i=e.detail.plyr;o=setInterval((function(){n.sync_time(i)}),r*1e3);if(_tutorobject.tutor_pro_url&&a.provider==="youtube"){t(".plyr--youtube.plyr__poster-enabled .plyr__poster").css("opacity",0)}}));a.on("pause",(function(t){clearInterval(o);var e=t.detail.plyr;n.sync_time(e)}));a.on("ended",(function(e){clearInterval(o);var r=n.video_data();var i=e.detail.plyr;var u={is_ended:true};n.sync_time(i,u);if(r.autoload_next_course_content&&n.played_once){n.autoload_content()}if(_tutorobject.tutor_pro_url&&a.provider==="youtube"){t(".plyr--youtube.plyr__poster-enabled .plyr__poster").css("opacity",1)}}))}},sync_time:function e(r,n){var o=this.video_data();if(!o){return}if(this.isRequiredPercentage()){this.enable_complete_lesson_btn(r)}var i={action:"sync_video_playback",currentTime:r.currentTime,duration:r.duration,post_id:o.post_id};i[this.nonce_key]=_tutorobject[this.nonce_key];var a=i;if(n){a=Object.assign(i,n)}t.post(this.ajaxurl,a);var u=o.best_watch_time>r.currentTime?o.best_watch_time:r.currentTime;if(u>this.max_seek_time){this.max_seek_time=u}},autoload_content:function e(){console.log("Autoloader called");var r=this.video_data().post_id;var n={action:"autoload_next_course_content",post_id:r};n[this.nonce_key]=_tutorobject[this.nonce_key];t.post(this.ajaxurl,n).done((function(t){console.log(t);if(t.success&&t.data.next_url){location.href=t.data.next_url}}))},isRequiredPercentage:function t(){var e=this.video_data();if(!e){return false}var r=e.strict_mode,n=e.control_video_lesson_completion,o=e.lesson_completed,i=e.is_enrolled;if(_tutorobject.tutor_pro_url&&i&&!o&&r&&n){return true}return false},enable_complete_lesson_btn:function e(r){var n=t('button[name="complete_lesson_btn"]');var o=this.video_data();var i=this.getPercentage(Number(r.currentTime),Number(r.duration));if(i>=o.required_percentage){n.attr("disabled",false);n.next().remove()}},disable_complete_lesson_btn:function e(){var n=this.video_data();if(!n){return}var o=n.best_watch_time,i=n.video_duration,a=n.required_percentage;var u=this.getPercentage(Number(o),Number(i));if(u<a){var c=t('button[name="complete_lesson_btn"]');c.attr("disabled",true);c.wrap('<div class="tooltip-wrap"></div>').after('<span class="tooltip-txt tooltip-bottom">'.concat(m(r("Watch at least %s% to complete the lesson.","tutor"),n.required_percentage),"</span>"))}},getPercentage:function t(e,r){if(e>0&&r>0){return Math.round(e/r*100)}return 0},getTargetTime:function t(e,r){if(Nt(r)==="object"&&(r.type==="input"||r.type==="change")){return r.target.value/r.target.max*e.media.duration}else{return Number(r)}},init:function t(e){this.player_DOM=e;this.track_player();if(this.isRequiredPercentage()){this.disable_complete_lesson_btn()}}};t(".tutorPlayer").each((function(){a.init(this)}));t(document).on("change keyup paste",".tutor_user_name",(function(){t(this).val(u(t(this).val()))}));function u(t){return t.toString().toLowerCase().replace(/\s+/g,"-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,"")}t(document).on("click",".tutor_question_cancel",(function(e){e.preventDefault();t(".tutor-add-question-wrap").toggle()}));t(".tooltip-btn").on("hover",(function(e){t(this).toggleClass("active")}));t(".tutor-course-title h4 .toggle-information-icon").on("click",(function(e){t(this).closest(".tutor-topics-in-single-lesson").find(".tutor-topics-summery").slideToggle();e.stopPropagation()}));t(".tutor-course-topic.tutor-active").find(".tutor-course-lessons").slideDown();t(".tutor-course-title").on("click",(function(){var e=t(this).siblings(".tutor-course-lessons");t(this).closest(".tutor-course-topic").toggleClass("tutor-active");e.slideToggle()}));t(document).on("click",".tutor-topics-title h3 .toggle-information-icon",(function(e){t(this).closest(".tutor-topics-in-single-lesson").find(".tutor-topics-summery").slideToggle();e.stopPropagation()}));t(document).on("click","[tutor-course-topics-sidebar-toggler]",(function(e){e.preventDefault();t(".tutor-course-single-content-wrapper").toggleClass("tutor-course-single-sidebar-hidden")}));t("[tutor-course-topics-sidebar-offcanvas-toggler]").on("click",(function(e){e.preventDefault();t(".tutor-course-single-content-wrapper").toggleClass("tutor-course-single-sidebar-open");t("body").toggleClass("tutor-overflow-hidden")}));t("[tutor-hide-course-single-sidebar]").on("click",(function(e){e.preventDefault();console.log("Hello");t(".tutor-course-single-content-wrapper").removeClass("tutor-course-single-sidebar-open");t("body").removeClass("tutor-overflow-hidden")}));t(".tutor-tabs-btn-group a").on("click touchstart",(function(e){e.preventDefault();var r=t(this);var n=r.attr("href");t(".tutor-lesson-sidebar-tab-item").hide();t(n).show();t(".tutor-tabs-btn-group a").removeClass("active");r.addClass("active")}));var c=t(".quiz-draggable-rand-answers").length;if(c){t(".quiz-draggable-rand-answers").each((function(){var e=t(this);var r=e.height();e.css({height:r})}))}if(jQuery.datepicker){t(".tutor_report_datepicker").datepicker({dateFormat:"yy-mm-dd"})}t(document).on("submit","#tutor-withdraw-account-set-form",(function(e){if(!e.detail||e.detail==1){e.preventDefault();var n=t(this);var o=n.find(".tutor_set_withdraw_account_btn");var i=n.serializeObject();o.prop("disabled",true);t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:i,beforeSend:function t(){o.addClass("is-loading")},success:function t(e){if(e.success){tutor_toast(r("Success!","tutor"),e.data.msg,"success")}},complete:function t(){o.removeClass("is-loading");setTimeout((function(){o.prop("disabled",false)}),2e3)}})}}));t(document).on("submit","#tutor-earning-withdraw-form",(function(e){e.preventDefault();var n=t(this);var o=t("#tutor-earning-withdraw-btn");var i=t(".tutor-withdraw-form-response");var a=n.serializeObject();t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:a,beforeSend:function t(){n.find(".tutor-success-msg").remove();o.attr("disabled","disabled").addClass("is-loading")},success:function e(n){var o;t(".tutor-earning-withdraw-form-wrap").hide();if(n.success){console.log(n.data.available_balance);if(n.data.available_balance!=="undefined"){t(".withdraw-balance-col .available_balance").html(n.data.available_balance)}tutor_toast(r("Request Successful","tutor"),r("Your request has been submitted. Please wait for the administrator's response.","tutor"),"success");setTimeout((function(){location.reload()}),500)}else{tutor_toast(r("Error","tutor"),n.data.msg,"error");o='<div class="tutor-error-msg inline-image-text is-inline-block">                            <img src="'+window._tutorobject.tutor_url+'assets/images/icon-cross.svg"/>                             <div>                                <b>Error</b><br/>                                <span>'+n.data.msg+"</span>                            </div>                        </div>";setTimeout((function(){i.html("")}),5e3);return false}},complete:function t(){o.removeAttr("disabled").removeClass("is-loading")}})}));t(document).on("click",".tutor-dashboard-element-delete-btn",(function(e){e.preventDefault();var r=t(this).attr("data-id");t("#tutor-dashboard-delete-element-id").val(r)}));t(document).on("submit","#tutor-dashboard-delete-element-form",(function(e){e.preventDefault();var r=t("#tutor-dashboard-delete-element-id").val();var n=t(".tutor-modal-element-delete-btn");var o=t(this).serializeObject();t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:o,beforeSend:function t(){n.addClass("is-loading")},success:function e(n){if(n.success){t("#tutor-dashboard-"+n.data.element+"-"+r).remove()}},complete:function t(){n.removeClass("is-loading")}})}));t(document).on("submit","#tutor_assignment_start_form",(function(e){e.preventDefault();var r=t(this);var n=r.serializeObject();n.action="tutor_start_assignment";t.ajax({url:_tutorobject.ajaxurl,type:"POST",data:n,beforeSend:function e(){t("#tutor_assignment_start_btn").addClass("is-loading")},success:function t(e){if(e.success){location.reload(true)}},error:function t(e,r,n){console.log("assignment start error: "+n)},complete:function e(){t("#tutor_assignment_start_btn").removeClass("is-loading")}})}));t(document).on("submit","#tutor_assignment_submit_form",(function(t){var e=tinymce.activeEditor.getContent();if(e.trim().length<1){t.preventDefault();tutor_toast(r("Warning","tutor"),r("Assignment answer is required.","tutor"),"error");setTimeout((function(){jQuery("button#tutor_assignment_submit_btn").removeClass("is-loading").removeAttr("disabled")}),500)}}));t("form").on("change",".tutor-assignment-file-upload",(function(){t(this).siblings("label").find("span").html(t(this).val().replace(/.*(\/|\\)/,""))}));if(t(".tutor-accordion-item-header.is-active").length===0){t(".tutor-accordion-item-header").first().trigger("click")}t(".tutor-course-builder-section-title").on("click",(function(){if(t(this).find("i").hasClass("tutor-icon-angle-up")){t(this).find("i").removeClass("tutor-icon-angle-up").addClass("tutor-icon-angle-down")}else{t(this).find("i").removeClass("tutor-icon-angle-down").addClass("tutor-icon-angle-up")}t(this).next("div").slideToggle()}));t(document).on("click","#tutor_profile_photo_button",(function(e){e.preventDefault();t("#tutor_profile_photo_file").trigger("click")}));t(document).on("change","#tutor_profile_photo_file",(function(e){e.preventDefault();var r=this;if(r.files&&r.files[0]){var n=new FileReader;n.onload=function(e){t(".tutor-profile-photo-upload-wrap").find("img").attr("src",e.target.result)};n.readAsDataURL(r.files[0])}}));t(document).on("click",".thread-content .subject",(function(e){var r=t(this);var n=parseInt(r.closest(".thread-content").attr("data-thread-id"));var o=_tutorobject.nonce_key;var i={thread_id:n,action:"tutor_bp_retrieve_user_records_for_thread"};i[o]=_tutorobject[o];t.ajax({type:"POST",url:window._tutorobject.ajaxurl,data:i,beforeSend:function e(){t("#tutor-bp-thread-wrap").html("")},success:function e(r){if(r.success){t("#tutor-bp-thread-wrap").html(r.data.thread_head_html);s()}}})}));function s(){t("ul.tutor-bp-enrolled-course-list").each((function(){var e=t(this);var r=e.find(" > li");var n=3;if(r.length>n){var o=r.length-n;r.each((function(e,r){var o=t(this);if(e>=n){o.hide()}}));var i='<a href="javascript:;" class="tutor_bp_plus_courses"><strong>+'+o+" More </strong></a> Courses";e.closest(".tutor-bp-enrolled-courses-wrap").find(".thread-participant-enrolled-info").html(i)}e.show()}))}s();t(document).on("click","a.tutor_bp_plus_courses",(function(e){e.preventDefault();var r=t(this);r.closest(".tutor-bp-enrolled-courses-wrap").find(".tutor-bp-enrolled-course-list li").show();r.closest(".thread-participant-enrolled-info").html("")}));t(".tutor-dropbtn").click((function(){var e=t(this).parent().find(".tutor-dropdown-content");e.slideToggle(100)}));t(document).on("click",(function(e){var r=t(".tutor-dropdown");var n=r.find(".tutor-dropdown-content");if(!r.is(e.target)&&r.has(e.target).length===0){n.slideUp(100)}}));var l=t('.tutor-frontend-builder-course-price [name="tutor_course_price_type"]');if(l.length==0){t("#_tutor_is_course_public_meta_checkbox").show()}else{l.change((function(){if(t(this).prop("checked")){var e=t(this).val()=="paid"?"hide":"show";t("#_tutor_is_course_public_meta_checkbox")[e]()}})).trigger("change")}(function(t){t.fn.tutor_tooltip=function(){this.on("mouseenter click",".tooltip",(function(e){e.stopPropagation();t(this).removeClass("isVisible")})).on("mouseenter focus",":has(>.tooltip)",(function(e){if(!t(this).prop("disabled")){t(this).find(".tooltip").addClass("isVisible")}})).on("mouseleave blur keydown",":has(>.tooltip)",(function(e){if(e.type==="keydown"){if(e.which===27){t(this).find(".tooltip").removeClass("isVisible")}}else{t(this).find(".tooltip").removeClass("isVisible")}}));return this}})(jQuery);jQuery(".tutor-tooltip-inside").tutor_tooltip();jQuery(".tutor-static-loader").click((function(){var t=this;setTimeout((function(){jQuery(t).addClass("is-loading").attr("disabled","disabled")}),100)}));var f=document.getElementById("tutor-reuseable-snackbar");if(f){setTimeout((function(){f.classList.add("tutor-snackbar-show")}),1e3)}jQuery('#tutor-registration-form [name="password_confirmation"]').on("input",(function(){var t=jQuery('[name="password"]');var e=(t.val()||"").trim();var r=e&&jQuery(this).val()===e;jQuery(this).parent().find(".tutor-validation-icon")[r?"show":"hide"]()}))}))})()})();
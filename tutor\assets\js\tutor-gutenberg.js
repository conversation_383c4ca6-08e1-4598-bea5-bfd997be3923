(()=>{"use strict";const t=wp.i18n;(function(n,e){var r="tutor-frontend-builder-trigger";var o='\n        <a id="'.concat(r,'" class="tutor-btn tutor-btn-primary tutor-btn-sm tutor-text-nowrap" href="').concat(tutorInlineData.frontend_dashboard_url,'" target="_blank">\n        ').concat((0,t.__)("Edit with Frontend Course Builder","tutor"),"\n        </a>\n    ");var a=document.getElementById("editor");if(!a){return}var i=e.data.subscribe((function(){setTimeout((function(){if(!document.getElementById(r)){var t=a.querySelector(".edit-post-header-toolbar");if(t instanceof HTMLElement){t.insertAdjacentHTML("beforeend",o)}}}),100)}))})(window,wp)})();
"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[4128],{1807:(l,C,e)=>{e.r(C);e.d(C,{default:()=>r});const r={icon:'<g clip-path="url(#clip0_36673_20063)"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 7.03184C3.5 5.08126 5.06634 3.5 6.99851 3.5H16.6079C18.5401 3.5 20.1064 5.08126 20.1064 7.03184V14.5915C20.1064 14.9498 19.8187 15.2402 19.4638 15.2402C19.1089 15.2402 18.8212 14.9498 18.8212 14.5915V7.03184C18.8212 5.7978 17.8303 4.79741 16.6079 4.79741H6.99851C5.77611 4.79741 4.78517 5.7978 4.78517 7.03184V16.7328C4.78517 17.9668 5.77611 18.9672 6.99851 18.9672H14.4935C14.8483 18.9672 15.136 19.2576 15.136 19.6159C15.136 19.9741 14.8483 20.2646 14.4935 20.2646H6.99851C5.06634 20.2646 3.5 18.6833 3.5 16.7328V7.03184Z" fill="currentColor"/><path d="M6.773 12.8695C6.6873 12.9591 6.6394 13.0788 6.6394 13.2033V16.4485C6.6394 16.8466 6.95906 17.1693 7.35339 17.1693H16.3972C16.7915 17.1693 17.1111 16.8466 17.1111 16.4485V13.2057C17.1111 13.0797 17.0621 12.9588 16.9747 12.869L14.3466 10.168C14.0823 9.89642 13.6477 9.89204 13.378 10.1583L10.0313 13.4617C9.94173 13.5501 9.79758 13.5491 9.70932 13.4594L8.42753 12.157C8.15737 11.8825 7.71416 11.8857 7.44792 12.164L6.773 12.8695Z" fill="currentColor"/><path d="M11.2645 8.08434C11.2645 8.93804 10.579 9.6301 9.73333 9.6301C8.88768 9.6301 8.20215 8.93804 8.20215 8.08434C8.20215 7.23064 8.88768 6.53857 9.73333 6.53857C10.579 6.53857 11.2645 7.23064 11.2645 8.08434Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M14 17.6487C14 17.2904 14.2877 17 14.6426 17L19.43 17C19.7849 17 20.0726 17.2904 20.0726 17.6487C20.0726 18.007 19.7849 18.2974 19.43 18.2974L14.6426 18.2974C14.2877 18.2974 14 18.007 14 17.6487Z" fill="currentColor"/></g><defs><clipPath id="clip0_36673_20063"><rect width="24" height="24" fill="currentColor"/></clipPath></defs>',viewBox:"0 0 24 24"}}}]);
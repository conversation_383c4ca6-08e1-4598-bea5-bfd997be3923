"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[3823],{44386:(l,a,o)=>{o.r(a);o.d(a,{default:()=>t});const t={icon:'<rect x="17" y="26.5" width="40" height="32" rx="3" fill="#D8D8D8"/><path d="M40.046 39.4 17 51.158V55.5a3 3 0 0 0 3 3h34a3 3 0 0 0 3-3v-4.342l-13.67-11.39a3 3 0 0 0-3.284-.368Z" fill="#fff" fill-opacity=".5"/><circle cx="48.5" cy="33" r="3.5" fill="#fff" fill-opacity=".5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20 19.5a1 1 0 0 0-.96.725l-1.085 3.795a5 5 0 0 1-3.434 3.435l-3.795 1.084a1 1 0 0 0 0 1.922l3.795 1.084a5 5 0 0 1 3.434 3.435l1.084 3.795a1 1 0 0 0 1.923 0l1.084-3.795a5 5 0 0 1 3.435-3.435l3.794-1.084a1 1 0 0 0 0-1.922l-3.794-1.084a5 5 0 0 1-3.435-3.435l-1.084-3.795a1 1 0 0 0-.961-.725Zm-12-4a1 1 0 0 0-.97.757l-.344 1.382a3.506 3.506 0 0 1-2.547 2.546l-1.38.344a1 1 0 0 0 0 1.942l1.38.344a3.506 3.506 0 0 1 2.547 2.546l.344 1.382a1 1 0 0 0 1.941 0l.344-1.382a3.5 3.5 0 0 1 2.547-2.546l1.381-.344a1 1 0 0 0 0-1.942l-1.38-.344a3.5 3.5 0 0 1-2.548-2.546l-.344-1.382a1 1 0 0 0-.97-.757Zm2 18a1 1 0 0 0-.949.684l-.525 1.577a2 2 0 0 1-1.264 1.264l-1.577.527a1 1 0 0 0 0 1.896l1.577.527a2 2 0 0 1 1.264 1.264l.527 1.577a1 1 0 0 0 1.896 0l.526-1.577a2 2 0 0 1 1.264-1.264l1.578-.527a1 1 0 0 0 0-1.896l-1.578-.527a2 2 0 0 1-1.264-1.264l-.526-1.577A1 1 0 0 0 10 33.5Z" fill="url(#a)"/><defs><linearGradient id="a" x1="27.665" y1="29.499" x2=".977" y2="21.385" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9645"/><stop offset=".153" stop-color="#FF6471"/><stop offset=".468" stop-color="#CF6EBD"/><stop offset=".671" stop-color="#A477D1"/><stop offset="1" stop-color="#3E64DE"/></linearGradient></defs>',viewBox:"0 0 72 72"}}}]);
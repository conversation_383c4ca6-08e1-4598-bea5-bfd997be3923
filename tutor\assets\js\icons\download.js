"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[6601],{57668:(a,c,l)=>{l.r(c);l.d(c,{default:()=>t});const t={icon:'<path d="M16.928 8.541c.426.048.84.175 1.22.375.36.203.684.465.96.774.273.31.49.667.64 1.053.162.397.248.822.252 1.25a3.522 3.522 0 0 1-2.125 3.252 3.712 3.712 0 0 1-1.36.281h-2.492a.509.509 0 0 1-.464-.697.496.496 0 0 1 .464-.304h2.499a2.503 2.503 0 0 0 2.499-2.515 2.502 2.502 0 0 0-2.5-2.5.531.531 0 0 1-.341-.124.486.486 0 0 1-.19-.32 3.778 3.778 0 0 0-.374-1.28 3.994 3.994 0 0 0-.764-1.078 3.905 3.905 0 0 0-1.079-.765 3.871 3.871 0 0 0-1.299-.377 3.743 3.743 0 0 0-1.577.124 3.955 3.955 0 0 0-2.358 1.865 3.76 3.76 0 0 0-.5 1.518.47.47 0 0 1-.156.32.5.5 0 0 1-.346.124 2.41 2.41 0 0 0-1.763.727 2.485 2.485 0 0 0-.026 3.542 2.437 2.437 0 0 0 1.773.739h2.515a.502.502 0 0 1 .464.304.47.47 0 0 1 .035.195.476.476 0 0 1-.147.352.484.484 0 0 1-.352.15H7.52a3.49 3.49 0 0 1-1.28-.236 3.52 3.52 0 0 1-1.827-1.638 3.309 3.309 0 0 1-.39-1.236 3.392 3.392 0 0 1 .124-1.392c.131-.43.34-.832.618-1.187.28-.356.628-.652 1.024-.873.407-.235.86-.379 1.328-.423.09-.492.258-.966.499-1.404.23-.427.52-.817.864-1.159a5.215 5.215 0 0 1 1.174-.873c.436-.23.903-.399 1.386-.5a4.706 4.706 0 0 1 1.983 0 4.982 4.982 0 0 1 3.136 2.093c.379.556.64 1.183.768 1.843Zm-3.139 8a.426.426 0 0 1 .352-.147c.132 0 .259.053.352.147.09.097.14.225.141.358a.494.494 0 0 1-.157.362l-2.112 2.124a.411.411 0 0 1-.156.093.419.419 0 0 1-.39 0l-.078-.038a.294.294 0 0 1-.06-.055l-2.144-2.124a.483.483 0 0 1-.135-.362.502.502 0 0 1 .148-.342.46.46 0 0 1 .361-.147.55.55 0 0 1 .359.147l1.28 1.28V11.51a.51.51 0 0 1 .499-.503.483.483 0 0 1 .352.15.477.477 0 0 1 .147.353v6.31l1.241-1.28Z" fill="currentColor"/>',viewBox:"0 0 24 24"}}}]);
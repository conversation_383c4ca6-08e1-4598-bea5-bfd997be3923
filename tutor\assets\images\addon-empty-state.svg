<svg width="783" height="229" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M0 229s351.583-192.8934 783-.908L0 229Z" fill="url(#a)"/>
  <path d="M267.323 158.534v19.052" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M276.607 148.912c-6.48-21.952-9.139-24.225-9.139-24.225s-2.031 2.418-9.09 24.225c-6.19 19.003 4.738 19.1 7.688 18.81-.049 0 16.827 2.514 10.541-18.81Z" fill="#E5E7EA"/>
  <path d="M477.901 144.367v19.728" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M487.476 134.406c-6.721-22.726-9.477-25.047-9.477-25.047s-2.08 2.515-9.429 25.047c-6.383 19.68 4.932 19.777 7.93 19.487.048 0 17.504 2.562 10.976-19.487Z" fill="#E5E7EA"/>
  <path d="M507.736 158.583v14.99" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="M515.038 151.04c-5.126-17.262-7.157-19.051-7.157-19.051s-1.595 1.885-7.156 19.051c-4.835 14.893 3.723 14.989 6.044 14.796-.048 0 13.201 1.934 8.269-14.796Z" fill="#E5E7EA"/>
  <path d="m332.358 42.873 95.402-8.4619s23.064 1.1122 25.53 29.4957c2.466 28.3834 4.787 44.9202 4.787 44.9202l-87.568 10.541-5.657-47.7731c-.725-6.1892-3.095-12.04-6.963-16.9237-4.739-5.9475-12.62-12.0883-25.531-11.7982Z" fill="#E3E5EA"/>
  <path d="m373.41 118.837-58.846 6.672-5.947-49.6103c-1.983-16.5368 10.009-31.4298 26.594-33.0738 16.054-1.5473 30.414 10.0092 32.3 26.0626l5.899 49.9495ZM384.483 117.192v17.456h15.57l-.193-18.858-15.377 1.402Z" fill="#C1C3CA"/>
  <path d="m313.935 126.041-7.495 24.854c-1.16 3.433-1.644 7.059-1.402 10.686.484 6.818 3.723 15.424 17.166 15.086 8.316-.242 15.376-3.336 20.985-7.06 7.833-5.222 13.878-12.668 17.649-21.275l13.056-28.915-59.959 6.624Z" fill="#D0D2D6"/>
  <path d="M401.31 133.826h-17.842v40.956h17.842v-40.956Z" fill="#CDCFD4"/>
  <path d="m362.772 56.9924-51.738 37.0388M363.933 57.7666l-49.175 67.0174M342.464 122.125l21.324-61.9407M332.842 78.5586s10.492-2.853 10.444 5.9474c0 0 10.106-2.2725 9.526 7.8333M318.916 88.7612s13.297-2.8527 13.539 11.0731c0 0 10.638-3.9167 14.748 7.3497" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="m260.167 21.2591-.773-.0484v1.2572l.773.0483v-1.2571ZM260.312 18.9868l-.774-.0483-.097 1.2571.774.0484.097-1.2572ZM260.409 16.7141l-.774-.0483-.048 1.2571.774.0484.048-1.2572ZM260.215 28.0288l-.774.0483c.049.4352.049.8704.097 1.2572l.774-.0484c-.097-.3868-.097-.8219-.097-1.2571ZM260.118 25.7561h-.773c0 .4352 0 .8704.048 1.2572h.774c0-.3868-.049-.822-.049-1.2572ZM260.118 23.532h-.773v1.2571h.773V23.532ZM261.037 34.7498l-.725.1933.29 1.2573.725-.1934-.29-1.2572ZM260.651 32.5256l-.774.0968c.048.4352.145.8703.193 1.2572l.774-.1451c-.097-.3385-.145-.7737-.193-1.2089ZM260.361 30.3015l-.774.0967c.048.4352.097.8704.145 1.2572l.774-.0968c-.049-.4351-.097-.8219-.145-1.2571ZM263.213 41.1323l-.677.3385c.194.3868.387.7737.58 1.1605l.677-.3868c-.241-.3385-.386-.7253-.58-1.1122ZM262.342 39.1016l-.725.2417.484 1.2089.677-.2902-.436-1.1604ZM261.617 36.9741l-.725.2418c.096.4352.241.822.386 1.2088l.726-.2417c-.145-.4352-.29-.8221-.387-1.2089ZM266.888 46.8381l-.628.4836.87.967.58-.5319-.822-.9187ZM265.486 45.0493l-.629.4352c.242.3384.532.6769.774 1.0154l.629-.4835c-.242-.2901-.532-.6286-.774-.9671ZM264.277 43.1633l-.677.3869c.193.3868.435.7253.677 1.1121l.628-.4351c-.193-.3385-.435-.7254-.628-1.0639ZM271.772 51.4802l-.484.6285c.339.2901.725.4836 1.064.7253l.435-.6285c-.338-.2417-.677-.4835-1.015-.7253ZM270.031 50.0298l-.532.5802c.338.2901.629.5802.967.822l.484-.5802c-.29-.2418-.629-.5319-.919-.822ZM268.387 48.4822l-.58.5319c.29.2901.58.6286.918.9187l.532-.5802c-.29-.2418-.58-.5803-.87-.8704ZM277.622 54.8164l-.29.7253c.387.1451.774.3385 1.209.4835l.29-.7253c-.435-.145-.822-.2901-1.209-.4835ZM275.592 53.8494l-.387.7253 1.161.5802.338-.7253-1.112-.5802ZM273.657 52.7373l-.435.677 1.112.6285.387-.6285-1.064-.677ZM284.102 56.7024l-.097.7737c.435.0483.87.1451 1.305.1451l.049-.7737c-.435-.0484-.871-.0968-1.257-.1451ZM281.877 56.2671l-.193.7737 1.305.2418.145-.7737-1.257-.2418ZM279.75 55.6384l-.242.7253c.387.1451.822.2418 1.209.3869l.193-.7253c-.387-.1451-.774-.2418-1.16-.3869ZM290.774 56.5089l.145.7737c.436-.0967.871-.1935 1.258-.2902l-.194-.7253c-.387.0483-.773.1451-1.209.2418ZM288.55 56.8475l.048.7737c.435-.0484.871-.0968 1.306-.1451l-.097-.7737c-.387.0483-.822.0967-1.257.1451ZM286.326 56.896v.7736c.435 0 .871.0483 1.306 0v-.7736c-.435.0483-.871 0-1.306 0ZM296.818 53.7527l.484.5802c.338-.2901.628-.6285.967-.9186l-.58-.532c-.29.2901-.581.5803-.871.8704ZM294.981 55.0101l.387.677c.387-.2418.726-.4836 1.112-.677l-.435-.6285c-.387.1934-.725.4351-1.064.6285ZM292.95 55.9288l.242.7253c.387-.145.822-.3384 1.209-.4835l-.339-.7253c-.338.1451-.725.3385-1.112.4835ZM299.865 48.0954l.773.0483c.049-.4352 0-.9187-.048-1.3539l-.774.0968c.097.3868.097.822.049 1.2088ZM299.382 50.2229l.725.2902c.194-.4352.29-.8705.387-1.3056l-.774-.145c-.048.3868-.193.7736-.338 1.1604ZM298.317 52.1087l.629.4352c.242-.3868.484-.7253.725-1.1121l-.677-.3869c-.241.3868-.435.7253-.677 1.0638ZM296.431 43.3086l.194-.7736c-.435-.0967-.87-.1934-1.354-.2418l-.048.7736c.435.0967.822.1451 1.208.2418ZM298.366 44.2752l.483-.5802c-.387-.2901-.774-.5319-1.209-.7253l-.338.7253c.387.1451.725.3385 1.064.5802ZM299.623 45.9679l.725-.2901c-.145-.4352-.387-.8703-.677-1.2088l-.58.4835c.193.2901.387.6286.532 1.0154ZM290.097 44.469l-.387-.677c-.193.0967-.387.2418-.532.3868-.193.1451-.386.2418-.532.3869l.484.6286c.145-.145.338-.2418.484-.3868.145-.1451.29-.2418.483-.3385ZM292.129 43.5019l-.242-.7253c-.242.0484-.435.1451-.629.2418-.193.0967-.386.1933-.628.29l.338.677c.194-.0967.387-.1934.581-.2417.193-.0967.338-.1934.58-.2418ZM294.256 43.115l-.049-.7737c-.241 0-.435.0484-.677.0484-.241 0-.435.0483-.676.0967l.145.7737c.193-.0484.386-.0968.58-.0968.193 0 .483-.0483.677-.0483ZM285.794 49.401l-.726-.2902c-.193.3869-.338.822-.483 1.2089l.725.2417c.194-.3868.339-.7736.484-1.1604ZM286.907 47.467l-.629-.4353c-.145.1935-.242.3869-.387.532-.096.1934-.242.3868-.338.5802l.677.3868c.096-.1934.193-.3385.338-.5319.097-.1934.194-.3384.339-.5318ZM288.357 45.8226l-.532-.5802c-.145.1451-.338.2902-.484.4836-.145.145-.29.3384-.435.4835l.58.4835c.146-.145.242-.3384.387-.4351.194-.1451.339-.2902.484-.4353Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="m284.682 55.9771-.774.0484c0 .4351.049.8703.097 1.3055l.774-.0967c-.049-.4352-.097-.8704-.097-1.2572ZM284.731 53.7043l-.774-.0966c-.048.4351-.048.8703-.097 1.3055h.774c.048-.3868.048-.7737.097-1.2089ZM285.069 51.5286l-.726-.1934c-.096.4352-.193.8703-.241 1.3055l.773.0967c.049-.3868.097-.822.194-1.2088ZM285.987 62.553l-.725.2417c.145.3869.29.8221.435 1.2089l.725-.2902-.435-1.1604ZM285.31 60.377l-.725.1933c.097.4352.242.8221.338 1.2573l.726-.2418c-.097-.3868-.242-.7737-.339-1.2088ZM284.875 58.2012l-.773.0967c.048.4351.145.8704.241 1.2572l.774-.1451c-.097-.3868-.193-.7736-.242-1.2088ZM289.034 68.5491l-.629.4352.726 1.0638.628-.4836-.725-1.0154ZM287.873 66.6147l-.677.3869.629 1.1122.677-.3869-.629-1.1122ZM286.858 64.6326l-.725.3384c.193.3868.338.7736.532 1.1605l.677-.3385-.484-1.1604ZM293.482 73.626l-.532.5803.967.8703.484-.5802-.919-.8704ZM291.887 72.0786l-.58.5319.87.9187.58-.5802-.87-.8704ZM290.388 70.3862l-.58.4836.822.967.58-.4835-.822-.9671ZM298.946 77.5911l-.386.6769 1.112.6286.387-.677-1.113-.6285ZM297.012 76.4304l-.435.6286 1.112.7253.387-.6769-1.064-.677ZM295.223 75.1248l-.484.5802 1.016.7737.435-.6286-.967-.7253ZM305.136 80.2019l-.194.7737 1.258.3385.145-.7737-1.209-.3385ZM303.008 79.5254l-.241.7253 1.208.4351.242-.7737-1.209-.3867ZM300.929 78.655l-.29.6769 1.16.532.29-.7253-1.16-.4836ZM267.371 8.05912s-.676 3.96498 3.095 4.59358c0 0 2.273-1.644 1.209-3.86828-1.016-2.17591-4.304-.7253-4.304-.7253Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M266.84 8.34878s-.387 4.11012 3.24 4.69042c0 0-1.548 1.7407-2.998.7253-1.354-.9187-2.321-2.6595-1.983-4.25516.097-.38683.29-.7737.629-1.01547.29-.09671.677-.24179 1.112-.14509Z" fill="#C1C3CA"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M264.277 9.79957c0-.09671-1.886 2.03093-1.451 3.57823 0 0-1.112.5802-1.064 1.2571 0 0 .822-.8219 1.548-.4351 0 0 1.45 1.644 3.723.4351.048-.0483-2.563-1.4989-2.756-4.83533ZM267.13 7.38179c-1.161-.91872-2.031-2.17586-2.563-3.57811-.145-.33848-.242-.67693-.242-1.06376-.048-.58024.145-1.16048.484-1.69237.241-.386826.483-.725371.918-.918785.822-.386828 1.886.145061 2.321.967065.436.82201.387 1.78913.242 2.70785-.145 1.06377-.435 2.17595-.822 3.19137-.097.19342-.193.38674-.338.38674-.145.04835-.29-.09666-.435-.19337-1.354-1.40225-3.192-2.17586-4.981-2.85281-.387-.14506-.822-.29021-1.209-.33857-.483 0-.918.14516-1.354.33857-.58.29012-1.112.72521-1.354 1.35381-.241.62859-.096 1.35394.387 1.74077.29.24177.677.29014 1.064.38685 2.563.43518 5.174.3385 7.688-.33845" fill="#C1C3CA"/>
  <path d="m372.83 118.643-58.218 6.625-5.899-49.1274c-1.982-16.3434 9.913-31.1397 26.304-32.687 15.909-1.5473 30.125 9.9125 31.962 25.7725l5.851 49.4169Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>
  <path d="m435.206 23.29 15.521 11.4114c.629.4352.435 1.4507-.338 1.6441l-6.238 1.4506.532 6.5277c.048.822-.87 1.3055-1.499.7736l-15.666-13.684 7.688-8.1234Z" fill="#1973AA"/>
  <path d="M421.425 56.9917c1.896 0 3.433-1.5587 3.433-3.4815 0-1.9227-1.537-3.4814-3.433-3.4814s-3.433 1.5587-3.433 3.4814c0 1.9228 1.537 3.4815 3.433 3.4815Z" fill="#949BA9"/>
  <path d="M422.682 51.7694c.58-2.9012 1.161-5.8024 1.741-8.7036.387-2.0308.822-4.0617 1.209-6.1409.193-.8703.677-2.1759.58-3.0463.048.6286-.774.9672.097.1935.483-.4352.967-.967 1.402-1.4506 1.45-1.4506 2.901-2.9496 4.303-4.4002 2.08-2.1275 4.207-4.3035 6.286-6.431 1.548-1.5473-.87-3.965-2.369-2.3693-2.224 2.2726-4.4 4.4969-6.624 6.7695-1.403 1.4506-2.805 2.9011-4.256 4.3034-.677.6769-1.644 1.4023-2.079 2.321-.387.8703-.435 2.0308-.628 2.9012-.387 1.9825-.774 4.0133-1.209 5.9958-.629 3.0462-1.209 6.1409-1.838 9.1871-.29 2.0792 2.998 2.998 3.385.8704Z" fill="#949BA9"/>
  <path d="M530.269 20.9209c2.805 0 2.805-4.3518 0-4.3518-2.804 0-2.804 4.3518 0 4.3518Z" fill="url(#b)"/>
  <defs>
    <linearGradient id="a" x1="384" y1="134" x2="380.501" y2="269.41" gradientUnits="userSpaceOnUse">
      <stop offset=".1653" stop-color="#fff"/>
      <stop offset=".618236" stop-color="#fff" stop-opacity="0"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="b" x1="530.241" y1="20.9073" x2="530.298" y2="16.5557" gradientUnits="userSpaceOnUse">
      <stop offset=".1653" stop-color="#E3E5EA"/>
      <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>
      <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>
      <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>
    </linearGradient>
  </defs>
</svg>

(()=>{document.addEventListener("DOMContentLoaded",(function(){var e=document.querySelector(".tutor-template-import-area");var t=document.querySelector(".tutor-template-preview-modal");var r=document.querySelector(".tutor-template-preview-modal-overlay");var a=document.querySelector(".tutor-template-preview-iframe-wrapper");var o=document.getElementById("tutor-template-preview-iframe");var l=document.querySelector(".tutor-template-preview-modal-back-link");var n=document.querySelectorAll(".tutor-template-preview-device-switcher li");var i=document.querySelector(".tutor-preview-template-name");var c=document.querySelector(".tutor-template-shimmer-effect");var d=document.querySelector(".tutor-template-import-btn");var s=document.getElementById("tutor_template_course_data_url");if(e){var u=function e(){t.style.display="none";o.src="";v(n);n[0].classList.add("active");c.style.display="none";document.body.style.overflow="visible";o.style.width="1400px"};var v=function e(t){t.forEach((function(e){e.classList.remove("active")}))};e.addEventListener("click",(function(e){if(e.target.closest(".tutor-template-preview-btn")){var r;document.body.style.overflow="hidden";c.style.display="block";t.style.display="flex";i.innerText=e.target.dataset.template_name;s.value=e.target.dataset.template_course_data_url;o.src=e.target.dataset.template_url;if((r=_tutorobject)!==null&&r!==void 0&&r.tutor_pro_url){d.setAttribute("data-import_template_id",e.target.dataset.template_id)}}}));o.addEventListener("load",(function(){c.style.display="none"}));l===null||l===void 0||l.addEventListener("click",(function(){u();var e=d.querySelector("i");d.classList.remove("is-loading");d.classList.add("tutor-template-import-btn");d.classList.remove("tutor-template-view-template-btn");e.classList.add("tutor-icon-import");e.classList.remove("tutor-icon-circle-mark");var t='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" role="presentation" aria-hidden="true" class="css-xron3k-svg-SVGIcon"><rect width="16" height="16" rx="8" fill="#E5803C"></rect><path d="M12.252 7.042c0 .004 0 .008-.003.012l-.862 3.951a.609.609 0 0 1-.598.495H5.213a.61.61 0 0 1-.598-.495l-.862-3.95c0-.005-.002-.009-.003-.013a.609.609 0 0 1 1.056-.51l1.28 1.38 1.362-3.054v-.004a.609.609 0 0 1 1.106.004l1.362 3.054 1.28-1.38a.609.609 0 0 1 1.055.51h.001Z" fill="#fff"></path></svg>';d.innerHTML="".concat(e.outerHTML," import ").concat(_tutorobject.tutor_pro_url?"":t)}));n.forEach((function(e){e.addEventListener("click",(function(){v(n);e.classList.add("active");var t=this.getAttribute("data-width");var r=this.getAttribute("data-height");var a=this.getAttribute("data-device");o.style.width=t;if("desktop"!==a){o.style.transform="scale(0.8085714286)";o.style.transformOrigin="center top"}else{o.style.transform="scale(0.8085714286)";o.style.transformOrigin="left top"}}))}))}}));(function(){var e=document.querySelector("#tutor-template-preview-iframe");document.addEventListener("DOMContentLoaded",(function(){e.addEventListener("load",(function(){window.addEventListener("message",t);var e=document.querySelector(".tutor-template-preview-import-area .tutor-template-shimmer-effect-2");e.style.display="none";o();l()}))}));var t=function e(t){var a=t.data.type;switch(a){case"RETURN_DROIP_VARIABLE_DATA":{var o;if(((o=t.data.droipCSSVariable)===null||o===void 0||(o=o.data)===null||o===void 0||(o=o[0])===null||o===void 0?void 0:o.modes.length)>1){var l=document.querySelector(".tutor-droip-color-presets-heading");l.style.display="block";var c=document.querySelector("#droip-color-modes");c.style.display="block";var d=t.data.droipCSSVariable.data[0];var s=d.modes;var u=document.getElementById("droip-color-presets");if(s.length<=1){u.style.display="none"}else{u.style.display="flex";u.style.justifyContent="center";u.style.alignItems="center"}var v=document.createElement("div");v.classList.add("all-colors-wrapper");v.style.display="flex";s.forEach((function(e,t){var r=i(e.key,d);var a=document.createElement("div");a.classList.add("color-palette");a.setAttribute("data-mode",e.key);r.forEach((function(e,t){if(t<3){var r='<div style="background-color:'.concat(e,';" data-index="').concat(t,'"></div>');a.innerHTML+=r}}));v.append(a)}));document.querySelector("#droip-color-modes").innerHTML=v.outerHTML;document.querySelectorAll(".color-palette").forEach((function(e){e.addEventListener("click",(function(){var t=e.getAttribute("data-mode");n(t);r(t)}))}))}break}case"RETURN_DROIP_ACTIVE_MODE":{var m;var p=(t===null||t===void 0||(m=t.data)===null||m===void 0?void 0:m.activeMode)||"default";r(p);break}}};var r=function e(t){var r;var a=document.querySelectorAll(".color-palette");if(!a)return;a.forEach((function(e){e.classList.remove("active")}));(r=document.querySelector('[data-mode="'+t+'"]'))===null||r===void 0||(r=r.classList)===null||r===void 0||r.add("active")};var a=function t(r){e.contentWindow.postMessage(r,"*")};var o=function e(){a({type:"GET_DROIP_VARIABLE_DATA"})};var l=function e(){a({type:"GET_DROIP_ACTIVE_MODE"})};var n=function e(t){a({type:"APPLY_DROIP_VARIABLE_PALETTE",mode:t})};var i=function e(t,r){if(!r||!r.variables){return[]}var a=r.variables.filter((function(e){return e.type==="color"})).map((function(e){var r,a;if((r=e.value)!==null&&r!==void 0&&r[t]){return e.value[t]}if((a=e.value)!==null&&a!==void 0&&a["default"]){return e.value["default"]}return"#000000"}));return a}})()})();